<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class WilayahController extends Controller
{
    /**
     * Display a listing of wilayah.
     */
    public function index(Request $request)
    {
        $query = Wilayah::with(['parent'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('nama_wilayah', 'like', "%{$search}%")
                      ->orWhere('kode_wilayah', 'like', "%{$search}%");
                });
            })
            ->when($request->level, function ($query, $level) {
                $query->where('level_wilayah', $level);
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            });

        $wilayah = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/Wilayah/Index', [
            'wilayah' => $wilayah,
            'filters' => $request->only(['search', 'level', 'status']),
            'levels' => [
                'provinsi' => 'Provinsi',
                'kabupaten' => 'Kabupaten',
                'kota' => 'Kota'
            ]
        ]);
    }

    /**
     * Show the form for creating a new wilayah.
     */
    public function create()
    {
        return Inertia::render('Admin/Wilayah/Create', [
            'parentWilayah' => Wilayah::where('level_wilayah', 'provinsi')->aktif()->get(),
            'levels' => [
                'provinsi' => 'Provinsi',
                'kabupaten' => 'Kabupaten',
                'kota' => 'Kota'
            ]
        ]);
    }

    /**
     * Store a newly created wilayah in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'kode_wilayah' => 'required|string|max:10|unique:wilayah',
            'nama_wilayah' => 'required|string|max:100',
            'level_wilayah' => ['required', Rule::in(['provinsi', 'kabupaten', 'kota'])],
            'parent_id' => 'nullable|exists:wilayah,id_wilayah',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        Wilayah::create($validated);

        return redirect()->route('admin.wilayah.index')
            ->with('success', 'Wilayah berhasil dibuat.');
    }

    /**
     * Display the specified wilayah.
     */
    public function show(Wilayah $wilayah)
    {
        $wilayah->load(['parent', 'children', 'users', 'peserta']);

        return Inertia::render('Admin/Wilayah/Show', [
            'wilayah' => $wilayah
        ]);
    }

    /**
     * Show the form for editing the specified wilayah.
     */
    public function edit(Wilayah $wilayah)
    {
        return Inertia::render('Admin/Wilayah/Edit', [
            'wilayah' => $wilayah,
            'parentWilayah' => Wilayah::where('level_wilayah', 'provinsi')
                ->where('id_wilayah', '!=', $wilayah->id_wilayah)
                ->aktif()
                ->get(),
            'levels' => [
                'provinsi' => 'Provinsi',
                'kabupaten' => 'Kabupaten',
                'kota' => 'Kota'
            ]
        ]);
    }

    /**
     * Update the specified wilayah in storage.
     */
    public function update(Request $request, Wilayah $wilayah)
    {
        $validated = $request->validate([
            'kode_wilayah' => ['required', 'string', 'max:10', Rule::unique('wilayah')->ignore($wilayah->id_wilayah, 'id_wilayah')],
            'nama_wilayah' => 'required|string|max:100',
            'level_wilayah' => ['required', Rule::in(['provinsi', 'kabupaten', 'kota'])],
            'parent_id' => 'nullable|exists:wilayah,id_wilayah',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        $wilayah->update($validated);

        return redirect()->route('admin.wilayah.index')
            ->with('success', 'Wilayah berhasil diperbarui.');
    }

    /**
     * Remove the specified wilayah from storage.
     */
    public function destroy(Wilayah $wilayah)
    {
        // Check if wilayah has children or users
        if ($wilayah->children()->count() > 0) {
            return back()->with('error', 'Wilayah tidak dapat dihapus karena memiliki wilayah anak.');
        }

        if ($wilayah->users()->count() > 0) {
            return back()->with('error', 'Wilayah tidak dapat dihapus karena memiliki pengguna.');
        }

        $wilayah->delete();

        return redirect()->route('admin.wilayah.index')
            ->with('success', 'Wilayah berhasil dihapus.');
    }

    /**
     * Get children of specified wilayah.
     */
    public function children(Wilayah $wilayah)
    {
        $children = $wilayah->children()->aktif()->get();

        return response()->json($children);
    }
}

{"version": 3, "file": "getGroupedRowModel.js", "sources": ["../../../src/utils/getGroupedRowModel.ts"], "sourcesContent": ["import { createRow } from '../core/row'\nimport { Row, RowData, RowModel, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { GroupingState } from '../features/ColumnGrouping'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          rowModel.rows.forEach(row => {\n            row.depth = 0\n            row.parentId = undefined\n          })\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Perform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              subRows.forEach(subRow => {\n                subRow.parentId = id\n              })\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n        table._queue(() => {\n          table._autoResetExpanded()\n          table._autoResetPageIndex()\n        })\n      })\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n"], "names": ["getGroupedRowModel", "table", "memo", "getState", "grouping", "getPreGroupedRowModel", "rowModel", "rows", "length", "for<PERSON>ach", "row", "depth", "parentId", "undefined", "existingGrouping", "filter", "columnId", "getColumn", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "map", "push", "id", "subRows", "rowGroupsMap", "groupBy", "aggregatedGroupedRows", "Array", "from", "entries", "_ref", "index", "groupingValue", "groupedRows", "subRow", "leafRows", "flattenBy", "createRow", "original", "Object", "assign", "groupingColumnId", "getValue", "includes", "_valuesCache", "hasOwnProperty", "_groupedRows$0$getVal", "_groupingValuesCache", "column", "aggregateFn", "getAggregationFn", "flatRows", "rowsById", "getMemoOptions", "options", "_queue", "_autoResetExpanded", "_autoResetPageIndex", "groupMap", "Map", "reduce", "res<PERSON>ey", "getGroupingValue", "previous", "get", "set"], "mappings": ";;;;;;;;;;;;;;;AAKO,SAASA,kBAAkBA,GAEP;EACzB,OAAOC,KAAK,IACVC,UAAI,CACF,MAAM,CAACD,KAAK,CAACE,QAAQ,EAAE,CAACC,QAAQ,EAAEH,KAAK,CAACI,qBAAqB,EAAE,CAAC,EAChE,CAACD,QAAQ,EAAEE,QAAQ,KAAK;IACtB,IAAI,CAACA,QAAQ,CAACC,IAAI,CAACC,MAAM,IAAI,CAACJ,QAAQ,CAACI,MAAM,EAAE;AAC7CF,MAAAA,QAAQ,CAACC,IAAI,CAACE,OAAO,CAACC,GAAG,IAAI;QAC3BA,GAAG,CAACC,KAAK,GAAG,CAAC,CAAA;QACbD,GAAG,CAACE,QAAQ,GAAGC,SAAS,CAAA;AAC1B,OAAC,CAAC,CAAA;AACF,MAAA,OAAOP,QAAQ,CAAA;AACjB,KAAA;;AAEA;AACA,IAAA,MAAMQ,gBAAgB,GAAGV,QAAQ,CAACW,MAAM,CAACC,QAAQ,IAC/Cf,KAAK,CAACgB,SAAS,CAACD,QAAQ,CAC1B,CAAC,CAAA;IAED,MAAME,eAA6B,GAAG,EAAE,CAAA;IACxC,MAAMC,eAA2C,GAAG,EAAE,CAAA;AACtD;AACA;AACA;AACA;;AAEA;IACA,MAAMC,kBAAkB,GAAG,UACzBb,IAAkB,EAClBI,KAAK,EACLC,QAAiB,EACd;AAAA,MAAA,IAFHD,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,QAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,OAAA;AAGT;AACA;AACA,MAAA,IAAIA,KAAK,IAAIG,gBAAgB,CAACN,MAAM,EAAE;AACpC,QAAA,OAAOD,IAAI,CAACc,GAAG,CAACX,GAAG,IAAI;UACrBA,GAAG,CAACC,KAAK,GAAGA,KAAK,CAAA;AAEjBO,UAAAA,eAAe,CAACI,IAAI,CAACZ,GAAG,CAAC,CAAA;AACzBS,UAAAA,eAAe,CAACT,GAAG,CAACa,EAAE,CAAC,GAAGb,GAAG,CAAA;UAE7B,IAAIA,GAAG,CAACc,OAAO,EAAE;AACfd,YAAAA,GAAG,CAACc,OAAO,GAAGJ,kBAAkB,CAACV,GAAG,CAACc,OAAO,EAAEb,KAAK,GAAG,CAAC,EAAED,GAAG,CAACa,EAAE,CAAC,CAAA;AAClE,WAAA;AAEA,UAAA,OAAOb,GAAG,CAAA;AACZ,SAAC,CAAC,CAAA;AACJ,OAAA;AAEA,MAAA,MAAMM,QAAgB,GAAGF,gBAAgB,CAACH,KAAK,CAAE,CAAA;;AAEjD;AACA,MAAA,MAAMc,YAAY,GAAGC,OAAO,CAACnB,IAAI,EAAES,QAAQ,CAAC,CAAA;;AAE5C;AACA,MAAA,MAAMW,qBAAqB,GAAGC,KAAK,CAACC,IAAI,CAACJ,YAAY,CAACK,OAAO,EAAE,CAAC,CAACT,GAAG,CAClE,CAAAU,IAAA,EAA+BC,KAAK,KAAK;AAAA,QAAA,IAAxC,CAACC,aAAa,EAAEC,WAAW,CAAC,GAAAH,IAAA,CAAA;AAC3B,QAAA,IAAIR,EAAE,GAAG,CAAA,EAAGP,QAAQ,CAAA,CAAA,EAAIiB,aAAa,CAAE,CAAA,CAAA;QACvCV,EAAE,GAAGX,QAAQ,GAAG,CAAA,EAAGA,QAAQ,CAAIW,CAAAA,EAAAA,EAAE,CAAE,CAAA,GAAGA,EAAE,CAAA;;AAExC;QACA,MAAMC,OAAO,GAAGJ,kBAAkB,CAACc,WAAW,EAAEvB,KAAK,GAAG,CAAC,EAAEY,EAAE,CAAC,CAAA;AAE9DC,QAAAA,OAAO,CAACf,OAAO,CAAC0B,MAAM,IAAI;UACxBA,MAAM,CAACvB,QAAQ,GAAGW,EAAE,CAAA;AACtB,SAAC,CAAC,CAAA;;AAEF;AACA,QAAA,MAAMa,QAAQ,GAAGzB,KAAK,GAClB0B,eAAS,CAACH,WAAW,EAAExB,GAAG,IAAIA,GAAG,CAACc,OAAO,CAAC,GAC1CU,WAAW,CAAA;QAEf,MAAMxB,KAAG,GAAG4B,aAAS,CACnBrC,KAAK,EACLsB,EAAE,EACFa,QAAQ,CAAC,CAAC,CAAC,CAAEG,QAAQ,EACrBP,KAAK,EACLrB,KAAK,EACLE,SAAS,EACTD,QACF,CAAC,CAAA;AAED4B,QAAAA,MAAM,CAACC,MAAM,CAAC/B,KAAG,EAAE;AACjBgC,UAAAA,gBAAgB,EAAE1B,QAAQ;UAC1BiB,aAAa;UACbT,OAAO;UACPY,QAAQ;UACRO,QAAQ,EAAG3B,QAAgB,IAAK;AAC9B;AACA,YAAA,IAAIF,gBAAgB,CAAC8B,QAAQ,CAAC5B,QAAQ,CAAC,EAAE;cACvC,IAAIN,KAAG,CAACmC,YAAY,CAACC,cAAc,CAAC9B,QAAQ,CAAC,EAAE;AAC7C,gBAAA,OAAON,KAAG,CAACmC,YAAY,CAAC7B,QAAQ,CAAC,CAAA;AACnC,eAAA;AAEA,cAAA,IAAIkB,WAAW,CAAC,CAAC,CAAC,EAAE;AAAA,gBAAA,IAAAa,qBAAA,CAAA;gBAClBrC,KAAG,CAACmC,YAAY,CAAC7B,QAAQ,CAAC,GAAA+B,CAAAA,qBAAA,GACxBb,WAAW,CAAC,CAAC,CAAC,CAACS,QAAQ,CAAC3B,QAAQ,CAAC,KAAA+B,IAAAA,GAAAA,qBAAA,GAAIlC,SAAS,CAAA;AAClD,eAAA;AAEA,cAAA,OAAOH,KAAG,CAACmC,YAAY,CAAC7B,QAAQ,CAAC,CAAA;AACnC,aAAA;YAEA,IAAIN,KAAG,CAACsC,oBAAoB,CAACF,cAAc,CAAC9B,QAAQ,CAAC,EAAE;AACrD,cAAA,OAAON,KAAG,CAACsC,oBAAoB,CAAChC,QAAQ,CAAC,CAAA;AAC3C,aAAA;;AAEA;AACA,YAAA,MAAMiC,MAAM,GAAGhD,KAAK,CAACgB,SAAS,CAACD,QAAQ,CAAC,CAAA;YACxC,MAAMkC,WAAW,GAAGD,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAEE,gBAAgB,EAAE,CAAA;AAE9C,YAAA,IAAID,WAAW,EAAE;AACfxC,cAAAA,KAAG,CAACsC,oBAAoB,CAAChC,QAAQ,CAAC,GAAGkC,WAAW,CAC9ClC,QAAQ,EACRoB,QAAQ,EACRF,WACF,CAAC,CAAA;AAED,cAAA,OAAOxB,KAAG,CAACsC,oBAAoB,CAAChC,QAAQ,CAAC,CAAA;AAC3C,aAAA;AACF,WAAA;AACF,SAAC,CAAC,CAAA;AAEFQ,QAAAA,OAAO,CAACf,OAAO,CAAC0B,MAAM,IAAI;AACxBjB,UAAAA,eAAe,CAACI,IAAI,CAACa,MAAM,CAAC,CAAA;AAC5BhB,UAAAA,eAAe,CAACgB,MAAM,CAACZ,EAAE,CAAC,GAAGY,MAAM,CAAA;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACF,SAAC,CAAC,CAAA;AAEF,QAAA,OAAOzB,KAAG,CAAA;AACZ,OACF,CAAC,CAAA;AAED,MAAA,OAAOiB,qBAAqB,CAAA;KAC7B,CAAA;IAED,MAAMO,WAAW,GAAGd,kBAAkB,CAACd,QAAQ,CAACC,IAAI,EAAE,CAAC,CAAC,CAAA;AAExD2B,IAAAA,WAAW,CAACzB,OAAO,CAAC0B,MAAM,IAAI;AAC5BjB,MAAAA,eAAe,CAACI,IAAI,CAACa,MAAM,CAAC,CAAA;AAC5BhB,MAAAA,eAAe,CAACgB,MAAM,CAACZ,EAAE,CAAC,GAAGY,MAAM,CAAA;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACF,KAAC,CAAC,CAAA;IAEF,OAAO;AACL5B,MAAAA,IAAI,EAAE2B,WAAW;AACjBkB,MAAAA,QAAQ,EAAElC,eAAe;AACzBmC,MAAAA,QAAQ,EAAElC,eAAAA;KACX,CAAA;GACF,EACDmC,oBAAc,CAACrD,KAAK,CAACsD,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,MAAM;IACtEtD,KAAK,CAACuD,MAAM,CAAC,MAAM;MACjBvD,KAAK,CAACwD,kBAAkB,EAAE,CAAA;MAC1BxD,KAAK,CAACyD,mBAAmB,EAAE,CAAA;AAC7B,KAAC,CAAC,CAAA;AACJ,GAAC,CACH,CAAC,CAAA;AACL,CAAA;AAEA,SAAShC,OAAOA,CAAwBnB,IAAkB,EAAES,QAAgB,EAAE;AAC5E,EAAA,MAAM2C,QAAQ,GAAG,IAAIC,GAAG,EAAqB,CAAA;EAE7C,OAAOrD,IAAI,CAACsD,MAAM,CAAC,CAACxC,GAAG,EAAEX,GAAG,KAAK;IAC/B,MAAMoD,MAAM,GAAG,CAAGpD,EAAAA,GAAG,CAACqD,gBAAgB,CAAC/C,QAAQ,CAAC,CAAE,CAAA,CAAA;AAClD,IAAA,MAAMgD,QAAQ,GAAG3C,GAAG,CAAC4C,GAAG,CAACH,MAAM,CAAC,CAAA;IAChC,IAAI,CAACE,QAAQ,EAAE;MACb3C,GAAG,CAAC6C,GAAG,CAACJ,MAAM,EAAE,CAACpD,GAAG,CAAC,CAAC,CAAA;AACxB,KAAC,MAAM;AACLsD,MAAAA,QAAQ,CAAC1C,IAAI,CAACZ,GAAG,CAAC,CAAA;AACpB,KAAA;AACA,IAAA,OAAOW,GAAG,CAAA;GACX,EAAEsC,QAAQ,CAAC,CAAA;AACd;;;;"}
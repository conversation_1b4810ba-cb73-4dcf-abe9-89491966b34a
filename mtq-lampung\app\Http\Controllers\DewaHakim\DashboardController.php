<?php

namespace App\Http\Controllers\DewaHakim;

use App\Http\Controllers\Controller;
use App\Models\DewaHakim;
use App\Models\NilaiPeserta;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the dewan hakim dashboard.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return redirect()->route('dashboard')
                ->with('error', 'Profil dewan hakim belum lengkap. Silakan hubungi administrator.');
        }

        // Get statistics
        $stats = [
            'total_penilaian' => NilaiPeserta::where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)->count(),
            'peserta_dinilai' => NilaiPeserta::where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
                ->distinct('id_pendaftaran')
                ->count(),
            'pending_penilaian' => $this->getPendingAssignments($dewaHakim),
            'completed_today' => NilaiPeserta::where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
                ->whereDate('created_at', today())
                ->count()
        ];

        // Get recent scoring activities
        $recentScoring = NilaiPeserta::with([
                'pendaftaran.peserta',
                'pendaftaran.golongan.cabangLomba',
                'jenisNilai'
            ])
            ->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
            ->latest()
            ->limit(10)
            ->get();

        // Get upcoming assignments (placeholder - would need assignment system)
        $upcomingAssignments = collect(); // TODO: Implement assignment system

        return Inertia::render('DewaHakim/Dashboard', [
            'dewaHakim' => $dewaHakim->load(['wilayah', 'pendidikan', 'pengalaman', 'prestasi']),
            'stats' => $stats,
            'recentScoring' => $recentScoring,
            'upcomingAssignments' => $upcomingAssignments
        ]);
    }

    /**
     * Get pending assignments count for dewan hakim.
     */
    private function getPendingAssignments(DewaHakim $dewaHakim): int
    {
        // TODO: Implement proper assignment system
        // For now, return count of participants that haven't been scored by this judge
        return Pendaftaran::whereIn('status_pendaftaran', ['approved', 'verified'])
            ->whereDoesntHave('nilaiPeserta', function ($query) use ($dewaHakim) {
                $query->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
            })
            ->count();
    }
}

{"version": 3, "file": "merge-proxy.js", "sources": ["../../src/merge-proxy.ts"], "sourcesContent": ["function trueFn() {\n  return true\n}\n\nconst $PROXY = Symbol('merge-proxy')\n\n// https://github.com/solidjs/solid/blob/c20ca4fd8c36bc0522fedb2c7f38a110b7ee2663/packages/solid/src/render/component.ts#L51-L118\nconst propTraps: ProxyHandler<{\n  get: (k: string | number | symbol) => any\n  has: (k: string | number | symbol) => boolean\n  keys: () => string[]\n}> = {\n  get(_, property, receiver) {\n    if (property === $PROXY) return receiver\n    return _.get(property)\n  },\n  has(_, property) {\n    return _.has(property)\n  },\n  set: trueFn,\n  deleteProperty: trueFn,\n  getOwnPropertyDescriptor(_, property) {\n    return {\n      configurable: true,\n      enumerable: true,\n      get() {\n        return _.get(property)\n      },\n      set: trueFn,\n      deleteProperty: trueFn,\n    }\n  },\n  ownKeys(_) {\n    return _.keys()\n  },\n}\n\ntype UnboxLazy<T> = T extends () => infer U ? U : T\ntype BoxedTupleTypes<T extends any[]> = {\n  [P in keyof T]: [UnboxLazy<T[P]>]\n}[Exclude<keyof T, keyof any[]>]\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (\n  k: infer I\n) => void\n  ? I\n  : never\ntype UnboxIntersection<T> = T extends { 0: infer U } ? U : never\ntype MergeProxy<T extends any[]> = UnboxIntersection<\n  UnionToIntersection<BoxedTupleTypes<T>>\n>\n\nfunction resolveSource(s: any) {\n  return 'value' in s ? s.value : s\n}\n\nexport function mergeProxy<T extends any[]>(...sources: T): MergeProxy<T>\nexport function mergeProxy(...sources: any): any {\n  return new Proxy(\n    {\n      get(property: string | number | symbol) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          const v = resolveSource(sources[i])[property]\n          if (v !== undefined) return v\n        }\n      },\n      has(property: string | number | symbol) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          if (property in resolveSource(sources[i])) return true\n        }\n        return false\n      },\n      keys() {\n        const keys = []\n        for (let i = 0; i < sources.length; i++)\n          keys.push(...Object.keys(resolveSource(sources[i])))\n        return [...Array.from(new Set(keys))]\n      },\n    },\n    propTraps\n  )\n}\n"], "names": ["trueFn", "$PROXY", "Symbol", "propTraps", "get", "_", "property", "receiver", "has", "set", "deleteProperty", "getOwnPropertyDescriptor", "configurable", "enumerable", "ownKeys", "keys", "resolveSource", "s", "value", "mergeProxy", "_len", "arguments", "length", "sources", "Array", "_key", "Proxy", "i", "v", "undefined", "push", "Object", "from", "Set"], "mappings": ";;;;;;;;;;;;AAAA,SAASA,MAAMA,GAAG;AAChB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA,MAAMC,MAAM,GAAGC,MAAM,CAAC,aAAa,CAAC,CAAA;;AAEpC;AACA,MAAMC,SAIJ,GAAG;AACHC,EAAAA,GAAGA,CAACC,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;AACzB,IAAA,IAAID,QAAQ,KAAKL,MAAM,EAAE,OAAOM,QAAQ,CAAA;AACxC,IAAA,OAAOF,CAAC,CAACD,GAAG,CAACE,QAAQ,CAAC,CAAA;GACvB;AACDE,EAAAA,GAAGA,CAACH,CAAC,EAAEC,QAAQ,EAAE;AACf,IAAA,OAAOD,CAAC,CAACG,GAAG,CAACF,QAAQ,CAAC,CAAA;GACvB;AACDG,EAAAA,GAAG,EAAET,MAAM;AACXU,EAAAA,cAAc,EAAEV,MAAM;AACtBW,EAAAA,wBAAwBA,CAACN,CAAC,EAAEC,QAAQ,EAAE;IACpC,OAAO;AACLM,MAAAA,YAAY,EAAE,IAAI;AAClBC,MAAAA,UAAU,EAAE,IAAI;AAChBT,MAAAA,GAAGA,GAAG;AACJ,QAAA,OAAOC,CAAC,CAACD,GAAG,CAACE,QAAQ,CAAC,CAAA;OACvB;AACDG,MAAAA,GAAG,EAAET,MAAM;AACXU,MAAAA,cAAc,EAAEV,MAAAA;KACjB,CAAA;GACF;EACDc,OAAOA,CAACT,CAAC,EAAE;AACT,IAAA,OAAOA,CAAC,CAACU,IAAI,EAAE,CAAA;AACjB,GAAA;AACF,CAAC,CAAA;AAgBD,SAASC,aAAaA,CAACC,CAAM,EAAE;EAC7B,OAAO,OAAO,IAAIA,CAAC,GAAGA,CAAC,CAACC,KAAK,GAAGD,CAAC,CAAA;AACnC,CAAA;AAGO,SAASE,UAAUA,GAAuB;AAAA,EAAA,KAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAnBC,OAAO,GAAAC,IAAAA,KAAA,CAAAJ,IAAA,GAAAK,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA,EAAA,EAAA;AAAPF,IAAAA,OAAO,CAAAE,IAAA,CAAAJ,GAAAA,SAAA,CAAAI,IAAA,CAAA,CAAA;AAAA,GAAA;EACnC,OAAO,IAAIC,KAAK,CACd;IACEtB,GAAGA,CAACE,QAAkC,EAAE;AACtC,MAAA,KAAK,IAAIqB,CAAC,GAAGJ,OAAO,CAACD,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5C,MAAMC,CAAC,GAAGZ,aAAa,CAACO,OAAO,CAACI,CAAC,CAAC,CAAC,CAACrB,QAAQ,CAAC,CAAA;AAC7C,QAAA,IAAIsB,CAAC,KAAKC,SAAS,EAAE,OAAOD,CAAC,CAAA;AAC/B,OAAA;KACD;IACDpB,GAAGA,CAACF,QAAkC,EAAE;AACtC,MAAA,KAAK,IAAIqB,CAAC,GAAGJ,OAAO,CAACD,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5C,IAAIrB,QAAQ,IAAIU,aAAa,CAACO,OAAO,CAACI,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAA;AACxD,OAAA;AACA,MAAA,OAAO,KAAK,CAAA;KACb;AACDZ,IAAAA,IAAIA,GAAG;MACL,MAAMA,IAAI,GAAG,EAAE,CAAA;AACf,MAAA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACD,MAAM,EAAEK,CAAC,EAAE,EACrCZ,IAAI,CAACe,IAAI,CAAC,GAAGC,MAAM,CAAChB,IAAI,CAACC,aAAa,CAACO,OAAO,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACtD,MAAA,OAAO,CAAC,GAAGH,KAAK,CAACQ,IAAI,CAAC,IAAIC,GAAG,CAAClB,IAAI,CAAC,CAAC,CAAC,CAAA;AACvC,KAAA;GACD,EACDZ,SACF,CAAC,CAAA;AACH;;;;"}
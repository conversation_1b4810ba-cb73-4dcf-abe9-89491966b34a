{"version": 3, "file": "index.mjs", "sources": ["../../src/merge-proxy.ts", "../../src/index.ts"], "sourcesContent": ["function trueFn() {\n  return true\n}\n\nconst $PROXY = Symbol('merge-proxy')\n\n// https://github.com/solidjs/solid/blob/c20ca4fd8c36bc0522fedb2c7f38a110b7ee2663/packages/solid/src/render/component.ts#L51-L118\nconst propTraps: ProxyHandler<{\n  get: (k: string | number | symbol) => any\n  has: (k: string | number | symbol) => boolean\n  keys: () => string[]\n}> = {\n  get(_, property, receiver) {\n    if (property === $PROXY) return receiver\n    return _.get(property)\n  },\n  has(_, property) {\n    return _.has(property)\n  },\n  set: trueFn,\n  deleteProperty: trueFn,\n  getOwnPropertyDescriptor(_, property) {\n    return {\n      configurable: true,\n      enumerable: true,\n      get() {\n        return _.get(property)\n      },\n      set: trueFn,\n      deleteProperty: trueFn,\n    }\n  },\n  ownKeys(_) {\n    return _.keys()\n  },\n}\n\ntype UnboxLazy<T> = T extends () => infer U ? U : T\ntype BoxedTupleTypes<T extends any[]> = {\n  [P in keyof T]: [UnboxLazy<T[P]>]\n}[Exclude<keyof T, keyof any[]>]\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (\n  k: infer I\n) => void\n  ? I\n  : never\ntype UnboxIntersection<T> = T extends { 0: infer U } ? U : never\ntype MergeProxy<T extends any[]> = UnboxIntersection<\n  UnionToIntersection<BoxedTupleTypes<T>>\n>\n\nfunction resolveSource(s: any) {\n  return 'value' in s ? s.value : s\n}\n\nexport function mergeProxy<T extends any[]>(...sources: T): MergeProxy<T>\nexport function mergeProxy(...sources: any): any {\n  return new Proxy(\n    {\n      get(property: string | number | symbol) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          const v = resolveSource(sources[i])[property]\n          if (v !== undefined) return v\n        }\n      },\n      has(property: string | number | symbol) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          if (property in resolveSource(sources[i])) return true\n        }\n        return false\n      },\n      keys() {\n        const keys = []\n        for (let i = 0; i < sources.length; i++)\n          keys.push(...Object.keys(resolveSource(sources[i])))\n        return [...Array.from(new Set(keys))]\n      },\n    },\n    propTraps\n  )\n}\n", "import {\n  TableOptions,\n  createTable,\n  RowData,\n  TableOptionsResolved,\n} from '@tanstack/table-core'\nimport {\n  h,\n  watchEffect,\n  ref,\n  defineComponent,\n  isRef,\n  unref,\n  MaybeRef,\n  watch,\n  shallowRef,\n} from 'vue'\nimport { mergeProxy } from './merge-proxy'\n\nexport * from '@tanstack/table-core'\n\nexport type TableOptionsWithReactiveData<TData extends RowData> = Omit<\n  TableOptions<TData>,\n  'data'\n> & {\n  data: MaybeRef<TData[]>\n}\n\nexport const FlexRender = defineComponent({\n  props: ['render', 'props'],\n  setup: (props: { render: any; props: any }) => {\n    return () => {\n      if (\n        typeof props.render === 'function' ||\n        typeof props.render === 'object'\n      ) {\n        return h(props.render, props.props)\n      }\n\n      return props.render\n    }\n  },\n})\n\nfunction getOptionsWithReactiveData<TData extends RowData>(\n  options: TableOptionsWithReactiveData<TData>\n) {\n  return mergeProxy(options, {\n    data: unref(options.data),\n  })\n}\n\nexport function useVueTable<TData extends RowData>(\n  initialOptions: TableOptionsWithReactiveData<TData>\n) {\n  const IS_REACTIVE = isRef(initialOptions.data)\n\n  const resolvedOptions = mergeProxy(\n    {\n      state: {}, // Dummy state\n      onStateChange: () => {}, // noop\n      renderFallbackValue: null,\n      mergeOptions(\n        defaultOptions: TableOptions<TData>,\n        options: TableOptions<TData>\n      ) {\n        return IS_REACTIVE\n          ? {\n              ...defaultOptions,\n              ...options,\n            }\n          : mergeProxy(defaultOptions, options)\n      },\n    },\n    IS_REACTIVE ? getOptionsWithReactiveData(initialOptions) : initialOptions\n  )\n\n  const table = createTable<TData>(\n    resolvedOptions as TableOptionsResolved<TData>\n  )\n\n  // Add reactivity support\n  if (IS_REACTIVE) {\n    const dataRef = shallowRef(initialOptions.data)\n    watch(\n      dataRef,\n      () => {\n        table.setState(prev => ({\n          ...prev,\n          data: dataRef.value,\n        }))\n      },\n      { immediate: true }\n    )\n  }\n\n  // can't use `reactive` because update needs to be immutable\n  const state = ref(table.initialState)\n\n  watchEffect(() => {\n    table.setOptions(prev => {\n      const stateProxy = new Proxy({} as typeof state.value, {\n        get: (_, prop) => state.value[prop as keyof typeof state.value],\n      })\n\n      return mergeProxy(\n        prev,\n        IS_REACTIVE\n          ? getOptionsWithReactiveData(initialOptions)\n          : initialOptions,\n        {\n          // merge the initialState and `options.state`\n          // create a new proxy on each `setOptions` call\n          // and get the value from state on each property access\n          state: mergeProxy(stateProxy, initialOptions.state ?? {}),\n          // Similarly, we'll maintain both our internal state and any user-provided\n          // state.\n          onStateChange: (updater: any) => {\n            if (updater instanceof Function) {\n              state.value = updater(state.value)\n            } else {\n              state.value = updater\n            }\n\n            initialOptions.onStateChange?.(updater)\n          },\n        }\n      )\n    })\n  })\n\n  return table\n}\n"], "names": ["trueFn", "$PROXY", "Symbol", "propTraps", "get", "_", "property", "receiver", "has", "set", "deleteProperty", "getOwnPropertyDescriptor", "configurable", "enumerable", "ownKeys", "keys", "resolveSource", "s", "value", "mergeProxy", "_len", "arguments", "length", "sources", "Array", "_key", "Proxy", "i", "v", "undefined", "push", "Object", "from", "Set", "FlexRender", "defineComponent", "props", "setup", "render", "h", "getOptionsWithReactiveData", "options", "data", "unref", "useVueTable", "initialOptions", "IS_REACTIVE", "isRef", "resolvedOptions", "state", "onStateChange", "renderFallbackValue", "mergeOptions", "defaultOptions", "table", "createTable", "dataRef", "shallowRef", "watch", "setState", "prev", "immediate", "ref", "initialState", "watchEffect", "setOptions", "_initialOptions$state", "stateProxy", "prop", "updater", "Function"], "mappings": ";;;;;;;;;;;;;;AAAA,SAASA,MAAMA,GAAG;AAChB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA,MAAMC,MAAM,GAAGC,MAAM,CAAC,aAAa,CAAC,CAAA;;AAEpC;AACA,MAAMC,SAIJ,GAAG;AACHC,EAAAA,GAAGA,CAACC,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;AACzB,IAAA,IAAID,QAAQ,KAAKL,MAAM,EAAE,OAAOM,QAAQ,CAAA;AACxC,IAAA,OAAOF,CAAC,CAACD,GAAG,CAACE,QAAQ,CAAC,CAAA;GACvB;AACDE,EAAAA,GAAGA,CAACH,CAAC,EAAEC,QAAQ,EAAE;AACf,IAAA,OAAOD,CAAC,CAACG,GAAG,CAACF,QAAQ,CAAC,CAAA;GACvB;AACDG,EAAAA,GAAG,EAAET,MAAM;AACXU,EAAAA,cAAc,EAAEV,MAAM;AACtBW,EAAAA,wBAAwBA,CAACN,CAAC,EAAEC,QAAQ,EAAE;IACpC,OAAO;AACLM,MAAAA,YAAY,EAAE,IAAI;AAClBC,MAAAA,UAAU,EAAE,IAAI;AAChBT,MAAAA,GAAGA,GAAG;AACJ,QAAA,OAAOC,CAAC,CAACD,GAAG,CAACE,QAAQ,CAAC,CAAA;OACvB;AACDG,MAAAA,GAAG,EAAET,MAAM;AACXU,MAAAA,cAAc,EAAEV,MAAAA;KACjB,CAAA;GACF;EACDc,OAAOA,CAACT,CAAC,EAAE;AACT,IAAA,OAAOA,CAAC,CAACU,IAAI,EAAE,CAAA;AACjB,GAAA;AACF,CAAC,CAAA;AAgBD,SAASC,aAAaA,CAACC,CAAM,EAAE;EAC7B,OAAO,OAAO,IAAIA,CAAC,GAAGA,CAAC,CAACC,KAAK,GAAGD,CAAC,CAAA;AACnC,CAAA;AAGO,SAASE,UAAUA,GAAuB;AAAA,EAAA,KAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAnBC,OAAO,GAAAC,IAAAA,KAAA,CAAAJ,IAAA,GAAAK,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA,EAAA,EAAA;AAAPF,IAAAA,OAAO,CAAAE,IAAA,CAAAJ,GAAAA,SAAA,CAAAI,IAAA,CAAA,CAAA;AAAA,GAAA;EACnC,OAAO,IAAIC,KAAK,CACd;IACEtB,GAAGA,CAACE,QAAkC,EAAE;AACtC,MAAA,KAAK,IAAIqB,CAAC,GAAGJ,OAAO,CAACD,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5C,MAAMC,CAAC,GAAGZ,aAAa,CAACO,OAAO,CAACI,CAAC,CAAC,CAAC,CAACrB,QAAQ,CAAC,CAAA;AAC7C,QAAA,IAAIsB,CAAC,KAAKC,SAAS,EAAE,OAAOD,CAAC,CAAA;AAC/B,OAAA;KACD;IACDpB,GAAGA,CAACF,QAAkC,EAAE;AACtC,MAAA,KAAK,IAAIqB,CAAC,GAAGJ,OAAO,CAACD,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5C,IAAIrB,QAAQ,IAAIU,aAAa,CAACO,OAAO,CAACI,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAA;AACxD,OAAA;AACA,MAAA,OAAO,KAAK,CAAA;KACb;AACDZ,IAAAA,IAAIA,GAAG;MACL,MAAMA,IAAI,GAAG,EAAE,CAAA;AACf,MAAA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACD,MAAM,EAAEK,CAAC,EAAE,EACrCZ,IAAI,CAACe,IAAI,CAAC,GAAGC,MAAM,CAAChB,IAAI,CAACC,aAAa,CAACO,OAAO,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACtD,MAAA,OAAO,CAAC,GAAGH,KAAK,CAACQ,IAAI,CAAC,IAAIC,GAAG,CAAClB,IAAI,CAAC,CAAC,CAAC,CAAA;AACvC,KAAA;GACD,EACDZ,SACF,CAAC,CAAA;AACH;;ACpDa+B,MAAAA,UAAU,GAAGC,eAAe,CAAC;AACxCC,EAAAA,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;EAC1BC,KAAK,EAAGD,KAAkC,IAAK;AAC7C,IAAA,OAAO,MAAM;AACX,MAAA,IACE,OAAOA,KAAK,CAACE,MAAM,KAAK,UAAU,IAClC,OAAOF,KAAK,CAACE,MAAM,KAAK,QAAQ,EAChC;QACA,OAAOC,CAAC,CAACH,KAAK,CAACE,MAAM,EAAEF,KAAK,CAACA,KAAK,CAAC,CAAA;AACrC,OAAA;MAEA,OAAOA,KAAK,CAACE,MAAM,CAAA;KACpB,CAAA;AACH,GAAA;AACF,CAAC,EAAC;AAEF,SAASE,0BAA0BA,CACjCC,OAA4C,EAC5C;EACA,OAAOtB,UAAU,CAACsB,OAAO,EAAE;AACzBC,IAAAA,IAAI,EAAEC,KAAK,CAACF,OAAO,CAACC,IAAI,CAAA;AAC1B,GAAC,CAAC,CAAA;AACJ,CAAA;AAEO,SAASE,WAAWA,CACzBC,cAAmD,EACnD;AACA,EAAA,MAAMC,WAAW,GAAGC,KAAK,CAACF,cAAc,CAACH,IAAI,CAAC,CAAA;EAE9C,MAAMM,eAAe,GAAG7B,UAAU,CAChC;IACE8B,KAAK,EAAE,EAAE;AAAE;AACXC,IAAAA,aAAa,EAAEA,MAAM,EAAE;AAAE;AACzBC,IAAAA,mBAAmB,EAAE,IAAI;AACzBC,IAAAA,YAAYA,CACVC,cAAmC,EACnCZ,OAA4B,EAC5B;AACA,MAAA,OAAOK,WAAW,GACd;AACE,QAAA,GAAGO,cAAc;QACjB,GAAGZ,OAAAA;AACL,OAAC,GACDtB,UAAU,CAACkC,cAAc,EAAEZ,OAAO,CAAC,CAAA;AACzC,KAAA;GACD,EACDK,WAAW,GAAGN,0BAA0B,CAACK,cAAc,CAAC,GAAGA,cAC7D,CAAC,CAAA;AAED,EAAA,MAAMS,KAAK,GAAGC,WAAW,CACvBP,eACF,CAAC,CAAA;;AAED;AACA,EAAA,IAAIF,WAAW,EAAE;AACf,IAAA,MAAMU,OAAO,GAAGC,UAAU,CAACZ,cAAc,CAACH,IAAI,CAAC,CAAA;IAC/CgB,KAAK,CACHF,OAAO,EACP,MAAM;AACJF,MAAAA,KAAK,CAACK,QAAQ,CAACC,IAAI,KAAK;AACtB,QAAA,GAAGA,IAAI;QACPlB,IAAI,EAAEc,OAAO,CAACtC,KAAAA;AAChB,OAAC,CAAC,CAAC,CAAA;AACL,KAAC,EACD;AAAE2C,MAAAA,SAAS,EAAE,IAAA;AAAK,KACpB,CAAC,CAAA;AACH,GAAA;;AAEA;AACA,EAAA,MAAMZ,KAAK,GAAGa,GAAG,CAACR,KAAK,CAACS,YAAY,CAAC,CAAA;AAErCC,EAAAA,WAAW,CAAC,MAAM;AAChBV,IAAAA,KAAK,CAACW,UAAU,CAACL,IAAI,IAAI;AAAA,MAAA,IAAAM,qBAAA,CAAA;AACvB,MAAA,MAAMC,UAAU,GAAG,IAAIzC,KAAK,CAAC,EAAE,EAAwB;QACrDtB,GAAG,EAAEA,CAACC,CAAC,EAAE+D,IAAI,KAAKnB,KAAK,CAAC/B,KAAK,CAACkD,IAAI,CAAA;AACpC,OAAC,CAAC,CAAA;AAEF,MAAA,OAAOjD,UAAU,CACfyC,IAAI,EACJd,WAAW,GACPN,0BAA0B,CAACK,cAAc,CAAC,GAC1CA,cAAc,EAClB;AACE;AACA;AACA;AACAI,QAAAA,KAAK,EAAE9B,UAAU,CAACgD,UAAU,GAAAD,qBAAA,GAAErB,cAAc,CAACI,KAAK,KAAAiB,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC;AACzD;AACA;QACAhB,aAAa,EAAGmB,OAAY,IAAK;UAC/B,IAAIA,OAAO,YAAYC,QAAQ,EAAE;YAC/BrB,KAAK,CAAC/B,KAAK,GAAGmD,OAAO,CAACpB,KAAK,CAAC/B,KAAK,CAAC,CAAA;AACpC,WAAC,MAAM;YACL+B,KAAK,CAAC/B,KAAK,GAAGmD,OAAO,CAAA;AACvB,WAAA;UAEAxB,cAAc,CAACK,aAAa,IAA5BL,IAAAA,IAAAA,cAAc,CAACK,aAAa,CAAGmB,OAAO,CAAC,CAAA;AACzC,SAAA;AACF,OACF,CAAC,CAAA;AACH,KAAC,CAAC,CAAA;AACJ,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOf,KAAK,CAAA;AACd;;;;"}
{"version": 3, "file": "RowSorting.js", "sources": ["../../../src/features/RowSorting.ts"], "sourcesContent": ["import { RowModel } from '..'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n  TableFeature,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  desc: boolean\n  id: string\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  /**\n   * Enables/Disables multi-sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  invertSorting?: boolean\n  /**\n   * Set to `true` for sorting toggles on this column to start in the descending direction.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n  /**\n   * The sorting function to use with this column.\n   * - A `string` referencing a built-in sorting function\n   * - A custom sorting function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortingFn?: SortingFnOption<TData>\n  /**\n   * The priority of undefined values when sorting this column.\n   * - `false`\n   *   - Undefined values will be considered tied and need to be sorted by the next column filter or original index (whichever applies)\n   * - `-1`\n   *   - Undefined values will be sorted with higher priority (ascending) (if ascending, undefined will appear on the beginning of the list)\n   * - `1`\n   *   - Undefined values will be sorted with lower priority (descending) (if ascending, undefined will appear on the end of the list)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortundefined)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortUndefined?: false | -1 | 1 | 'first' | 'last'\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  /**\n   * Removes this column from the table's sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#clearsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  clearSorting: () => void\n  /**\n   * Returns a sort direction automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortDir: () => SortDirection\n  /**\n   * Returns a sorting function automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortingFn: () => SortingFn<TData>\n  /**\n   * Returns whether this column can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcanmultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanMultiSort: () => boolean\n  /**\n   * Returns whether this column can be sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcansort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanSort: () => boolean\n  /**\n   * Returns the first direction that should be used when sorting this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getfirstsortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getFirstSortDir: () => SortDirection\n  /**\n   * Returns the current sort direction of this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getissorted)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getIsSorted: () => false | SortDirection\n  /**\n   * Returns the next sorting order.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getnextsortingorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getNextSortingOrder: () => SortDirection | false\n  /**\n   * Returns the index position of this column's sorting within the sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortIndex: () => number\n  /**\n   * Returns the resolved sorting function to be used for this column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortingFn: () => SortingFn<TData>\n  /**\n   * Returns a function that can be used to toggle this column's sorting state. This is useful for attaching a click handler to the column header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#gettogglesortinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n  /**\n   * Toggles this columns sorting state. If `desc` is provided, it will force the sort direction to that value. If `isMulti` is provided, it will additivity multi-sort the column (or toggle it if it is already sorted).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#togglesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n}\n\ninterface SortingOptionsBase {\n  /**\n   * Enables/disables the ability to remove multi-sorts\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultiremove)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiRemove?: boolean\n  /**\n   * Enables/Disables multi-sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Enables/Disables the ability to remove sorting for the table.\n   * - If `true` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'none' -> ...\n   * - If `false` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'desc' -> 'asc' -> ...\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesortingremoval)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSortingRemoval?: boolean\n  /**\n   * This function is used to retrieve the sorted row model. If using server-side sorting, this function is not required. To use client-side sorting, pass the exported `getSortedRowModel()` from your adapter to your table or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Pass a custom function that will be used to determine if a multi-sort event should be triggered. It is passed the event from the sort toggle handler and should return `true` if the event should trigger a multi-sort.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#ismultisortevent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  isMultiSortEvent?: (e: unknown) => boolean\n  /**\n   * Enables manual sorting for the table. If this is `true`, you will be expected to sort your data before it is passed to the table. This is useful if you are doing server-side sorting.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#manualsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  manualSorting?: boolean\n  /**\n   * Set a maximum number of columns that can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#maxmultisortcolcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  maxMultiSortColCount?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.sorting` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#onsortingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  onSortingChange?: OnChangeFn<SortingState>\n  /**\n   * If `true`, all sorts will default to descending as their first toggle state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  _getSortedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getpresortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getPreSortedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **sorting** state to `initialState.sorting`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#resetsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  resetSorting: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.sorting` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#setsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  setSorting: (updater: Updater<SortingState>) => void\n}\n\n//\n\nexport const RowSorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n      let isString = false\n\n      for (const row of firstRows) {\n        const value = row?.getValue(column.id)\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime\n        }\n\n        if (typeof value === 'string') {\n          isString = true\n\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric\n          }\n        }\n      }\n\n      if (isString) {\n        return sortingFns.text\n      }\n\n      return sortingFns.basic\n    }\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return 'asc'\n      }\n\n      return 'desc'\n    }\n    column.getSortingFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.sortingFn)\n        ? column.columnDef.sortingFn\n        : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n    }\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder()\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old?.find(d => d.id === column.id)\n        const existingIndex = old?.findIndex(d => d.id === column.id)\n\n        let newSorting: SortingState = []\n\n        // What should we do with this sort action?\n        let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n        // Multi-mode\n        if (old?.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'add'\n          }\n        } else {\n          // Normal mode\n          if (old?.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace'\n          } else if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'replace'\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove'\n            }\n          }\n        }\n\n        if (sortAction === 'add') {\n          newSorting = [\n            ...old,\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n          // Take latest n columns\n          newSorting.splice(\n            0,\n            newSorting.length -\n              (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n          )\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc,\n              }\n            }\n            return d\n          })\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id)\n        } else {\n          newSorting = [\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n        }\n\n        return newSorting\n      })\n    }\n\n    column.getFirstSortDir = () => {\n      const sortDescFirst =\n        column.columnDef.sortDescFirst ??\n        table.options.sortDescFirst ??\n        column.getAutoSortDir() === 'desc'\n      return sortDescFirst ? 'desc' : 'asc'\n    }\n\n    column.getNextSortingOrder = (multi?: boolean) => {\n      const firstSortDirection = column.getFirstSortDir()\n      const isSorted = column.getIsSorted()\n\n      if (!isSorted) {\n        return firstSortDirection\n      }\n\n      if (\n        isSorted !== firstSortDirection &&\n        (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n        (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc'\n    }\n\n    column.getCanSort = () => {\n      return (\n        (column.columnDef.enableSorting ?? true) &&\n        (table.options.enableSorting ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanMultiSort = () => {\n      return (\n        column.columnDef.enableMultiSort ??\n        table.options.enableMultiSort ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsSorted = () => {\n      const columnSort = table.getState().sorting?.find(d => d.id === column.id)\n\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n    }\n\n    column.getSortIndex = () =>\n      table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1\n\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old =>\n        old?.length ? old.filter(d => d.id !== column.id) : []\n      )\n    }\n\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort()\n\n      return (e: unknown) => {\n        if (!canSort) return\n        ;(e as any).persist?.()\n        column.toggleSorting?.(\n          undefined,\n          column.getCanMultiSort() ? table.options.isMultiSortEvent?.(e) : false\n        )\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setSorting = updater => table.options.onSortingChange?.(updater)\n    table.resetSorting = defaultState => {\n      table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n    }\n    table.getPreSortedRowModel = () => table.getGroupedRowModel()\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table)\n      }\n\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel()\n      }\n\n      return table._getSortedRowModel()\n    }\n  },\n}\n"], "names": ["RowSorting", "getInitialState", "state", "sorting", "getDefaultColumnDef", "sortingFn", "sortUndefined", "getDefaultOptions", "table", "onSortingChange", "makeStateUpdater", "isMultiSortEvent", "e", "shift<PERSON>ey", "createColumn", "column", "getAutoSortingFn", "firstRows", "getFilteredRowModel", "flatRows", "slice", "isString", "row", "value", "getValue", "id", "Object", "prototype", "toString", "call", "sortingFns", "datetime", "split", "reSplitAlphaNumeric", "length", "alphanumeric", "text", "basic", "getAutoSortDir", "firstRow", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "Error", "isFunction", "columnDef", "options", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "old", "existingSorting", "find", "d", "existingIndex", "findIndex", "newSorting", "sortAction", "nextDesc", "getCanMultiSort", "_table$options$maxMul", "splice", "maxMultiSortColCount", "Number", "MAX_SAFE_INTEGER", "map", "filter", "getFirstSortDir", "_ref", "_column$columnDef$sor", "sortDescFirst", "_table$options$enable", "_table$options$enable2", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "_column$columnDef$ena", "_table$options$enable3", "enableSorting", "accessorFn", "_ref2", "_column$columnDef$ena2", "enableMultiSort", "_table$getState$sorti", "columnSort", "getState", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "persist", "undefined", "createTable", "updater", "resetSorting", "defaultState", "_table$initialState$s", "_table$initialState", "initialState", "getPreSortedRowModel", "getGroupedRowModel", "getSortedRowModel", "_getSortedRowModel", "manualSorting"], "mappings": ";;;;;;;;;;;;;;;AAmRA;;AAEO,MAAMA,UAAwB,GAAG;EACtCC,eAAe,EAAGC,KAAK,IAAwB;IAC7C,OAAO;AACLC,MAAAA,OAAO,EAAE,EAAE;MACX,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,mBAAmB,EAAEA,MAAsD;IACzE,OAAO;AACLC,MAAAA,SAAS,EAAE,MAAM;AACjBC,MAAAA,aAAa,EAAE,CAAA;KAChB,CAAA;GACF;EAEDC,iBAAiB,EACfC,KAAmB,IACO;IAC1B,OAAO;AACLC,MAAAA,eAAe,EAAEC,sBAAgB,CAAC,SAAS,EAAEF,KAAK,CAAC;MACnDG,gBAAgB,EAAGC,CAAU,IAAK;QAChC,OAAQA,CAAC,CAAgBC,QAAQ,CAAA;AACnC,OAAA;KACD,CAAA;GACF;AAEDC,EAAAA,YAAY,EAAEA,CACZC,MAA6B,EAC7BP,KAAmB,KACV;IACTO,MAAM,CAACC,gBAAgB,GAAG,MAAM;AAC9B,MAAA,MAAMC,SAAS,GAAGT,KAAK,CAACU,mBAAmB,EAAE,CAACC,QAAQ,CAACC,KAAK,CAAC,EAAE,CAAC,CAAA;MAEhE,IAAIC,QAAQ,GAAG,KAAK,CAAA;AAEpB,MAAA,KAAK,MAAMC,GAAG,IAAIL,SAAS,EAAE;QAC3B,MAAMM,KAAK,GAAGD,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEE,QAAQ,CAACT,MAAM,CAACU,EAAE,CAAC,CAAA;AAEtC,QAAA,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,KAAK,CAAC,KAAK,eAAe,EAAE;UAC7D,OAAOO,qBAAU,CAACC,QAAQ,CAAA;AAC5B,SAAA;AAEA,QAAA,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;AAC7BF,UAAAA,QAAQ,GAAG,IAAI,CAAA;UAEf,IAAIE,KAAK,CAACS,KAAK,CAACC,8BAAmB,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;YAC/C,OAAOJ,qBAAU,CAACK,YAAY,CAAA;AAChC,WAAA;AACF,SAAA;AACF,OAAA;AAEA,MAAA,IAAId,QAAQ,EAAE;QACZ,OAAOS,qBAAU,CAACM,IAAI,CAAA;AACxB,OAAA;MAEA,OAAON,qBAAU,CAACO,KAAK,CAAA;KACxB,CAAA;IACDtB,MAAM,CAACuB,cAAc,GAAG,MAAM;MAC5B,MAAMC,QAAQ,GAAG/B,KAAK,CAACU,mBAAmB,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;MAExD,MAAMI,KAAK,GAAGgB,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEf,QAAQ,CAACT,MAAM,CAACU,EAAE,CAAC,CAAA;AAE3C,MAAA,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AAEA,MAAA,OAAO,MAAM,CAAA;KACd,CAAA;IACDR,MAAM,CAACyB,YAAY,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;MAC1B,IAAI,CAAC3B,MAAM,EAAE;QACX,MAAM,IAAI4B,KAAK,EAAE,CAAA;AACnB,OAAA;MAEA,OAAOC,gBAAU,CAAC7B,MAAM,CAAC8B,SAAS,CAACxC,SAAS,CAAC,GACzCU,MAAM,CAAC8B,SAAS,CAACxC,SAAS,GAC1BU,MAAM,CAAC8B,SAAS,CAACxC,SAAS,KAAK,MAAM,GACnCU,MAAM,CAACC,gBAAgB,EAAE,IAAAyB,qBAAA,GAAA,CAAAC,sBAAA,GACzBlC,KAAK,CAACsC,OAAO,CAAChB,UAAU,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBY,sBAAA,CAA2B3B,MAAM,CAAC8B,SAAS,CAACxC,SAAS,CAAW,KAAAoC,IAAAA,GAAAA,qBAAA,GAChEX,qBAAU,CAACf,MAAM,CAAC8B,SAAS,CAACxC,SAAS,CAAqB,CAAA;KACjE,CAAA;AACDU,IAAAA,MAAM,CAACgC,aAAa,GAAG,CAACC,IAAI,EAAEC,KAAK,KAAK;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAA,MAAMC,gBAAgB,GAAGnC,MAAM,CAACoC,mBAAmB,EAAE,CAAA;MACrD,MAAMC,cAAc,GAAG,OAAOJ,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,CAAA;AAEnExC,MAAAA,KAAK,CAAC6C,UAAU,CAACC,GAAG,IAAI;AACtB;AACA,QAAA,MAAMC,eAAe,GAAGD,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKV,MAAM,CAACU,EAAE,CAAC,CAAA;AAC1D,QAAA,MAAMiC,aAAa,GAAGJ,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEK,SAAS,CAACF,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKV,MAAM,CAACU,EAAE,CAAC,CAAA;QAE7D,IAAImC,UAAwB,GAAG,EAAE,CAAA;;AAEjC;AACA,QAAA,IAAIC,UAAmD,CAAA;QACvD,IAAIC,QAAQ,GAAGV,cAAc,GAAGJ,IAAI,GAAGE,gBAAgB,KAAK,MAAM,CAAA;;AAElE;AACA,QAAA,IAAII,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEpB,MAAM,IAAInB,MAAM,CAACgD,eAAe,EAAE,IAAId,KAAK,EAAE;AACpD,UAAA,IAAIM,eAAe,EAAE;AACnBM,YAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,WAAC,MAAM;AACLA,YAAAA,UAAU,GAAG,KAAK,CAAA;AACpB,WAAA;AACF,SAAC,MAAM;AACL;AACA,UAAA,IAAIP,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEpB,MAAM,IAAIwB,aAAa,KAAKJ,GAAG,CAACpB,MAAM,GAAG,CAAC,EAAE;AACnD2B,YAAAA,UAAU,GAAG,SAAS,CAAA;WACvB,MAAM,IAAIN,eAAe,EAAE;AAC1BM,YAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,WAAC,MAAM;AACLA,YAAAA,UAAU,GAAG,SAAS,CAAA;AACxB,WAAA;AACF,SAAA;;AAEA;QACA,IAAIA,UAAU,KAAK,QAAQ,EAAE;AAC3B;UACA,IAAI,CAACT,cAAc,EAAE;AACnB;YACA,IAAI,CAACF,gBAAgB,EAAE;AACrBW,cAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAIA,UAAU,KAAK,KAAK,EAAE;AAAA,UAAA,IAAAG,qBAAA,CAAA;AACxBJ,UAAAA,UAAU,GAAG,CACX,GAAGN,GAAG,EACN;YACE7B,EAAE,EAAEV,MAAM,CAACU,EAAE;AACbuB,YAAAA,IAAI,EAAEc,QAAAA;AACR,WAAC,CACF,CAAA;AACD;UACAF,UAAU,CAACK,MAAM,CACf,CAAC,EACDL,UAAU,CAAC1B,MAAM,IAAA,CAAA8B,qBAAA,GACdxD,KAAK,CAACsC,OAAO,CAACoB,oBAAoB,KAAAF,IAAAA,GAAAA,qBAAA,GAAIG,MAAM,CAACC,gBAAgB,CAClE,CAAC,CAAA;AACH,SAAC,MAAM,IAAIP,UAAU,KAAK,QAAQ,EAAE;AAClC;AACAD,UAAAA,UAAU,GAAGN,GAAG,CAACe,GAAG,CAACZ,CAAC,IAAI;AACxB,YAAA,IAAIA,CAAC,CAAChC,EAAE,KAAKV,MAAM,CAACU,EAAE,EAAE;cACtB,OAAO;AACL,gBAAA,GAAGgC,CAAC;AACJT,gBAAAA,IAAI,EAAEc,QAAAA;eACP,CAAA;AACH,aAAA;AACA,YAAA,OAAOL,CAAC,CAAA;AACV,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM,IAAII,UAAU,KAAK,QAAQ,EAAE;AAClCD,UAAAA,UAAU,GAAGN,GAAG,CAACgB,MAAM,CAACb,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKV,MAAM,CAACU,EAAE,CAAC,CAAA;AAClD,SAAC,MAAM;AACLmC,UAAAA,UAAU,GAAG,CACX;YACEnC,EAAE,EAAEV,MAAM,CAACU,EAAE;AACbuB,YAAAA,IAAI,EAAEc,QAAAA;AACR,WAAC,CACF,CAAA;AACH,SAAA;AAEA,QAAA,OAAOF,UAAU,CAAA;AACnB,OAAC,CAAC,CAAA;KACH,CAAA;IAED7C,MAAM,CAACwD,eAAe,GAAG,MAAM;MAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;AAC7B,MAAA,MAAMC,aAAa,GAAA,CAAAF,IAAA,GAAA,CAAAC,qBAAA,GACjB1D,MAAM,CAAC8B,SAAS,CAAC6B,aAAa,KAAA,IAAA,GAAAD,qBAAA,GAC9BjE,KAAK,CAACsC,OAAO,CAAC4B,aAAa,KAAA,IAAA,GAAAF,IAAA,GAC3BzD,MAAM,CAACuB,cAAc,EAAE,KAAK,MAAM,CAAA;AACpC,MAAA,OAAOoC,aAAa,GAAG,MAAM,GAAG,KAAK,CAAA;KACtC,CAAA;AAED3D,IAAAA,MAAM,CAACoC,mBAAmB,GAAIF,KAAe,IAAK;MAAA,IAAA0B,qBAAA,EAAAC,sBAAA,CAAA;AAChD,MAAA,MAAMC,kBAAkB,GAAG9D,MAAM,CAACwD,eAAe,EAAE,CAAA;AACnD,MAAA,MAAMO,QAAQ,GAAG/D,MAAM,CAACgE,WAAW,EAAE,CAAA;MAErC,IAAI,CAACD,QAAQ,EAAE;AACb,QAAA,OAAOD,kBAAkB,CAAA;AAC3B,OAAA;AAEA,MAAA,IACEC,QAAQ,KAAKD,kBAAkB,KAAA,CAAAF,qBAAA,GAC9BnE,KAAK,CAACsC,OAAO,CAACkC,oBAAoB,KAAA,IAAA,GAAAL,qBAAA,GAAI,IAAI,CAAC;AAAI;AAC/C1B,MAAAA,KAAK,GAAA2B,CAAAA,sBAAA,GAAGpE,KAAK,CAACsC,OAAO,CAACmC,iBAAiB,KAAA,IAAA,GAAAL,sBAAA,GAAI,IAAI,GAAG,IAAI,CAAC;QACxD;AACA,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AACA,MAAA,OAAOE,QAAQ,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM,CAAA;KAC5C,CAAA;IAED/D,MAAM,CAACmE,UAAU,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;AACxB,MAAA,OACE,CAAAD,CAAAA,qBAAA,GAACpE,MAAM,CAAC8B,SAAS,CAACwC,aAAa,KAAAF,IAAAA,GAAAA,qBAAA,GAAI,IAAI,OAAAC,sBAAA,GACtC5E,KAAK,CAACsC,OAAO,CAACuC,aAAa,KAAA,IAAA,GAAAD,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAACrE,MAAM,CAACuE,UAAU,CAAA;KAEtB,CAAA;IAEDvE,MAAM,CAACgD,eAAe,GAAG,MAAM;MAAA,IAAAwB,KAAA,EAAAC,sBAAA,CAAA;MAC7B,OAAAD,CAAAA,KAAA,GAAAC,CAAAA,sBAAA,GACEzE,MAAM,CAAC8B,SAAS,CAAC4C,eAAe,KAAA,IAAA,GAAAD,sBAAA,GAChChF,KAAK,CAACsC,OAAO,CAAC2C,eAAe,KAAAF,IAAAA,GAAAA,KAAA,GAC7B,CAAC,CAACxE,MAAM,CAACuE,UAAU,CAAA;KAEtB,CAAA;IAEDvE,MAAM,CAACgE,WAAW,GAAG,MAAM;AAAA,MAAA,IAAAW,qBAAA,CAAA;MACzB,MAAMC,UAAU,GAAAD,CAAAA,qBAAA,GAAGlF,KAAK,CAACoF,QAAQ,EAAE,CAACzF,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxBuF,qBAAA,CAA0BlC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKV,MAAM,CAACU,EAAE,CAAC,CAAA;MAE1E,OAAO,CAACkE,UAAU,GAAG,KAAK,GAAGA,UAAU,CAAC3C,IAAI,GAAG,MAAM,GAAG,KAAK,CAAA;KAC9D,CAAA;IAEDjC,MAAM,CAAC8E,YAAY,GAAG,MAAA;MAAA,IAAAC,sBAAA,EAAAC,sBAAA,CAAA;AAAA,MAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACpBvF,KAAK,CAACoF,QAAQ,EAAE,CAACzF,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAxB4F,sBAAA,CAA0BpC,SAAS,CAACF,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKV,MAAM,CAACU,EAAE,CAAC,KAAA,IAAA,GAAAqE,sBAAA,GAAI,CAAC,CAAC,CAAA;AAAA,KAAA,CAAA;IAEpE/E,MAAM,CAACiF,YAAY,GAAG,MAAM;AAC1B;MACAxF,KAAK,CAAC6C,UAAU,CAACC,GAAG,IAClBA,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEpB,MAAM,GAAGoB,GAAG,CAACgB,MAAM,CAACb,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKV,MAAM,CAACU,EAAE,CAAC,GAAG,EACtD,CAAC,CAAA;KACF,CAAA;IAEDV,MAAM,CAACkF,uBAAuB,GAAG,MAAM;AACrC,MAAA,MAAMC,OAAO,GAAGnF,MAAM,CAACmE,UAAU,EAAE,CAAA;AAEnC,MAAA,OAAQtE,CAAU,IAAK;QACrB,IAAI,CAACsF,OAAO,EAAE,OAAA;AACZtF,QAAAA,CAAC,CAASuF,OAAO,IAAA,IAAA,IAAjBvF,CAAC,CAASuF,OAAO,EAAI,CAAA;AACvBpF,QAAAA,MAAM,CAACgC,aAAa,IAApBhC,IAAAA,IAAAA,MAAM,CAACgC,aAAa,CAClBqD,SAAS,EACTrF,MAAM,CAACgD,eAAe,EAAE,GAAGvD,KAAK,CAACsC,OAAO,CAACnC,gBAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAA9BH,KAAK,CAACsC,OAAO,CAACnC,gBAAgB,CAAGC,CAAC,CAAC,GAAG,KACnE,CAAC,CAAA;OACF,CAAA;KACF,CAAA;GACF;EAEDyF,WAAW,EAA0B7F,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAAC6C,UAAU,GAAGiD,OAAO,IAAI9F,KAAK,CAACsC,OAAO,CAACrC,eAAe,IAAA,IAAA,GAAA,KAAA,CAAA,GAA7BD,KAAK,CAACsC,OAAO,CAACrC,eAAe,CAAG6F,OAAO,CAAC,CAAA;AACtE9F,IAAAA,KAAK,CAAC+F,YAAY,GAAGC,YAAY,IAAI;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MACnClG,KAAK,CAAC6C,UAAU,CAACmD,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAA,CAAAC,mBAAA,GAAGlG,KAAK,CAACmG,YAAY,qBAAlBD,mBAAA,CAAoBvG,OAAO,KAAAsG,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC,CAAA;KACxE,CAAA;IACDjG,KAAK,CAACoG,oBAAoB,GAAG,MAAMpG,KAAK,CAACqG,kBAAkB,EAAE,CAAA;IAC7DrG,KAAK,CAACsG,iBAAiB,GAAG,MAAM;MAC9B,IAAI,CAACtG,KAAK,CAACuG,kBAAkB,IAAIvG,KAAK,CAACsC,OAAO,CAACgE,iBAAiB,EAAE;QAChEtG,KAAK,CAACuG,kBAAkB,GAAGvG,KAAK,CAACsC,OAAO,CAACgE,iBAAiB,CAACtG,KAAK,CAAC,CAAA;AACnE,OAAA;MAEA,IAAIA,KAAK,CAACsC,OAAO,CAACkE,aAAa,IAAI,CAACxG,KAAK,CAACuG,kBAAkB,EAAE;AAC5D,QAAA,OAAOvG,KAAK,CAACoG,oBAAoB,EAAE,CAAA;AACrC,OAAA;AAEA,MAAA,OAAOpG,KAAK,CAACuG,kBAAkB,EAAE,CAAA;KAClC,CAAA;AACH,GAAA;AACF;;;;"}
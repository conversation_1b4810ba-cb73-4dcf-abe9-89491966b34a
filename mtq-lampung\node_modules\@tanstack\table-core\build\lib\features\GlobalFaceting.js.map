{"version": 3, "file": "GlobalFaceting.js", "sources": ["../../../src/features/GlobalFaceting.ts"], "sourcesContent": ["import { RowModel } from '..'\nimport { Table, RowData, TableFeature } from '../types'\n\nexport interface GlobalFacetingInstance<TData extends RowData> {\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model for the table after **global** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  /**\n   * Returns the faceted unique values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n}\n\n//\n\nexport const GlobalFaceting: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table._getGlobalFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, '__global__')\n\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getGlobalFacetedRowModel()\n    }\n\n    table._getGlobalFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, '__global__')\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return table._getGlobalFacetedUniqueValues()\n    }\n\n    table._getGlobalFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, '__global__')\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return\n      }\n\n      return table._getGlobalFacetedMinMaxValues()\n    }\n  },\n}\n"], "names": ["GlobalFaceting", "createTable", "table", "_getGlobalFacetedRowModel", "options", "getFacetedRowModel", "getGlobalFacetedRowModel", "manualFiltering", "getPreFilteredRowModel", "_getGlobalFacetedUniqueValues", "getFacetedUniqueValues", "getGlobalFacetedUniqueValues", "Map", "_getGlobalFacetedMinMaxValues", "getFacetedMinMaxValues", "getGlobalFacetedMinMaxValues"], "mappings": ";;;;;;;;;;;;AA2BA;;AAEO,MAAMA,cAA4B,GAAG;EAC1CC,WAAW,EAA0BC,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACC,yBAAyB,GAC7BD,KAAK,CAACE,OAAO,CAACC,kBAAkB,IAChCH,KAAK,CAACE,OAAO,CAACC,kBAAkB,CAACH,KAAK,EAAE,YAAY,CAAC,CAAA;IAEvDA,KAAK,CAACI,wBAAwB,GAAG,MAAM;MACrC,IAAIJ,KAAK,CAACE,OAAO,CAACG,eAAe,IAAI,CAACL,KAAK,CAACC,yBAAyB,EAAE;AACrE,QAAA,OAAOD,KAAK,CAACM,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAON,KAAK,CAACC,yBAAyB,EAAE,CAAA;KACzC,CAAA;AAEDD,IAAAA,KAAK,CAACO,6BAA6B,GACjCP,KAAK,CAACE,OAAO,CAACM,sBAAsB,IACpCR,KAAK,CAACE,OAAO,CAACM,sBAAsB,CAACR,KAAK,EAAE,YAAY,CAAC,CAAA;IAC3DA,KAAK,CAACS,4BAA4B,GAAG,MAAM;AACzC,MAAA,IAAI,CAACT,KAAK,CAACO,6BAA6B,EAAE;QACxC,OAAO,IAAIG,GAAG,EAAE,CAAA;AAClB,OAAA;AAEA,MAAA,OAAOV,KAAK,CAACO,6BAA6B,EAAE,CAAA;KAC7C,CAAA;AAEDP,IAAAA,KAAK,CAACW,6BAA6B,GACjCX,KAAK,CAACE,OAAO,CAACU,sBAAsB,IACpCZ,KAAK,CAACE,OAAO,CAACU,sBAAsB,CAACZ,KAAK,EAAE,YAAY,CAAC,CAAA;IAC3DA,KAAK,CAACa,4BAA4B,GAAG,MAAM;AACzC,MAAA,IAAI,CAACb,KAAK,CAACW,6BAA6B,EAAE;AACxC,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,OAAOX,KAAK,CAACW,6BAA6B,EAAE,CAAA;KAC7C,CAAA;AACH,GAAA;AACF;;;;"}
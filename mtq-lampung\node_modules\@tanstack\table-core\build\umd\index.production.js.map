{"version": 3, "file": "index.production.js", "sources": ["../../src/utils.ts", "../../src/core/cell.ts", "../../src/core/column.ts", "../../src/core/headers.ts", "../../src/core/row.ts", "../../src/features/ColumnFaceting.ts", "../../src/filterFns.ts", "../../src/features/ColumnFiltering.ts", "../../src/aggregationFns.ts", "../../src/features/ColumnGrouping.ts", "../../src/features/ColumnOrdering.ts", "../../src/features/ColumnPinning.ts", "../../src/features/ColumnSizing.ts", "../../src/utils/document.ts", "../../src/features/ColumnVisibility.ts", "../../src/features/GlobalFaceting.ts", "../../src/features/GlobalFiltering.ts", "../../src/features/RowExpanding.ts", "../../src/features/RowPagination.ts", "../../src/features/RowPinning.ts", "../../src/features/RowSelection.ts", "../../src/sortingFns.ts", "../../src/features/RowSorting.ts", "../../src/core/table.ts", "../../src/utils/getExpandedRowModel.ts", "../../src/utils/filterRowsUtils.ts", "../../src/columnHelper.ts", "../../src/utils/getCoreRowModel.ts", "../../src/utils/getFacetedMinMaxValues.ts", "../../src/utils/getFacetedRowModel.ts", "../../src/utils/getFacetedUniqueValues.ts", "../../src/utils/getFilteredRowModel.ts", "../../src/utils/getGroupedRowModel.ts", "../../src/utils/getPaginationRowModel.ts", "../../src/utils/getSortedRowModel.ts"], "sourcesContent": ["import { TableOptionsResolved, TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = [],\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never,\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n    ? AllowedIndexes<Tail, Keys | Tail['length']>\n    : Keys\n\nexport type DeepKeys<T, TDepth extends any[] = []> = TDepth['length'] extends 5\n  ? never\n  : unknown extends T\n    ? string\n    : T extends readonly any[] & IsTuple<T>\n      ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>, TDepth>\n      : T extends any[]\n        ? DeepKeys<T[number], [...TDepth, any]>\n        : T extends Date\n          ? never\n          : T extends object\n            ? (keyof T & string) | DeepKeysPrefix<T, keyof T, TDepth>\n            : never\n\ntype DeepKeysPrefix<\n  T,\n  TPrefix,\n  TDepth extends any[],\n> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix], [...TDepth, any]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> =\n  T extends Record<string | number, any>\n    ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n      ? DeepValue<T[TBranch], TDeepProp>\n      : T[TProp & string]\n    : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TDepArgs, TResult>(\n  getDeps: (depArgs?: TDepArgs) => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): (depArgs?: TDepArgs) => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return depArgs => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps(depArgs)\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n\nexport function getMemoOptions(\n  tableOptions: Partial<TableOptionsResolved<any>>,\n  debugLevel:\n    | 'debugAll'\n    | 'debugCells'\n    | 'debugTable'\n    | 'debugColumns'\n    | 'debugRows'\n    | 'debugHeaders',\n  key: string,\n  onChange?: (result: any) => void\n) {\n  return {\n    debug: () => tableOptions?.debugAll ?? tableOptions[debugLevel],\n    key: process.env.NODE_ENV === 'development' && key,\n    onChange,\n  }\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, getMemoOptions, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  cell: Cell<TData, TValue>\n  column: Column<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n  row: Row<TData>\n  table: Table<TData>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  /**\n   * The associated Column object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  column: Column<TData, TValue>\n  /**\n   * Returns the rendering context (or props) for cell-based components like cells and aggregated cells. Use these props with your framework's `flexRender` utility to render these using the template of your choice:\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getContext: () => CellContext<TData, TValue>\n  /**\n   * Returns the value for the cell, accessed via the associated column's accessor key or accessor function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getValue: CellContext<TData, TValue>['getValue']\n  /**\n   * The unique ID for the cell across the entire table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  id: string\n  /**\n   * Renders the value for a cell the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  renderValue: CellContext<TData, TValue>['renderValue']\n  /**\n   * The associated Row object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#row)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  row: Row<TData>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      getMemoOptions(table.options, 'debugCells', 'cell.getContext')\n    ),\n  }\n\n  table._features.forEach(feature => {\n    feature.createCell?.(\n      cell as Cell<TData, TValue>,\n      column,\n      row as Row<TData>,\n      table\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  /**\n   * The resolved accessor function to use when extracting the value for the column from each row. Will only be defined if the column def has a valid accessor key or function defined.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#accessorfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  accessorFn?: AccessorFn<TData, TValue>\n  /**\n   * The original column def used to create the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columndef)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columnDef: ColumnDef<TData, TValue>\n  /**\n   * The child column (if the column is a group column). Will be an empty array if the column is not a group column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columns: Column<TData, TValue>[]\n  /**\n   * The depth of the column (if grouped) relative to the root column def array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  depth: number\n  /**\n   * Returns the flattened array of this column and all child/grand-child columns for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getFlatColumns: () => Column<TData, TValue>[]\n  /**\n   * Returns an array of all leaf-node columns for this column. If a column has no children, it is considered the only leaf-node column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getLeafColumns: () => Column<TData, TValue>[]\n  /**\n   * The resolved unique identifier for the column resolved in this priority:\n      - A manual `id` property from the column def\n      - The accessor key from the column def\n      - The header string from the column def\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  id: string\n  /**\n   * The parent column for this column. Will be undefined if this is a root column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#parent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  parent?: Column<TData, TValue>\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey\n      ? typeof String.prototype.replaceAll === 'function'\n        ? accessorKey.replaceAll('.', '_')\n        : accessorKey.replace(/\\./g, '_')\n      : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns')\n    ),\n  }\n\n  for (const feature of table._features) {\n    feature.createColumn?.(column as Column<TData, TValue>, table)\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  HeaderGroup,\n  Table,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nconst debug = 'debugHeaders'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  depth: number\n  headers: Header<TData, unknown>[]\n  id: string\n}\n\nexport interface HeaderContext<TData, TValue> {\n  /**\n   * An instance of a column.\n   */\n  column: Column<TData, TValue>\n  /**\n   * An instance of a header.\n   */\n  header: Header<TData, TValue>\n  /**\n   * The table instance.\n   */\n  table: Table<TData>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  /**\n   * The col-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#colspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  colSpan: number\n  /**\n   * The header's associated column object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  column: Column<TData, TValue>\n  /**\n   * The depth of the header, zero-indexed based.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  depth: number\n  /**\n   * Returns the rendering context (or props) for column-based components like headers, footers and filters.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getContext: () => HeaderContext<TData, TValue>\n  /**\n   * Returns the leaf headers hierarchically nested under this header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * The header's associated header group object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#headergroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  headerGroup: HeaderGroup<TData>\n  /**\n   * The unique identifier for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  id: string\n  /**\n   * The index for the header within the header group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  index: number\n  /**\n   * A boolean denoting if the header is a placeholder header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#isplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  isPlaceholder: boolean\n  /**\n   * If the header is a placeholder header, this will be a unique header ID that does not conflict with any other headers across the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#placeholderid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  placeholderId?: string\n  /**\n   * The row-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#rowspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  rowSpan: number\n  /**\n   * The header's hierarchical sub/child headers. Will be empty if the header's associated column is a leaf-column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#subheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  subHeaders: Header<TData, TValue>[]\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  /**\n   * Returns all header groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns the footer groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns headers for all columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  /**\n   * Returns headers for all leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    feature.createHeader?.(header as Header<TData, TValue>, table)\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        const leftColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const rightColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const centerColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n\n        const headerGroups = buildHeaderGroups(\n          allColumns,\n          [...leftColumns, ...centerColumns, ...rightColumns],\n          table\n        )\n\n        return headerGroups\n      },\n      getMemoOptions(table.options, debug, 'getHeaderGroups')\n    )\n\n    table.getCenterHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        leafColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n        return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n      },\n      getMemoOptions(table.options, debug, 'getCenterHeaderGroups')\n    )\n\n    table.getLeftHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n      ],\n      (allColumns, leafColumns, left) => {\n        const orderedLeafColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left')\n      },\n      getMemoOptions(table.options, debug, 'getLeftHeaderGroups')\n    )\n\n    table.getRightHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, right) => {\n        const orderedLeafColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right')\n      },\n      getMemoOptions(table.options, debug, 'getRightHeaderGroups')\n    )\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getFooterGroups')\n    )\n\n    table.getLeftFooterGroups = memo(\n      () => [table.getLeftHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFooterGroups')\n    )\n\n    table.getCenterFooterGroups = memo(\n      () => [table.getCenterHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFooterGroups')\n    )\n\n    table.getRightFooterGroups = memo(\n      () => [table.getRightHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getRightFooterGroups')\n    )\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return headerGroups\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getFlatHeaders')\n    )\n\n    table.getLeftFlatHeaders = memo(\n      () => [table.getLeftHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFlatHeaders')\n    )\n\n    table.getCenterFlatHeaders = memo(\n      () => [table.getCenterHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFlatHeaders')\n    )\n\n    table.getRightFlatHeaders = memo(\n      () => [table.getRightHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getRightFlatHeaders')\n    )\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(\n      () => [table.getCenterFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getCenterLeafHeaders')\n    )\n\n    table.getLeftLeafHeaders = memo(\n      () => [table.getLeftFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getLeftLeafHeaders')\n    )\n\n    table.getRightLeafHeaders = memo(\n      () => [table.getRightFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getRightLeafHeaders')\n    )\n\n    table.getLeafHeaders = memo(\n      () => [\n        table.getLeftHeaderGroups(),\n        table.getCenterHeaderGroups(),\n        table.getRightHeaderGroups(),\n      ],\n      (left, center, right) => {\n        return [\n          ...(left[0]?.headers ?? []),\n          ...(center[0]?.headers ?? []),\n          ...(right[0]?.headers ?? []),\n        ]\n          .map(header => {\n            return header.getLeafHeaders()\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeafHeaders')\n    )\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  _uniqueValuesCache: Record<string, unknown>\n  _valuesCache: Record<string, unknown>\n  /**\n   * The depth of the row (if nested or grouped) relative to the root row array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  depth: number\n  /**\n   * Returns all of the cells for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getallcells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getAllCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns the leaf rows for the row, not including any parent rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getLeafRows: () => Row<TData>[]\n  /**\n   * Returns the parent row for the row, if it exists.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRow: () => Row<TData> | undefined\n  /**\n   * Returns the parent rows for the row, all the way up to a root row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRows: () => Row<TData>[]\n  /**\n   * Returns a unique array of values from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getuniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  /**\n   * Returns the value from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getValue: <TValue>(columnId: string) => TValue\n  /**\n   * The resolved unique identifier for the row resolved via the `options.getRowId` option. Defaults to the row's index (or relative index if it is a subRow).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  id: string\n  /**\n   * The index of the row within its parent array (or the root data array).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  index: number\n  /**\n   * The original row object provided to the table. If the row is a grouped row, the original row object will be the first original in the group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#original)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  original: TData\n  /**\n   * An array of the original subRows as returned by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#originalsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  originalSubRows?: TData[]\n  /**\n   * If nested, this row's parent row id.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#parentid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  parentId?: string\n  /**\n   * Renders the value for the row in a given columnId the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  renderValue: <TValue>(columnId: string) => TValue\n  /**\n   * An array of subRows for the row as returned and created by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#subrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  subRows: Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () =>\n      row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCells')\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce(\n          (acc, cell) => {\n            acc[cell.column.id] = cell\n            return acc\n          },\n          {} as Record<string, Cell<TData, unknown>>\n        )\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId')\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    feature?.createRow?.(row as Row<TData>, table)\n  }\n\n  return row as Row<TData>\n}\n", "import { RowModel } from '..'\nimport { Column, RowData, Table, TableFeature } from '../types'\n\nexport interface FacetedColumn<TData extends RowData> {\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n  _getFacetedRowModel?: () => RowModel<TData>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * A function that **computes and returns** a min/max tuple derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedMinMaxValues` function to `options.getFacetedMinMaxValues`. A default implementation is provided via the exported `getFacetedMinMaxValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model with all other column filters applied, excluding its own filter. Useful for displaying faceted result counts.\n   * > ⚠️ Requires that you pass a valid `getFacetedRowModel` function to `options.facetedRowModel`. A default implementation is provided via the exported `getFacetedRowModel` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedRowModel: () => RowModel<TData>\n  /**\n   * A function that **computes and returns** a `Map` of unique values and their occurrences derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedUniqueValues` function to `options.getFacetedUniqueValues`. A default implementation is provided via the exported `getFacetedUniqueValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-faceting#getfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-faceting)\n   */\n  getFacetedUniqueValues: () => Map<any, number>\n}\n\nexport interface FacetedOptions<TData extends RowData> {\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n}\n\n//\n\nexport const ColumnFaceting: TableFeature = {\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column._getFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, column.id)\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return column._getFacetedRowModel()\n    }\n    column._getFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, column.id)\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return column._getFacetedUniqueValues()\n    }\n    column._getFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, column.id)\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined\n      }\n\n      return column._getFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn } from './features/ColumnFiltering'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue?.toString()?.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(val =>\n    row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  FilterFns,\n  FilterMeta,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface ColumnFiltersTableState {\n  columnFilters: ColumnFiltersState\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  filterFn: FilterFn<TData>\n  id: string\n  resolvedValue: unknown\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n  resolveFilterValue?: TransformFilterValueFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface ColumnFiltersColumnDef<TData extends RowData> {\n  /**\n   * Enables/disables the **column** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilter?: boolean\n  /**\n   * The filter function to use with this column. Can be the name of a built-in filter function or a custom filter function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFn?: FilterFnOption<TData>\n}\n\nexport interface ColumnFiltersColumn<TData extends RowData> {\n  /**\n   * Returns an automatically calculated filter function for the column based off of the columns first known value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be **column** filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getcanfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getCanFilter: () => boolean\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the columnId specified.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the index (including `-1`) of the column filter in the table's `state.columnFilters` array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterIndex: () => number\n  /**\n   * Returns the current filter value for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterValue: () => unknown\n  /**\n   * Returns whether or not the column is currently filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getisfiltered)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getIsFiltered: () => boolean\n  /**\n   * A function that sets the current filter value for the column. You can pass it a value or an updater function for immutability-safe operations on existing values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setFilterValue: (updater: Updater<any>) => void\n}\n\nexport interface ColumnFiltersRow<TData extends RowData> {\n  /**\n   * The column filters map for the row. This object tracks whether a row is passing/failing specific filters by their column ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFilters: Record<string, boolean>\n  /**\n   * The column filters meta map for the row. This object tracks any filter meta for a row as optionally provided during the filtering process.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfiltersmeta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface ColumnFiltersOptionsBase<TData extends RowData> {\n  /**\n   * Enables/disables **column** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilters?: boolean\n  /**\n   * Enables/disables all filtering for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablefilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableFilters?: boolean\n  /**\n   * By default, filtering is done from parent rows down (so if a parent row is filtered out, all of its children will be filtered out as well). Setting this option to `true` will cause filtering to be done from leaf rows up (which means parent rows will be included so long as one of their child or grand-child rows is also included).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfromleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFromLeafRows?: boolean\n  /**\n   * If provided, this function is called **once** per table and should return a **new function** which will calculate and return the row model for the table when it's filtered.\n   * - For server-side filtering, this function is unnecessary and can be ignored since the server should already return the filtered row model.\n   * - For client-side filtering, this function is required. A default implementation is provided via any table adapter's `{ getFilteredRowModel }` export.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Disables the `getFilteredRowModel` from being used to filter data. This may be useful if your table needs to dynamically support both client-side and server-side filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#manualfiltering)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  manualFiltering?: boolean\n  /**\n   * By default, filtering is done for all rows (max depth of 100), no matter if they are root level parent rows or the child leaf rows of a parent row. Setting this option to `0` will cause filtering to only be applied to the root level parent rows, with all sub-rows remaining unfiltered. Similarly, setting this option to `1` will cause filtering to only be applied to child leaf rows 1 level deep, and so on.\n\n   * This is useful for situations where you want a row's entire child hierarchy to be visible regardless of the applied filter.\n    * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#maxleafrowfilterdepth)\n    * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  maxLeafRowFilterDepth?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnFilters` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#oncolumnfilterschange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface ColumnFiltersOptions<TData extends RowData>\n  extends ColumnFiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface ColumnFiltersInstance<TData extends RowData> {\n  _getFilteredRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getprefilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getPreFilteredRowModel: () => RowModel<TData>\n  /**\n   * Resets the **columnFilters** state to `initialState.columnFilters`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetColumnFilters: (defaultState?: boolean) => void\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnFilters` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const ColumnFiltering: TableFeature = {\n  getDefaultColumnDef: <\n    TData extends RowData,\n  >(): ColumnFiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): ColumnFiltersTableState => {\n    return {\n      columnFilters: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnFiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n    } as ColumnFiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return filterFns.includesString\n      }\n\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange\n      }\n\n      if (typeof value === 'boolean') {\n        return filterFns.equals\n      }\n\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals\n      }\n\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes\n      }\n\n      return filterFns.weakEquals\n    }\n    column.getFilterFn = () => {\n      return isFunction(column.columnDef.filterFn)\n        ? column.columnDef.filterFn\n        : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n    }\n    column.getCanFilter = () => {\n      return (\n        (column.columnDef.enableColumnFilter ?? true) &&\n        (table.options.enableColumnFilters ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsFiltered = () => column.getFilterIndex() > -1\n\n    column.getFilterValue = () =>\n      table.getState().columnFilters?.find(d => d.id === column.id)?.value\n\n    column.getFilterIndex = () =>\n      table.getState().columnFilters?.findIndex(d => d.id === column.id) ?? -1\n\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn()\n        const previousFilter = old?.find(d => d.id === column.id)\n\n        const newFilter = functionalUpdate(\n          value,\n          previousFilter ? previousFilter.value : undefined\n        )\n\n        //\n        if (\n          shouldAutoRemoveFilter(filterFn as FilterFn<TData>, newFilter, column)\n        ) {\n          return old?.filter(d => d.id !== column.id) ?? []\n        }\n\n        const newFilterObj = { id: column.id, value: newFilter }\n\n        if (previousFilter) {\n          return (\n            old?.map(d => {\n              if (d.id === column.id) {\n                return newFilterObj\n              }\n              return d\n            }) ?? []\n          )\n        }\n\n        if (old?.length) {\n          return [...old, newFilterObj]\n        }\n\n        return [newFilterObj]\n      })\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    _table: Table<TData>\n  ): void => {\n    row.columnFilters = {}\n    row.columnFiltersMeta = {}\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnFilters = (updater: Updater<ColumnFiltersState>) => {\n      const leafColumns = table.getAllLeafColumns()\n\n      const updateFn = (old: ColumnFiltersState) => {\n        return functionalUpdate(updater, old)?.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id)\n\n          if (column) {\n            const filterFn = column.getFilterFn()\n\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false\n            }\n          }\n\n          return true\n        })\n      }\n\n      table.options.onColumnFiltersChange?.(updateFn)\n    }\n\n    table.resetColumnFilters = defaultState => {\n      table.setColumnFilters(\n        defaultState ? [] : table.initialState?.columnFilters ?? []\n      )\n    }\n\n    table.getPreFilteredRowModel = () => table.getCoreRowModel()\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n      }\n\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getFilteredRowModel()\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/ColumnGrouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport {\n  AggregationFns,\n  Cell,\n  Column,\n  ColumnDefTemplate,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  /**\n   * The cell to display each row for the column if the cell is an aggregate. If a function is passed, it will be passed a props object with the context of the cell and should return the property type for your adapter (the exact type depends on the adapter being used).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregatedcell)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  /**\n   * The resolved aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregationFn?: AggregationFnOption<TData>\n  /**\n   * Enables/disables grouping for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Specify a value to be used for grouping rows on this column. If this option is not specified, the value derived from `accessorKey` / `accessorFn` will be used instead.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  /**\n   * Returns the aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns the automatically inferred aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getautoaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getcangroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getCanGroup: () => boolean\n  /**\n   * Returns the index of the column in the grouping state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedIndex: () => number\n  /**\n   * Returns whether or not the column is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns a function that toggles the grouping state of the column. This is useful for passing to the `onClick` prop of a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#gettogglegroupinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getToggleGroupingHandler: () => () => void\n  /**\n   * Toggles the grouping state of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#togglegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  toggleGrouping: () => void\n}\n\nexport interface GroupingRow {\n  _groupingValuesCache: Record<string, any>\n  /**\n   * Returns the grouping value for any row and column (including leaf rows).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue: (columnId: string) => unknown\n  /**\n   * Returns whether or not the row is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * If this row is grouped, this is the id of the column that this row is grouped by.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingcolumnid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingColumnId?: string\n  /**\n   * If this row is grouped, this is the unique/shared value for the `groupingColumnId` for all of the rows in this group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingValue?: unknown\n}\n\nexport interface GroupingCell {\n  /**\n   * Returns whether or not the cell is currently aggregated.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisaggregated)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsAggregated: () => boolean\n  /**\n   * Returns whether or not the cell is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns whether or not the cell is currently a placeholder cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsPlaceholder: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  enableGrouping: boolean\n  onGroupingChange: OnChangeFn<GroupingState>\n}\n\ninterface GroupingOptionsBase {\n  /**\n   * Enables/disables grouping for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Returns the row model after grouping has taken place, but no further.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Grouping columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupedcolumnmode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupedColumnMode?: false | 'reorder' | 'remove'\n  /**\n   * Enables manual grouping. If this option is set to `true`, the table will not automatically group rows using `getGroupedRowModel()` and instead will expect you to manually group the rows before passing them to the table. This is useful if you are doing server-side grouping and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#manualgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  manualGrouping?: boolean\n  /**\n   * If this function is provided, it will be called when the grouping state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.grouping` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#ongroupingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  onGroupingChange?: OnChangeFn<GroupingState>\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  _getGroupedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getpregroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getPreGroupedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **grouping** state to `initialState.grouping`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#resetgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  resetGrouping: (defaultState?: boolean) => void\n  /**\n   * Updates the grouping state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#setgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  setGrouping: (updater: Updater<GroupingState>) => void\n}\n\n//\n\nexport const ColumnGrouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old?.includes(column.id)) {\n          return old.filter(d => d !== column.id)\n        }\n\n        return [...(old ?? []), column.id]\n      })\n    }\n\n    column.getCanGroup = () => {\n      return (\n        (column.columnDef.enableGrouping ?? true) &&\n        (table.options.enableGrouping ?? true) &&\n        (!!column.accessorFn || !!column.columnDef.getGroupingValue)\n      )\n    }\n\n    column.getIsGrouped = () => {\n      return table.getState().grouping?.includes(column.id)\n    }\n\n    column.getGroupedIndex = () => table.getState().grouping?.indexOf(column.id)\n\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup()\n\n      return () => {\n        if (!canGroup) return\n        column.toggleGrouping()\n      }\n    }\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'number') {\n        return aggregationFns.sum\n      }\n\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent\n      }\n    }\n    column.getAggregationFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.aggregationFn)\n        ? column.columnDef.aggregationFn\n        : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setGrouping = updater => table.options.onGroupingChange?.(updater)\n\n    table.resetGrouping = defaultState => {\n      table.setGrouping(defaultState ? [] : table.initialState?.grouping ?? [])\n    }\n\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel()\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n      }\n\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel()\n      }\n\n      return table._getGroupedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getIsGrouped = () => !!row.groupingColumnId\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.columnDef.getGroupingValue) {\n        return row.getValue(columnId)\n      }\n\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n        row.original\n      )\n\n      return row._groupingValuesCache[columnId]\n    }\n    row._groupingValuesCache = {}\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    cell.getIsGrouped = () =>\n      column.getIsGrouped() && column.id === row.groupingColumnId\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped()\n    cell.getIsAggregated = () =>\n      !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!row.subRows?.length\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nimport {\n  Column,\n  OnChangeFn,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\n\nimport { orderColumns } from './ColumnGrouping'\nimport { ColumnPinningPosition, _getVisibleLeafColumns } from '..'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnOrder` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#oncolumnorderchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderColumn {\n  /**\n   * Returns the index of the column in the order of the visible columns. Optionally pass a `position` parameter to get the index of the column in a sub-section of the table\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIndex: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns `true` if the column is the first column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the first in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getisfirstcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsFirstColumn: (position?: ColumnPinningPosition | 'center') => boolean\n  /**\n   * Returns `true` if the column is the last column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the last in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getislastcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsLastColumn: (position?: ColumnPinningPosition | 'center') => boolean\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n  /**\n   * Resets the **columnOrder** state to `initialState.columnOrder`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#resetcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  resetColumnOrder: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnOrder` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#setcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n}\n\n//\n\nexport const ColumnOrdering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getIndex = memo(\n      position => [_getVisibleLeafColumns(table, position)],\n      columns => columns.findIndex(d => d.id === column.id),\n      getMemoOptions(table.options, 'debugColumns', 'getIndex')\n    )\n    column.getIsFirstColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[0]?.id === column.id\n    }\n    column.getIsLastColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[columns.length - 1]?.id === column.id\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnOrder = updater =>\n      table.options.onColumnOrderChange?.(updater)\n    table.resetColumnOrder = defaultState => {\n      table.setColumnOrder(\n        defaultState ? [] : table.initialState.columnOrder ?? []\n      )\n    }\n    table._getOrderColumnsFn = memo(\n      () => [\n        table.getState().columnOrder,\n        table.getState().grouping,\n        table.options.groupedColumnMode,\n      ],\n      (columnOrder, grouping, groupedColumnMode) =>\n        (columns: Column<TData, unknown>[]) => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n      getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  /**\n   * Enables/disables column pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablecolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enableColumnPinning?: boolean\n  /**\n   * @deprecated Use `enableColumnPinning` or `enableRowPinning` instead.\n   * Enables/disables all pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnPinning` changes. This overrides the default internal state management, so you will also need to supply `state.columnPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#oncolumnpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/oncolumnpinningchange)\n   */\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  /**\n   * Enables/disables column pinning for this column. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  /**\n   * Returns whether or not the column can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcanpin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the column. (`'left'`, `'right'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getispinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsPinned: () => ColumnPinningPosition\n  /**\n   * Returns the numeric pinned index of the column within a pinned column group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getpinnedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a column to the `'left'` or `'right'`, or unpins the column to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#pin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcentervisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all left pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcenterleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether or not any columns are pinned. Optionally specify to only check for pinned columns in either the `left` or `right` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getissomecolumnspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  /**\n   * Returns all left pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the **columnPinning** state to `initialState.columnPinning`, or `true` can be passed to force a default blank state reset to `{ left: [], right: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#resetcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  resetColumnPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#setcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n}\n\n//\n\nconst getDefaultColumnPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const ColumnPinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.pin = position => {\n      const columnIds = column\n        .getLeafColumns()\n        .map(d => d.id)\n        .filter(Boolean) as string[]\n\n      table.setColumnPinning(old => {\n        if (position === 'right') {\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: [\n              ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n          }\n        }\n\n        if (position === 'left') {\n          return {\n            left: [\n              ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        }\n\n        return {\n          left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n          right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n        }\n      })\n    }\n\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns()\n\n      return leafColumns.some(\n        d =>\n          (d.columnDef.enablePinning ?? true) &&\n          (table.options.enableColumnPinning ??\n            table.options.enablePinning ??\n            true)\n      )\n    }\n\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n      const { left, right } = table.getState().columnPinning\n\n      const isLeft = leafColumnIds.some(d => left?.includes(d))\n      const isRight = leafColumnIds.some(d => right?.includes(d))\n\n      return isLeft ? 'left' : isRight ? 'right' : false\n    }\n\n    column.getPinnedIndex = () => {\n      const position = column.getIsPinned()\n\n      return position\n        ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n        : 0\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getCenterVisibleCells = memo(\n      () => [\n        row._getAllVisibleCells(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allCells, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allCells.filter(d => !leftAndRight.includes(d.column.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells')\n    )\n    row.getLeftVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.left],\n      (allCells, left) => {\n        const cells = (left ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'left' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells')\n    )\n    row.getRightVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n      (allCells, right) => {\n        const cells = (right ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'right' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnPinning = updater =>\n      table.options.onColumnPinningChange?.(updater)\n\n    table.resetColumnPinning = defaultState =>\n      table.setColumnPinning(\n        defaultState\n          ? getDefaultColumnPinningState()\n          : table.initialState?.columnPinning ?? getDefaultColumnPinningState()\n      )\n\n    table.getIsSomeColumnsPinned = position => {\n      const pinningState = table.getState().columnPinning\n\n      if (!position) {\n        return Boolean(pinningState.left?.length || pinningState.right?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table.getLeftLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n      (allColumns, left) => {\n        return (left ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns')\n    )\n\n    table.getRightLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n      (allColumns, right) => {\n        return (right ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns')\n    )\n\n    table.getCenterLeafColumns = memo(\n      () => [\n        table.getAllLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allColumns.filter(d => !leftAndRight.includes(d.id))\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns')\n    )\n  },\n}\n", "import { _getVisibleLeafColumns } from '..'\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>umn,\n  <PERSON>er,\n  OnChangeFn,\n  Table,\n  Updater,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\nimport { ColumnPinningPosition } from './ColumnPinning'\nimport { safelyAccessDocument } from '../utils/document'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  columnSizingStart: [string, number][]\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  startOffset: null | number\n  startSize: null | number\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport type ColumnResizeDirection = 'ltr' | 'rtl'\n\nexport interface ColumnSizingOptions {\n  /**\n   * Determines when the columnSizing state is updated. `onChange` updates the state when the user is dragging the resize handle. `onEnd` updates the state when the user releases the resize handle.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnresizemode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeMode?: ColumnResizeMode\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enablecolumnresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableColumnResizing?: boolean\n  /**\n   * Enables or disables right-to-left support for resizing the column. defaults to 'ltr'.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnResizeDirection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeDirection?: ColumnResizeDirection\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizing` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizing` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizingInfo` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizingInfo` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizinginfochange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport type ColumnSizingDefaultOptions = Pick<\n  ColumnSizingOptions,\n  | 'columnResizeMode'\n  | 'onColumnSizingChange'\n  | 'onColumnSizingInfoChange'\n  | 'columnResizeDirection'\n>\n\nexport interface ColumnSizingInstance {\n  /**\n   * If pinning, returns the total size of the center portion of the table by calculating the sum of the sizes of all unpinned/center leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcentertotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCenterTotalSize: () => number\n  /**\n   * Returns the total size of the left portion of the table by calculating the sum of the sizes of all left leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getlefttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getLeftTotalSize: () => number\n  /**\n   * Returns the total size of the right portion of the table by calculating the sum of the sizes of all right leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getrighttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getRightTotalSize: () => number\n  /**\n   * Returns the total size of the table by calculating the sum of the sizes of all leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#gettotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getTotalSize: () => number\n  /**\n   * Resets column sizing to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetColumnSizing: (defaultState?: boolean) => void\n  /**\n   * Resets column sizing info to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetheadersizeinfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  /**\n   * Sets the column sizing state using an updater function or a value. This will trigger the underlying `onColumnSizingChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  /**\n   * Sets the column sizing info state using an updater function or a value. This will trigger the underlying `onColumnSizingInfoChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizinginfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n}\n\nexport interface ColumnSizingColumnDef {\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enableresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableResizing?: boolean\n  /**\n   * The maximum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#maxsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  maxSize?: number\n  /**\n   * The minimum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#minsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  minSize?: number\n  /**\n   * The desired size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#size)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  size?: number\n}\n\nexport interface ColumnSizingColumn {\n  /**\n   * Returns `true` if the column can be resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcanresize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCanResize: () => boolean\n  /**\n   * Returns `true` if the column is currently being resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getisresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getIsResizing: () => boolean\n  /**\n   * Returns the current size of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding (left) headers in relation to the current column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all succeeding (right) headers in relation to the current column.\n   */\n  getAfter: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Resets the column to its initial size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  /**\n   * Returns an event handler function that can be used to resize the header. It can be used as an:\n   * - `onMouseDown` handler\n   * - `onTouchStart` handler\n   *\n   * The dragging and release events are automatically handled for you.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getresizehandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getResizeHandler: (context?: Document) => (event: unknown) => void\n  /**\n   * Returns the current size of the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getSize = () => {\n      const columnSize = table.getState().columnSizing[column.id]\n\n      return Math.min(\n        Math.max(\n          column.columnDef.minSize ?? defaultColumnSizing.minSize,\n          columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n        ),\n        column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n      )\n    }\n\n    column.getStart = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(0, column.getIndex(position))\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getStart')\n    )\n\n    column.getAfter = memo(\n      position => [\n        position,\n        _getVisibleLeafColumns(table, position),\n        table.getState().columnSizing,\n      ],\n      (position, columns) =>\n        columns\n          .slice(column.getIndex(position) + 1)\n          .reduce((sum, column) => sum + column.getSize(), 0),\n      getMemoOptions(table.options, 'debugColumns', 'getAfter')\n    )\n\n    column.resetSize = () => {\n      table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n        return rest\n      })\n    }\n    column.getCanResize = () => {\n      return (\n        (column.columnDef.enableResizing ?? true) &&\n        (table.options.enableColumnResizing ?? true)\n      )\n    }\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    header.getSize = () => {\n      let sum = 0\n\n      const recurse = (header: Header<TData, TValue>) => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse)\n        } else {\n          sum += header.column.getSize() ?? 0\n        }\n      }\n\n      recurse(header)\n\n      return sum\n    }\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1]!\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n      }\n\n      return 0\n    }\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id)\n      const canResize = column?.getCanResize()\n\n      return (e: unknown) => {\n        if (!column || !canResize) {\n          return\n        }\n\n        ;(e as any).persist?.()\n\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return\n          }\n        }\n\n        const startSize = header.getSize()\n\n        const columnSizingStart: [string, number][] = header\n          ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()])\n          : [[column.id, column.getSize()]]\n\n        const clientX = isTouchStartEvent(e)\n          ? Math.round(e.touches[0]!.clientX)\n          : (e as MouseEvent).clientX\n\n        const newColumnSizing: ColumnSizingState = {}\n\n        const updateOffset = (\n          eventType: 'move' | 'end',\n          clientXPos?: number\n        ) => {\n          if (typeof clientXPos !== 'number') {\n            return\n          }\n\n          table.setColumnSizingInfo(old => {\n            const deltaDirection =\n              table.options.columnResizeDirection === 'rtl' ? -1 : 1\n            const deltaOffset =\n              (clientXPos - (old?.startOffset ?? 0)) * deltaDirection\n            const deltaPercentage = Math.max(\n              deltaOffset / (old?.startSize ?? 0),\n              -0.999999\n            )\n\n            old.columnSizingStart.forEach(([columnId, headerSize]) => {\n              newColumnSizing[columnId] =\n                Math.round(\n                  Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                ) / 100\n            })\n\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage,\n            }\n          })\n\n          if (\n            table.options.columnResizeMode === 'onChange' ||\n            eventType === 'end'\n          ) {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing,\n            }))\n          }\n        }\n\n        const onMove = (clientXPos?: number) => updateOffset('move', clientXPos)\n\n        const onEnd = (clientXPos?: number) => {\n          updateOffset('end', clientXPos)\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: [],\n          }))\n        }\n\n        const contextDocument = safelyAccessDocument(_contextDocument)\n\n        const mouseEvents = {\n          moveHandler: (e: MouseEvent) => onMove(e.clientX),\n          upHandler: (e: MouseEvent) => {\n            contextDocument?.removeEventListener(\n              'mousemove',\n              mouseEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'mouseup',\n              mouseEvents.upHandler\n            )\n            onEnd(e.clientX)\n          },\n        }\n\n        const touchEvents = {\n          moveHandler: (e: TouchEvent) => {\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onMove(e.touches[0]!.clientX)\n            return false\n          },\n          upHandler: (e: TouchEvent) => {\n            contextDocument?.removeEventListener(\n              'touchmove',\n              touchEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'touchend',\n              touchEvents.upHandler\n            )\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onEnd(e.touches[0]?.clientX)\n          },\n        }\n\n        const passiveIfSupported = passiveEventSupported()\n          ? { passive: false }\n          : false\n\n        if (isTouchStartEvent(e)) {\n          contextDocument?.addEventListener(\n            'touchmove',\n            touchEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'touchend',\n            touchEvents.upHandler,\n            passiveIfSupported\n          )\n        } else {\n          contextDocument?.addEventListener(\n            'mousemove',\n            mouseEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'mouseup',\n            mouseEvents.upHandler,\n            passiveIfSupported\n          )\n        }\n\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id,\n        }))\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnSizing = updater =>\n      table.options.onColumnSizingChange?.(updater)\n    table.setColumnSizingInfo = updater =>\n      table.options.onColumnSizingInfoChange?.(updater)\n    table.resetColumnSizing = defaultState => {\n      table.setColumnSizing(\n        defaultState ? {} : table.initialState.columnSizing ?? {}\n      )\n    }\n    table.resetHeaderSizeInfo = defaultState => {\n      table.setColumnSizingInfo(\n        defaultState\n          ? getDefaultColumnSizingInfoState()\n          : table.initialState.columnSizingInfo ??\n              getDefaultColumnSizingInfoState()\n      )\n    }\n    table.getTotalSize = () =>\n      table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getLeftTotalSize = () =>\n      table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getCenterTotalSize = () =>\n      table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getRightTotalSize = () =>\n      table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "export function safelyAccessDocument(_document?: Document): Document | null {\n  return _document || (typeof document !== 'undefined' ? document : null)\n}\n\nexport function safelyAccessDocumentEvent(event: Event): Document | null {\n  return !!event &&\n    !!event.target &&\n    typeof event.target === 'object' &&\n    'ownerDocument' in event.target\n    ? (event.target.ownerDocument as Document | null)\n    : null\n}\n", "import { ColumnPinningPosition } from '..'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  /**\n   * Whether to enable column hiding. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#enablehiding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  enableHiding?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnVisibility` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#oncolumnvisibilitychange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n}\n\nexport type VisibilityDefaultOptions = Pick<\n  VisibilityOptions,\n  'onColumnVisibilityChange'\n>\n\nexport interface VisibilityInstance<TData extends RowData> {\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the unpinned/center portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcentervisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether all columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsAllColumnsVisible: () => boolean\n  /**\n   * Returns whether any columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getissomecolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsSomeColumnsVisible: () => boolean\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the left portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getleftvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the right portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getrightvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a handler for toggling the visibility of all columns, meant to be bound to a `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettoggleallcolumnsvisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Returns a flat array of columns that are visible, including parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a flat array of leaf-node columns that are visible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the column visibility state to the initial state. If `defaultState` is provided, the state will be reset to `{}`\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#resetcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  resetColumnVisibility: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnVisibility` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#setcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  /**\n   * Toggles the visibility of all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#toggleallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleAllColumnsVisible: (value?: boolean) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns an array of cells that account for column visibility for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  /**\n   * Returns whether the column can be hidden\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcanhide)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCanHide: () => boolean\n  /**\n   * Returns whether the column is visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsVisible: () => boolean\n  /**\n   * Returns a function that can be used to toggle the column visibility. This function can be used to bind to an event handler to a checkbox.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettogglevisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Toggles the visibility of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#togglevisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleVisibility: (value?: boolean) => void\n}\n\n//\n\nexport const ColumnVisibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value ?? !column.getIsVisible(),\n        }))\n      }\n    }\n    column.getIsVisible = () => {\n      const childColumns = column.columns\n      return (\n        (childColumns.length\n          ? childColumns.some(c => c.getIsVisible())\n          : table.getState().columnVisibility?.[column.id]) ?? true\n      )\n    }\n\n    column.getCanHide = () => {\n      return (\n        (column.columnDef.enableHiding ?? true) &&\n        (table.options.enableHiding ?? true)\n      )\n    }\n    column.getToggleVisibilityHandler = () => {\n      return (e: unknown) => {\n        column.toggleVisibility?.(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row._getAllVisibleCells = memo(\n      () => [row.getAllCells(), table.getState().columnVisibility],\n      cells => {\n        return cells.filter(cell => cell.column.getIsVisible())\n      },\n      getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells')\n    )\n    row.getVisibleCells = memo(\n      () => [\n        row.getLeftVisibleCells(),\n        row.getCenterVisibleCells(),\n        row.getRightVisibleCells(),\n      ],\n      (left, center, right) => [...left, ...center, ...right],\n      getMemoOptions(table.options, 'debugRows', 'getVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        getMemoOptions(table.options, 'debugColumns', key)\n      )\n    }\n\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod(\n      'getVisibleFlatColumns',\n      () => table.getAllFlatColumns()\n    )\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getVisibleLeafColumns',\n      () => table.getAllLeafColumns()\n    )\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getLeftVisibleLeafColumns',\n      () => table.getLeftLeafColumns()\n    )\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getRightVisibleLeafColumns',\n      () => table.getRightLeafColumns()\n    )\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getCenterVisibleLeafColumns',\n      () => table.getCenterLeafColumns()\n    )\n\n    table.setColumnVisibility = updater =>\n      table.options.onColumnVisibilityChange?.(updater)\n\n    table.resetColumnVisibility = defaultState => {\n      table.setColumnVisibility(\n        defaultState ? {} : table.initialState.columnVisibility ?? {}\n      )\n    }\n\n    table.toggleAllColumnsVisible = value => {\n      value = value ?? !table.getIsAllColumnsVisible()\n\n      table.setColumnVisibility(\n        table.getAllLeafColumns().reduce(\n          (obj, column) => ({\n            ...obj,\n            [column.id]: !value ? !column.getCanHide?.() : value,\n          }),\n          {}\n        )\n      )\n    }\n\n    table.getIsAllColumnsVisible = () =>\n      !table.getAllLeafColumns().some(column => !column.getIsVisible?.())\n\n    table.getIsSomeColumnsVisible = () =>\n      table.getAllLeafColumns().some(column => column.getIsVisible?.())\n\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllColumnsVisible(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nexport function _getVisibleLeafColumns<TData extends RowData>(\n  table: Table<TData>,\n  position?: ColumnPinningPosition | 'center'\n) {\n  return !position\n    ? table.getVisibleLeafColumns()\n    : position === 'center'\n      ? table.getCenterVisibleLeafColumns()\n      : position === 'left'\n        ? table.getLeftVisibleLeafColumns()\n        : table.getRightVisibleLeafColumns()\n}\n", "import { RowModel } from '..'\nimport { Table, RowData, TableFeature } from '../types'\n\nexport interface GlobalFacetingInstance<TData extends RowData> {\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model for the table after **global** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  /**\n   * Returns the faceted unique values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-faceting#getglobalfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-faceting)\n   */\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n}\n\n//\n\nexport const GlobalFaceting: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table._getGlobalFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, '__global__')\n\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getGlobalFacetedRowModel()\n    }\n\n    table._getGlobalFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, '__global__')\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return table._getGlobalFacetedUniqueValues()\n    }\n\n    table._getGlobalFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, '__global__')\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return\n      }\n\n      return table._getGlobalFacetedMinMaxValues()\n    }\n  },\n}\n", "import { FilterFn, FilterFnOption } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport interface GlobalFilterTableState {\n  globalFilter: any\n}\n\nexport interface GlobalFilterColumnDef {\n  /**\n   * Enables/disables the **global** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n}\n\nexport interface GlobalFilterColumn {\n  /**\n   * Returns whether or not the column can be **globally** filtered. Set to `false` to disable a column from being scanned during global filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getCanGlobalFilter: () => boolean\n}\n\nexport interface GlobalFilterOptions<TData extends RowData> {\n  /**\n   * Enables/disables **global** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  enableGlobalFilter?: boolean\n  /**\n   * If provided, this function will be called with the column and should return `true` or `false` to indicate whether this column should be used for global filtering.\n   *\n   * This is useful if the column can contain data that is not `string` or `number` (i.e. `undefined`).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getcolumncanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n  /**\n   * The filter function to use for global filtering.\n   * - A `string` referencing a built-in filter function\n   * - A `string` that references a custom filter functions provided via the `tableOptions.filterFns` option\n   * - A custom filter function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#globalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  globalFilterFn?: FilterFnOption<TData>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.globalFilter` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#onglobalfilterchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  onGlobalFilterChange?: OnChangeFn<any>\n}\n\nexport interface GlobalFilterInstance<TData extends RowData> {\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#getglobalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/global-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/global-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const GlobalFiltering: TableFeature = {\n  getInitialState: (state): GlobalFilterTableState => {\n    return {\n      globalFilter: undefined,\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GlobalFilterOptions<TData> => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as GlobalFilterOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getCanGlobalFilter = () => {\n      return (\n        (column.columnDef.enableGlobalFilter ?? true) &&\n        (table.options.enableGlobalFilter ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n        !!column.accessorFn\n      )\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString\n    }\n\n    table.getGlobalFilterFn = () => {\n      const { globalFilterFn: globalFilterFn } = table.options\n\n      return isFunction(globalFilterFn)\n        ? globalFilterFn\n        : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n    }\n\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange?.(updater)\n    }\n\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(\n        defaultState ? undefined : table.initialState.globalFilter\n      )\n    }\n  },\n}\n", "import { RowModel } from '..'\nimport {\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  /**\n   * Returns whether the row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanExpand: () => boolean\n  /**\n   * Returns whether all parent rows of the row are expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallparentsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllParentsExpanded: () => boolean\n  /**\n   * Returns whether the row is expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsExpanded: () => boolean\n  /**\n   * Returns a function that can be used to toggle the expanded state of the row. This function can be used to bind to an event handler to a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleExpandedHandler: () => () => void\n  /**\n   * Toggles the expanded state (or sets it if `expanded` is provided) for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleExpanded: (expanded?: boolean) => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  /**\n   * Enable this setting to automatically reset the expanded state of the table when expanding state changes.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#autoresetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  autoResetExpanded?: boolean\n  /**\n   * Enable/disable expanding for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#enableexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  enableExpanding?: boolean\n  /**\n   * This function is responsible for returning the expanded row model. If this function is not provided, the table will not expand rows. You can use the default exported `getExpandedRowModel` function to get the expanded row model or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row is currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisrowexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getrowcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  /**\n   * Enables manual row expansion. If this is set to `true`, `getExpandedRowModel` will not be used to expand rows and you would be expected to perform the expansion in your own data model. This is useful if you are doing server-side expansion.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#manualexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  manualExpanding?: boolean\n  /**\n   * This function is called when the `expanded` table state changes. If a function is provided, you will be responsible for managing this state on your own. To pass the managed state back to the table, use the `tableOptions.state.expanded` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#onexpandedchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  /**\n   * If `true` expanded rows will be paginated along with the rest of the table (which means expanded rows may span multiple pages). If `false` expanded rows will not be considered for pagination (which means expanded rows will always render on their parents page. This also means more rows will be rendered than the set page size)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#paginateexpandedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  _getExpandedRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether there are any rows that can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcansomerowsexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanSomeRowsExpand: () => boolean\n  /**\n   * Returns the maximum depth of the expanded rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandeddepth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedDepth: () => number\n  /**\n   * Returns the row model after expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether all rows are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllRowsExpanded: () => boolean\n  /**\n   * Returns whether there are any rows that are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getissomerowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsSomeRowsExpanded: () => boolean\n  /**\n   * Returns the row model before expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getpreexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getPreExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle the expanded state of all rows. This handler is meant to be used with an `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleallrowsexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  /**\n   * Resets the expanded state of the table to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#resetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  resetExpanded: (defaultState?: boolean) => void\n  /**\n   * Updates the expanded state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#setexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  /**\n   * Toggles the expanded state for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n}\n\n//\n\nexport const RowExpanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetExpanded = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetExpanded ??\n        !table.options.manualExpanding\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetExpanded()\n          queued = false\n        })\n      }\n    }\n    table.setExpanded = updater => table.options.onExpandedChange?.(updater)\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded ?? !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true)\n      } else {\n        table.setExpanded({})\n      }\n    }\n    table.resetExpanded = defaultState => {\n      table.setExpanded(defaultState ? {} : table.initialState?.expanded ?? {})\n    }\n    table.getCanSomeRowsExpand = () => {\n      return table\n        .getPrePaginationRowModel()\n        .flatRows.some(row => row.getCanExpand())\n    }\n    table.getToggleAllRowsExpandedHandler = () => {\n      return (e: unknown) => {\n        ;(e as any).persist?.()\n        table.toggleAllRowsExpanded()\n      }\n    }\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded\n      return expanded === true || Object.values(expanded).some(Boolean)\n    }\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true\n      }\n\n      if (!Object.keys(expanded).length) {\n        return false\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false\n      }\n\n      // They must all be expanded :shrug:\n      return true\n    }\n    table.getExpandedDepth = () => {\n      let maxDepth = 0\n\n      const rowIds =\n        table.getState().expanded === true\n          ? Object.keys(table.getRowModel().rowsById)\n          : Object.keys(table.getState().expanded)\n\n      rowIds.forEach(id => {\n        const splitId = id.split('.')\n        maxDepth = Math.max(maxDepth, splitId.length)\n      })\n\n      return maxDepth\n    }\n    table.getPreExpandedRowModel = () => table.getSortedRowModel()\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n      }\n\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel()\n      }\n\n      return table._getExpandedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        const exists = old === true ? true : !!old?.[row.id]\n\n        let oldExpanded: ExpandedStateList = {}\n\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true\n          })\n        } else {\n          oldExpanded = old\n        }\n\n        expanded = expanded ?? !exists\n\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true,\n          }\n        }\n\n        if (exists && !expanded) {\n          const { [row.id]: _, ...rest } = oldExpanded\n          return rest\n        }\n\n        return old\n      })\n    }\n    row.getIsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      return !!(\n        table.options.getIsRowExpanded?.(row) ??\n        (expanded === true || expanded?.[row.id])\n      )\n    }\n    row.getCanExpand = () => {\n      return (\n        table.options.getRowCanExpand?.(row) ??\n        ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n      )\n    }\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true\n      let currentRow = row\n\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true)\n        isFullyExpanded = currentRow.getIsExpanded()\n      }\n\n      return isFullyExpanded\n    }\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand()\n\n      return () => {\n        if (!canExpand) return\n        row.toggleExpanded()\n      }\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  RowModel,\n  Updater,\n  <PERSON>Data,\n  TableFeature,\n} from '../types'\nimport {\n  functionalUpdate,\n  getMemoOptions,\n  makeStateUpdater,\n  memo,\n} from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  /**\n   * If set to `true`, pagination will be reset to the first page when page-altering state changes eg. `data` is updated, filters change, grouping changes, etc.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#autoresetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  autoResetPageIndex?: boolean\n  /**\n   * Returns the row model after pagination has taken place, but no further.\n   *\n   * Pagination columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Enables manual pagination. If this option is set to `true`, the table will not automatically paginate rows using `getPaginationRowModel()` and instead will expect you to manually paginate the rows before passing them to the table. This is useful if you are doing server-side pagination and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#manualpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  manualPagination?: boolean\n  /**\n   * If this function is provided, it will be called when the pagination state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.pagination` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#onpaginationchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  onPaginationChange?: OnChangeFn<PaginationState>\n  /**\n   * When manually controlling pagination, you can supply a total `pageCount` value to the table if you know it (Or supply a `rowCount` and `pageCount` will be calculated). If you do not know how many pages there are, you can set this to `-1`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#pagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  pageCount?: number\n  /**\n   * When manually controlling pagination, you can supply a total `rowCount` value to the table if you know it. The `pageCount` can be calculated from this value and the `pageSize`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#rowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  rowCount?: number\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  _getPaginationRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether the table can go to the next page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcannextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanNextPage: () => boolean\n  /**\n   * Returns whether the table can go to the previous page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcanpreviouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanPreviousPage: () => boolean\n  /**\n   * Returns the page count. If manually paginating or controlling the pagination state, this will come directly from the `options.pageCount` table option, otherwise it will be calculated from the table data using the total row count and current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageCount: () => number\n  /**\n   * Returns the row count. If manually paginating or controlling the pagination state, this will come directly from the `options.rowCount` table option, otherwise it will be calculated from the table data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getrowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getRowCount: () => number\n  /**\n   * Returns an array of page options (zero-index-based) for the current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpageoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageOptions: () => number[]\n  /**\n   * Returns the row model for the table after pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getprepaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPrePaginationRowModel: () => RowModel<TData>\n  /**\n   * Increments the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#nextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  nextPage: () => void\n  /**\n   * Decrements the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#previouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  previousPage: () => void\n  /**\n   * Sets the page index to `0`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#firstpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  firstPage: () => void\n  /**\n   * Sets the page index to the last page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#lastpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  lastPage: () => void\n  /**\n   * Resets the page index to its initial state. If `defaultState` is `true`, the page index will be reset to `0` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageIndex: (defaultState?: boolean) => void\n  /**\n   * Resets the page size to its initial state. If `defaultState` is `true`, the page size will be reset to `10` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageSize: (defaultState?: boolean) => void\n  /**\n   * Resets the **pagination** state to `initialState.pagination`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPagination: (defaultState?: boolean) => void\n  /**\n   * @deprecated The page count no longer exists in the pagination state. Just pass as a table option instead.\n   */\n  setPageCount: (updater: Updater<number>) => void\n  /**\n   * Updates the page index using the provided function or value in the `state.pagination.pageIndex` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageIndex: (updater: Updater<number>) => void\n  /**\n   * Updates the page size using the provided function or value in the `state.pagination.pageSize` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageSize: (updater: Updater<number>) => void\n  /**\n   * Sets or updates the `state.pagination` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPagination: (updater: Updater<PaginationState>) => void\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const RowPagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetPageIndex = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetPageIndex ??\n        !table.options.manualPagination\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetPageIndex()\n          queued = false\n        })\n      }\n    }\n    table.setPagination = updater => {\n      const safeUpdater: Updater<PaginationState> = old => {\n        let newState = functionalUpdate(updater, old)\n\n        return newState\n      }\n\n      return table.options.onPaginationChange?.(safeUpdater)\n    }\n    table.resetPagination = defaultState => {\n      table.setPagination(\n        defaultState\n          ? getDefaultPaginationState()\n          : table.initialState.pagination ?? getDefaultPaginationState()\n      )\n    }\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n        const maxPageIndex =\n          typeof table.options.pageCount === 'undefined' ||\n          table.options.pageCount === -1\n            ? Number.MAX_SAFE_INTEGER\n            : table.options.pageCount - 1\n\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n        return {\n          ...old,\n          pageIndex,\n        }\n      })\n    }\n    table.resetPageIndex = defaultState => {\n      table.setPageIndex(\n        defaultState\n          ? defaultPageIndex\n          : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n      )\n    }\n    table.resetPageSize = defaultState => {\n      table.setPageSize(\n        defaultState\n          ? defaultPageSize\n          : table.initialState?.pagination?.pageSize ?? defaultPageSize\n      )\n    }\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n        const topRowIndex = old.pageSize * old.pageIndex!\n        const pageIndex = Math.floor(topRowIndex / pageSize)\n\n        return {\n          ...old,\n          pageIndex,\n          pageSize,\n        }\n      })\n    }\n    //deprecated\n    table.setPageCount = updater =>\n      table.setPagination(old => {\n        let newPageCount = functionalUpdate(\n          updater,\n          table.options.pageCount ?? -1\n        )\n\n        if (typeof newPageCount === 'number') {\n          newPageCount = Math.max(-1, newPageCount)\n        }\n\n        return {\n          ...old,\n          pageCount: newPageCount,\n        }\n      })\n\n    table.getPageOptions = memo(\n      () => [table.getPageCount()],\n      pageCount => {\n        let pageOptions: number[] = []\n        if (pageCount && pageCount > 0) {\n          pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n        }\n        return pageOptions\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPageOptions')\n    )\n\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0\n\n    table.getCanNextPage = () => {\n      const { pageIndex } = table.getState().pagination\n\n      const pageCount = table.getPageCount()\n\n      if (pageCount === -1) {\n        return true\n      }\n\n      if (pageCount === 0) {\n        return false\n      }\n\n      return pageIndex < pageCount - 1\n    }\n\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1)\n    }\n\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1\n      })\n    }\n\n    table.firstPage = () => {\n      return table.setPageIndex(0)\n    }\n\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1)\n    }\n\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel()\n    table.getPaginationRowModel = () => {\n      if (\n        !table._getPaginationRowModel &&\n        table.options.getPaginationRowModel\n      ) {\n        table._getPaginationRowModel =\n          table.options.getPaginationRowModel(table)\n      }\n\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel()\n      }\n\n      return table._getPaginationRowModel()\n    }\n\n    table.getPageCount = () => {\n      return (\n        table.options.pageCount ??\n        Math.ceil(table.getRowCount() / table.getState().pagination.pageSize)\n      )\n    }\n\n    table.getRowCount = () => {\n      return (\n        table.options.rowCount ?? table.getPrePaginationRowModel().rows.length\n      )\n    }\n  },\n}\n", "import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Row,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowPinningPosition = false | 'top' | 'bottom'\n\nexport interface RowPinningState {\n  bottom?: string[]\n  top?: string[]\n}\n\nexport interface RowPinningTableState {\n  rowPinning: RowPinningState\n}\n\nexport interface RowPinningOptions<TData extends RowData> {\n  /**\n   * Enables/disables row pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#enablerowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  enableRowPinning?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * When `false`, pinned rows will not be visible if they are filtered or paginated out of the table. When `true`, pinned rows will always be visible regardless of filtering or pagination. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#keeppinnedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  keepPinnedRows?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowPinning` changes. This overrides the default internal state management, so you will also need to supply `state.rowPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#onrowpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/onrowpinningchange)\n   */\n  onRowPinningChange?: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningDefaultOptions {\n  onRowPinningChange: OnChangeFn<RowPinningState>\n}\n\nexport interface RowPinningRow {\n  /**\n   * Returns whether or not the row can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcanpin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the row. (`'top'`, `'bottom'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getispinned-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsPinned: () => RowPinningPosition\n  /**\n   * Returns the numeric pinned index of the row within a pinned row group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getpinnedindex-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a row to the `'top'` or `'bottom'`, or unpins the row to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#pin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  pin: (\n    position: RowPinningPosition,\n    includeLeafRows?: boolean,\n    includeParentRows?: boolean\n  ) => void\n}\n\nexport interface RowPinningInstance<TData extends RowData> {\n  _getPinnedRows: (\n    visiblePinnedRows: Array<Row<TData>>,\n    pinnedRowIds: Array<string> | undefined,\n    position: 'top' | 'bottom'\n  ) => Row<TData>[]\n  /**\n   * Returns all bottom pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getbottomrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getBottomRows: () => Row<TData>[]\n  /**\n   * Returns all rows that are not pinned to the top or bottom.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getcenterrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getCenterRows: () => Row<TData>[]\n  /**\n   * Returns whether or not any rows are pinned. Optionally specify to only check for pinned rows in either the `top` or `bottom` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#getissomerowspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getIsSomeRowsPinned: (position?: RowPinningPosition) => boolean\n  /**\n   * Returns all top pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#gettoprows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  getTopRows: () => Row<TData>[]\n  /**\n   * Resets the **rowPinning** state to `initialState.rowPinning`, or `true` can be passed to force a default blank state reset to `{ top: [], bottom: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#resetrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  resetRowPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-pinning#setrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-pinning)\n   */\n  setRowPinning: (updater: Updater<RowPinningState>) => void\n}\n\n//\n\nconst getDefaultRowPinningState = (): RowPinningState => ({\n  top: [],\n  bottom: [],\n})\n\nexport const RowPinning: TableFeature = {\n  getInitialState: (state): RowPinningTableState => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowPinningDefaultOptions => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows\n        ? row.getLeafRows().map(({ id }) => id)\n        : []\n      const parentRowIds = includeParentRows\n        ? row.getParentRows().map(({ id }) => id)\n        : []\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds])\n\n      table.setRowPinning(old => {\n        if (position === 'bottom') {\n          return {\n            top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n            bottom: [\n              ...(old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n          }\n        }\n\n        if (position === 'top') {\n          return {\n            top: [\n              ...(old?.top ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n            bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n          }\n        }\n\n        return {\n          top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n          bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n        }\n      })\n    }\n    row.getCanPin = () => {\n      const { enableRowPinning, enablePinning } = table.options\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row)\n      }\n      return enableRowPinning ?? enablePinning ?? true\n    }\n    row.getIsPinned = () => {\n      const rowIds = [row.id]\n\n      const { top, bottom } = table.getState().rowPinning\n\n      const isTop = rowIds.some(d => top?.includes(d))\n      const isBottom = rowIds.some(d => bottom?.includes(d))\n\n      return isTop ? 'top' : isBottom ? 'bottom' : false\n    }\n    row.getPinnedIndex = () => {\n      const position = row.getIsPinned()\n      if (!position) return -1\n\n      const visiblePinnedRowIds = (\n        position === 'top' ? table.getTopRows() : table.getBottomRows()\n      )?.map(({ id }) => id)\n\n      return visiblePinnedRowIds?.indexOf(row.id) ?? -1\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowPinning = updater => table.options.onRowPinningChange?.(updater)\n\n    table.resetRowPinning = defaultState =>\n      table.setRowPinning(\n        defaultState\n          ? getDefaultRowPinningState()\n          : table.initialState?.rowPinning ?? getDefaultRowPinningState()\n      )\n\n    table.getIsSomeRowsPinned = position => {\n      const pinningState = table.getState().rowPinning\n\n      if (!position) {\n        return Boolean(pinningState.top?.length || pinningState.bottom?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      const rows =\n        table.options.keepPinnedRows ?? true\n          ? //get all rows that are pinned even if they would not be otherwise visible\n            //account for expanded parent rows, but not pagination or filtering\n            (pinnedRowIds ?? []).map(rowId => {\n              const row = table.getRow(rowId, true)\n              return row.getIsAllParentsExpanded() ? row : null\n            })\n          : //else get only visible rows that are pinned\n            (pinnedRowIds ?? []).map(\n              rowId => visibleRows.find(row => row.id === rowId)!\n            )\n\n      return rows.filter(Boolean).map(d => ({ ...d, position })) as Row<TData>[]\n    }\n\n    table.getTopRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.top],\n      (allRows, topPinnedRowIds) =>\n        table._getPinnedRows(allRows, topPinnedRowIds, 'top'),\n      getMemoOptions(table.options, 'debugRows', 'getTopRows')\n    )\n\n    table.getBottomRows = memo(\n      () => [table.getRowModel().rows, table.getState().rowPinning.bottom],\n      (allRows, bottomPinnedRowIds) =>\n        table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'),\n      getMemoOptions(table.options, 'debugRows', 'getBottomRows')\n    )\n\n    table.getCenterRows = memo(\n      () => [\n        table.getRowModel().rows,\n        table.getState().rowPinning.top,\n        table.getState().rowPinning.bottom,\n      ],\n      (allRows, top, bottom) => {\n        const topAndBottom = new Set([...(top ?? []), ...(bottom ?? [])])\n        return allRows.filter(d => !topAndBottom.has(d.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterRows')\n    )\n  },\n}\n", "import {\n  OnChangeFn,\n  Table,\n  Row,\n  <PERSON>Model,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  /**\n   * - Enables/disables multiple row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable multiple row selection for that row's children/grandchildren\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablemultirowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * - Enables/disables row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable row selection for that row\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablerowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * Enables/disables automatic sub-row selection when a parent row is selected, or a function that enables/disables automatic sub-row selection for each row.\n   * (Use in combination with expanding or grouping features)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablesubrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowSelection` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#onrowselectionchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  /**\n   * Returns whether or not the row can multi-select.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanmultiselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanMultiSelect: () => boolean\n  /**\n   * Returns whether or not the row can be selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelect: () => boolean\n  /**\n   * Returns whether or not the row can select sub rows automatically when the parent row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselectsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelectSubRows: () => boolean\n  /**\n   * Returns whether or not all of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallsubrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllSubRowsSelected: () => boolean\n  /**\n   * Returns whether or not the row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSelected: () => boolean\n  /**\n   * Returns whether or not some of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomeselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeSelected: () => boolean\n  /**\n   * Returns a handler that can be used to toggle the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleSelectedHandler: () => (event: unknown) => void\n  /**\n   * Selects/deselects the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleSelected: (value?: boolean, opts?: { selectChildren?: boolean }) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  /**\n   * Returns the row model of all rows that are selected after filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getfilteredselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getgroupedselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getGroupedSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether or not all rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllPageRowsSelected: () => boolean\n  /**\n   * Returns whether or not all rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomepagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomePageRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeRowsSelected: () => boolean\n  /**\n   * Returns the core row model of all rows before row selection has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getpreselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getPreSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallpagerowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Returns a handler that can be used to toggle all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallrowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Resets the **rowSelection** state to the `initialState.rowSelection`, or `true` can be passed to force a default blank state reset to `{}`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#resetrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  resetRowSelection: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowSelection` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#setrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  /**\n   * Selects/deselects all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  /**\n   * Selects/deselects all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllRowsSelected: (value?: boolean) => void\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowSelection = updater =>\n      table.options.onRowSelectionChange?.(updater)\n    table.resetRowSelection = defaultState =>\n      table.setRowSelection(\n        defaultState ? {} : table.initialState.rowSelection ?? {}\n      )\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value =\n          typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n        const rowSelection = { ...old }\n\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return\n            }\n            rowSelection[row.id] = true\n          })\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id]\n          })\n        }\n\n        return rowSelection\n      })\n    }\n    table.toggleAllPageRowsSelected = value =>\n      table.setRowSelection(old => {\n        const resolvedValue =\n          typeof value !== 'undefined'\n            ? value\n            : !table.getIsAllPageRowsSelected()\n\n        const rowSelection: RowSelectionState = { ...old }\n\n        table.getRowModel().rows.forEach(row => {\n          mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table)\n        })\n\n        return rowSelection\n      })\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel()\n    table.getSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getCoreRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel')\n    )\n\n    table.getFilteredSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getFilteredRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel')\n    )\n\n    table.getGroupedSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getSortedRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel')\n    )\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n      const { rowSelection } = table.getState()\n\n      let isAllRowsSelected = Boolean(\n        preGroupedFlatRows.length && Object.keys(rowSelection).length\n      )\n\n      if (isAllRowsSelected) {\n        if (\n          preGroupedFlatRows.some(\n            row => row.getCanSelect() && !rowSelection[row.id]\n          )\n        ) {\n          isAllRowsSelected = false\n        }\n      }\n\n      return isAllRowsSelected\n    }\n\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table\n        .getPaginationRowModel()\n        .flatRows.filter(row => row.getCanSelect())\n      const { rowSelection } = table.getState()\n\n      let isAllPageRowsSelected = !!paginationFlatRows.length\n\n      if (\n        isAllPageRowsSelected &&\n        paginationFlatRows.some(row => !rowSelection[row.id])\n      ) {\n        isAllPageRowsSelected = false\n      }\n\n      return isAllPageRowsSelected\n    }\n\n    table.getIsSomeRowsSelected = () => {\n      const totalSelected = Object.keys(\n        table.getState().rowSelection ?? {}\n      ).length\n      return (\n        totalSelected > 0 &&\n        totalSelected < table.getFilteredRowModel().flatRows.length\n      )\n    }\n\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows\n      return table.getIsAllPageRowsSelected()\n        ? false\n        : paginationFlatRows\n            .filter(row => row.getCanSelect())\n            .some(d => d.getIsSelected() || d.getIsSomeSelected())\n    }\n\n    table.getToggleAllRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllPageRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected()\n\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !isSelected\n\n        if (row.getCanSelect() && isSelected === value) {\n          return old\n        }\n\n        const selectedRowIds = { ...old }\n\n        mutateRowIsSelected(\n          selectedRowIds,\n          row.id,\n          value,\n          opts?.selectChildren ?? true,\n          table\n        )\n\n        return selectedRowIds\n      })\n    }\n    row.getIsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isRowSelected(row, rowSelection)\n    }\n\n    row.getIsSomeSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'some'\n    }\n\n    row.getIsAllSubRowsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'all'\n    }\n\n    row.getCanSelect = () => {\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row)\n      }\n\n      return table.options.enableRowSelection ?? true\n    }\n\n    row.getCanSelectSubRows = () => {\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row)\n      }\n\n      return table.options.enableSubRowSelection ?? true\n    }\n\n    row.getCanMultiSelect = () => {\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row)\n      }\n\n      return table.options.enableMultiRowSelection ?? true\n    }\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect()\n\n      return (e: unknown) => {\n        if (!canSelect) return\n        row.toggleSelected(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  includeChildren: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id, true)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (includeChildren && row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (!row.subRows?.length) return false\n\n  let allChildrenSelected = true\n  let someSelected = false\n\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return\n    }\n\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection, table)\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true\n        allChildrenSelected = false\n      } else {\n        allChildrenSelected = false\n      }\n    }\n  })\n\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n}\n", "import { SortingFn } from './features/RowSorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { RowModel } from '..'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n  TableFeature,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  desc: boolean\n  id: string\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  /**\n   * Enables/Disables multi-sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  invertSorting?: boolean\n  /**\n   * Set to `true` for sorting toggles on this column to start in the descending direction.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n  /**\n   * The sorting function to use with this column.\n   * - A `string` referencing a built-in sorting function\n   * - A custom sorting function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortingFn?: SortingFnOption<TData>\n  /**\n   * The priority of undefined values when sorting this column.\n   * - `false`\n   *   - Undefined values will be considered tied and need to be sorted by the next column filter or original index (whichever applies)\n   * - `-1`\n   *   - Undefined values will be sorted with higher priority (ascending) (if ascending, undefined will appear on the beginning of the list)\n   * - `1`\n   *   - Undefined values will be sorted with lower priority (descending) (if ascending, undefined will appear on the end of the list)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortundefined)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortUndefined?: false | -1 | 1 | 'first' | 'last'\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  /**\n   * Removes this column from the table's sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#clearsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  clearSorting: () => void\n  /**\n   * Returns a sort direction automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortDir: () => SortDirection\n  /**\n   * Returns a sorting function automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortingFn: () => SortingFn<TData>\n  /**\n   * Returns whether this column can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcanmultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanMultiSort: () => boolean\n  /**\n   * Returns whether this column can be sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcansort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanSort: () => boolean\n  /**\n   * Returns the first direction that should be used when sorting this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getfirstsortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getFirstSortDir: () => SortDirection\n  /**\n   * Returns the current sort direction of this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getissorted)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getIsSorted: () => false | SortDirection\n  /**\n   * Returns the next sorting order.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getnextsortingorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getNextSortingOrder: () => SortDirection | false\n  /**\n   * Returns the index position of this column's sorting within the sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortIndex: () => number\n  /**\n   * Returns the resolved sorting function to be used for this column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortingFn: () => SortingFn<TData>\n  /**\n   * Returns a function that can be used to toggle this column's sorting state. This is useful for attaching a click handler to the column header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#gettogglesortinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n  /**\n   * Toggles this columns sorting state. If `desc` is provided, it will force the sort direction to that value. If `isMulti` is provided, it will additivity multi-sort the column (or toggle it if it is already sorted).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#togglesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n}\n\ninterface SortingOptionsBase {\n  /**\n   * Enables/disables the ability to remove multi-sorts\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultiremove)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiRemove?: boolean\n  /**\n   * Enables/Disables multi-sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Enables/Disables the ability to remove sorting for the table.\n   * - If `true` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'none' -> ...\n   * - If `false` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'desc' -> 'asc' -> ...\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesortingremoval)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSortingRemoval?: boolean\n  /**\n   * This function is used to retrieve the sorted row model. If using server-side sorting, this function is not required. To use client-side sorting, pass the exported `getSortedRowModel()` from your adapter to your table or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Pass a custom function that will be used to determine if a multi-sort event should be triggered. It is passed the event from the sort toggle handler and should return `true` if the event should trigger a multi-sort.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#ismultisortevent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  isMultiSortEvent?: (e: unknown) => boolean\n  /**\n   * Enables manual sorting for the table. If this is `true`, you will be expected to sort your data before it is passed to the table. This is useful if you are doing server-side sorting.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#manualsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  manualSorting?: boolean\n  /**\n   * Set a maximum number of columns that can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#maxmultisortcolcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  maxMultiSortColCount?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.sorting` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#onsortingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  onSortingChange?: OnChangeFn<SortingState>\n  /**\n   * If `true`, all sorts will default to descending as their first toggle state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  _getSortedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getpresortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getPreSortedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **sorting** state to `initialState.sorting`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#resetsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  resetSorting: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.sorting` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#setsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  setSorting: (updater: Updater<SortingState>) => void\n}\n\n//\n\nexport const RowSorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n      let isString = false\n\n      for (const row of firstRows) {\n        const value = row?.getValue(column.id)\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime\n        }\n\n        if (typeof value === 'string') {\n          isString = true\n\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric\n          }\n        }\n      }\n\n      if (isString) {\n        return sortingFns.text\n      }\n\n      return sortingFns.basic\n    }\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return 'asc'\n      }\n\n      return 'desc'\n    }\n    column.getSortingFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.sortingFn)\n        ? column.columnDef.sortingFn\n        : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n    }\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder()\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old?.find(d => d.id === column.id)\n        const existingIndex = old?.findIndex(d => d.id === column.id)\n\n        let newSorting: SortingState = []\n\n        // What should we do with this sort action?\n        let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n        // Multi-mode\n        if (old?.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'add'\n          }\n        } else {\n          // Normal mode\n          if (old?.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace'\n          } else if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'replace'\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove'\n            }\n          }\n        }\n\n        if (sortAction === 'add') {\n          newSorting = [\n            ...old,\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n          // Take latest n columns\n          newSorting.splice(\n            0,\n            newSorting.length -\n              (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n          )\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc,\n              }\n            }\n            return d\n          })\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id)\n        } else {\n          newSorting = [\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n        }\n\n        return newSorting\n      })\n    }\n\n    column.getFirstSortDir = () => {\n      const sortDescFirst =\n        column.columnDef.sortDescFirst ??\n        table.options.sortDescFirst ??\n        column.getAutoSortDir() === 'desc'\n      return sortDescFirst ? 'desc' : 'asc'\n    }\n\n    column.getNextSortingOrder = (multi?: boolean) => {\n      const firstSortDirection = column.getFirstSortDir()\n      const isSorted = column.getIsSorted()\n\n      if (!isSorted) {\n        return firstSortDirection\n      }\n\n      if (\n        isSorted !== firstSortDirection &&\n        (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n        (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc'\n    }\n\n    column.getCanSort = () => {\n      return (\n        (column.columnDef.enableSorting ?? true) &&\n        (table.options.enableSorting ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanMultiSort = () => {\n      return (\n        column.columnDef.enableMultiSort ??\n        table.options.enableMultiSort ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsSorted = () => {\n      const columnSort = table.getState().sorting?.find(d => d.id === column.id)\n\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n    }\n\n    column.getSortIndex = () =>\n      table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1\n\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old =>\n        old?.length ? old.filter(d => d.id !== column.id) : []\n      )\n    }\n\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort()\n\n      return (e: unknown) => {\n        if (!canSort) return\n        ;(e as any).persist?.()\n        column.toggleSorting?.(\n          undefined,\n          column.getCanMultiSort() ? table.options.isMultiSortEvent?.(e) : false\n        )\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setSorting = updater => table.options.onSortingChange?.(updater)\n    table.resetSorting = defaultState => {\n      table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n    }\n    table.getPreSortedRowModel = () => table.getGroupedRowModel()\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table)\n      }\n\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel()\n      }\n\n      return table._getSortedRowModel()\n    }\n  },\n}\n", "import { functionalUpdate, getMemoOptions, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n  TableFeature,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnFaceting } from '../features/ColumnFaceting'\nimport { ColumnFiltering } from '../features/ColumnFiltering'\nimport { ColumnGrouping } from '../features/ColumnGrouping'\nimport { ColumnOrdering } from '../features/ColumnOrdering'\nimport { ColumnPinning } from '../features/ColumnPinning'\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { ColumnVisibility } from '../features/ColumnVisibility'\nimport { GlobalFaceting } from '../features/GlobalFaceting'\nimport { GlobalFiltering } from '../features/GlobalFiltering'\nimport { RowExpanding } from '../features/RowExpanding'\nimport { RowPagination } from '../features/RowPagination'\nimport { RowPinning } from '../features/RowPinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { RowSorting } from '../features/RowSorting'\n\nconst builtInFeatures = [\n  Headers,\n  ColumnVisibility,\n  ColumnOrdering,\n  ColumnPinning,\n  ColumnFaceting,\n  ColumnFiltering,\n  GlobalFaceting, //depends on ColumnFaceting\n  GlobalFiltering, //depends on ColumnFiltering\n  RowSorting,\n  ColumnGrouping, //depends on RowSorting\n  RowExpanding,\n  RowPagination,\n  RowPinning,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  /**\n   * An array of extra features that you can add to the table instance.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#_features)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  _features?: TableFeature[]\n  /**\n   * Set this option to override any of the `autoReset...` feature options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#autoresetall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  autoResetAll?: boolean\n  /**\n   * The array of column defs to use for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  columns: ColumnDef<TData, any>[]\n  /**\n   * The data for the table to display. This array should match the type you provided to `table.setRowType<...>`. Columns can access this data via string/index or a functional accessor. When the `data` option changes reference, the table will reprocess the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#data)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  data: TData[]\n  /**\n   * Set this option to `true` to output all debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugAll?: boolean\n  /**\n   * Set this option to `true` to output cell debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcells]\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugCells?: boolean\n  /**\n   * Set this option to `true` to output column debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugColumns?: boolean\n  /**\n   * Set this option to `true` to output header debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugHeaders?: boolean\n  /**\n   * Set this option to `true` to output row debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugRows?: boolean\n  /**\n   * Set this option to `true` to output table debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugtable)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugTable?: boolean\n  /**\n   * Default column options to use for all column defs supplied to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#defaultcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  /**\n   * This required option is a factory for a function that computes and returns the core row model for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  /**\n   * This optional function is used to derive a unique ID for any given row. If not provided the rows index is used (nested rows join together with `.` using their grandparents' index eg. `index.index.index`). If you need to identify individual rows that are originating from any server-side operations, it's suggested you use this function to return an ID that makes sense regardless of network IO/ambiguity eg. a userId, taskId, database ID field, etc.\n   * @example getRowId: row => row.userId\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  /**\n   * This optional function is used to access the sub rows for any given row. If you are using nested rows, you will need to use this function to return the sub rows object (or undefined) from the row.\n   * @example getSubRows: row => row.subRows\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  /**\n   * Use this option to optionally pass initial state to the table. This state will be used when resetting various table states either automatically by the table (eg. `options.autoResetPageIndex`) or via functions like `table.resetRowSelection()`. Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.\n   *\n   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable.\n   *\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState?: InitialTableState\n  /**\n   * This option is used to optionally implement the merging of table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#mergeoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  /**\n   * You can pass any object to `options.meta` and access it anywhere the `table` is available via `table.options.meta`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#meta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  meta?: TableMeta<TData>\n  /**\n   * The `onStateChange` option can be used to optionally listen to state changes within the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#onstatechange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  onStateChange: (updater: Updater<TableState>) => void\n  /**\n   * Value used when the desired value is not found in the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#renderfallbackvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  renderFallbackValue: any\n  /**\n   * The `state` option can be used to optionally _control_ part or all of the table state. The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table. You can also listen to state changes via the `onStateChange` option.\n   * > Note: Any state passed in here will override both the internal state and any other `initialState` you provide.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#state)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  state: Partial<TableState>\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  _features: readonly TableFeature[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getCoreRowModel?: () => RowModel<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  _queue: (cb: () => void) => void\n  /**\n   * Returns all columns in the table in their normalized and nested hierarchy.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all columns in the table flattened to a single level.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all leaf-node columns in the table flattened to a single level. This does not include parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a single column by its ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n  /**\n   * Returns the core row model before any processing has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: () => RowModel<TData>\n  /**\n   * Returns the row with the given ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRow: (id: string, searchAll?: boolean) => Row<TData>\n  /**\n   * Returns the final model after all processing from other used features has been applied. This is the row model that is most commonly used for rendering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowModel: () => RowModel<TData>\n  /**\n   * Call this function to get the table's current state. It's recommended to use this function and its state, especially when managing the table state manually. It is the exact same state used internally by the table for every feature and function it provides.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getState: () => TableState\n  /**\n   * This is the resolved initial state of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState: TableState\n  /**\n   * A read-only reference to the table's current options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#options)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  /**\n   * Call this function to reset the table state to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#reset)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  reset: () => void\n  /**\n   * This function can be used to update the table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  /**\n   * Call this function to update the table state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setState: (updater: Updater<TableState>) => void\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    (options.debugAll || options.debugTable)\n  ) {\n    console.info('Creating Table Instance...')\n  }\n\n  const _features = [...builtInFeatures, ...(options._features ?? [])]\n\n  let table = { _features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = (feature.getInitialState?.(initialState) ??\n      initialState) as TableState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id: string, searchAll?: boolean) => {\n      let row = (\n        searchAll ? table.getPrePaginationRowModel() : table.getRowModel()\n      ).rowsById[id]\n\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id]\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`)\n          }\n          throw new Error()\n        }\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllColumns')\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce(\n          (acc, column) => {\n            acc[column.id] = column\n            return acc\n          },\n          {} as Record<string, Column<TData, unknown>>\n        )\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index]\n    feature?.createTable?.(table)\n  }\n\n  return table\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel')\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nfunction filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nfunction filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n", "import {\n  AccessorFn,\n  AccessorFnColumnDef,\n  AccessorKeyColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n        ? DeepValue<TData, TAccessor>\n        : never,\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => TAccessor extends AccessorFn<TData>\n    ? AccessorFnColumnDef<TData, TValue>\n    : AccessorKeyColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => DisplayColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => GroupColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData,\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column,\n    group: column => column,\n  }\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const uniqueValues = facetedRowModel.flatRows\n          .flatMap(flatRow => flatRow.getUniqueValues(columnId) ?? [])\n          .map(Number)\n          .filter(value => !Number.isNaN(value))\n\n        if (!uniqueValues.length) return\n\n        let facetedMinValue = uniqueValues[0]!\n        let facetedMaxValue = uniqueValues[uniqueValues.length - 1]!\n\n        for (const value of uniqueValues) {\n          if (value < facetedMinValue) facetedMinValue = value\n          else if (value > facetedMaxValue) facetedMaxValue = value\n        }\n\n        return [facetedMinValue, facetedMaxValue]\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues')\n    )\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel')\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      getMemoOptions(\n        table.options,\n        'debugTable',\n        `getFacetedUniqueValues_${columnId}`\n      )\n    )\n}\n", "import { ResolvedColumnFilter } from '../features/ColumnFiltering'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = (columnFilters ?? []).map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowData, RowModel, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { GroupingState } from '../features/ColumnGrouping'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          rowModel.rows.forEach(row => {\n            row.depth = 0\n            row.parentId = undefined\n          })\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Perform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              subRows.forEach(subRow => {\n                subRow.parentId = id\n              })\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n        table._queue(() => {\n          table._autoResetExpanded()\n          table._autoResetPageIndex()\n        })\n      })\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel')\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/RowSorting'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(sort =>\n          table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1 | 'first' | 'last'\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = rows.map(row => ({ ...row }))\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const sortUndefined = columnInfo.sortUndefined\n              const isDesc = sortEntry?.desc ?? false\n\n              let sortInt = 0\n\n              // All sorting ints should always return in ascending order\n              if (sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = aValue === undefined\n                const bUndefined = bValue === undefined\n\n                if (aUndefined || bUndefined) {\n                  if (sortUndefined === 'first') return aUndefined ? -1 : 1\n                  if (sortUndefined === 'last') return aUndefined ? 1 : -1\n                  sortInt =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                        ? sortUndefined\n                        : -sortUndefined\n                }\n              }\n\n              if (sortInt === 0) {\n                sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n              }\n\n              // If sorting is non-zero, take care of desc and inversion\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n"], "names": ["functionalUpdate", "updater", "input", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "result", "deps", "depArgs", "depTime", "debug", "Date", "now", "newDeps", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "getMemoOptions", "tableOptions", "debugLevel", "_tableOptions$debugAl", "debugAll", "process", "createCell", "table", "row", "column", "columnId", "cell", "id", "getValue", "renderValue", "getRenderValue", "_cell$getValue", "options", "renderFallbackValue", "getContext", "_features", "feature", "createColumn", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "resolvedColumnDef", "_getDefaultColumnDef", "accessorKey", "accessorFn", "prototype", "replaceAll", "replace", "undefined", "header", "includes", "originalRow", "split", "_result", "Error", "columns", "getFlatColumns", "_column$columns", "flatMap", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "find", "filter", "Boolean", "rightColumns", "buildHeaderGroups", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "getRightHeaderGroups", "_right$map$filter2", "getFooterGroups", "headerGroups", "reverse", "getLeftFooterGroups", "getCenterFooterGroups", "getRightFooterGroups", "getFlatHeaders", "headers", "getLeftFlatHeaders", "getCenterFlatHeaders", "getRightFlatHeaders", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "getLeftLeafHeaders", "_header$subHeaders2", "getRightLeafHeaders", "_header$subHeaders3", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "bottomHeaders", "recurseHeadersForSpans", "childRowSpans", "childColSpan", "childRowSpan", "createRow", "original", "rowIndex", "subRows", "parentId", "_valuesCache", "_uniqueValuesCache", "hasOwnProperty", "getColumn", "getUniqueValues", "_row$getValue", "getLeafRows", "getParentRow", "getRow", "getParentRows", "parentRows", "currentRow", "parentRow", "getAllCells", "getAllLeafColumns", "_getAllCellsByColumnId", "allCells", "reduce", "acc", "i", "ColumnFaceting", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "includesString", "filterValue", "_filterValue$toString", "search", "toString", "toLowerCase", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "Number", "isNaN", "Infinity", "temp", "filterFns", "ColumnFiltering", "getDefaultColumnDef", "filterFn", "getInitialState", "state", "columnFilters", "getDefaultOptions", "onColumnFiltersChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "getAutoFilterFn", "firstRow", "getCoreRowModel", "flatRows", "value", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "findIndex", "setFilterValue", "setColumnFilters", "previousFilter", "newFilter", "_old$filter", "shouldAutoRemoveFilter", "newFilterObj", "_old$map", "_table", "columnFiltersMeta", "_functionalUpdate", "resetColumnFilters", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "aggregationFns", "sum", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "values", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "size", "_columnId", "ColumnGrouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getGroupingValue", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "Object", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "_groupingValuesCache", "getIsPlaceholder", "getIsAggregated", "_row$subRows", "nonGroupingColumns", "col", "g", "ColumnOrdering", "columnOrder", "onColumnOrderChange", "getIndex", "position", "_getVisibleLeafColumns", "getIsFirstColumn", "_columns$", "getIsLastColumn", "_columns", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "ColumnPinning", "onColumnPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "enableColumnPinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "getCenterVisibleCells", "_getAllVisibleCells", "leftAndRight", "getLeftVisibleCells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "defaultColumnSizing", "minSize", "maxSize", "MAX_SAFE_INTEGER", "ColumnSizing", "columnSizing", "columnSizingInfo", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "slice", "getAfter", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "enableResizing", "enableColumnResizing", "getIsResizing", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "_contextDocument", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "headerSize", "onMove", "onEnd", "contextDocument", "document", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "noop", "window", "err", "type", "ColumnVisibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "childColumns", "c", "enableHiding", "getToggleVisibilityHandler", "target", "checked", "cells", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "_target", "GlobalFaceting", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "GlobalFiltering", "globalFilter", "onGlobalFilterChange", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "getCanGlobalFilter", "_table$options$getCol", "enableGlobalFilter", "getGlobalAutoFilterFn", "getGlobalFilterFn", "setGlobalFilter", "resetGlobalFilter", "RowExpanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "autoResetAll", "autoResetExpanded", "manualExpanding", "_queue", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "getCanSomeRowsExpand", "getPrePaginationRowModel", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "getRowCanExpand", "enableExpanding", "getIsAllParentsExpanded", "isFullyExpanded", "getToggleExpandedHandler", "canExpand", "RowPagination", "pagination", "pageIndex", "pageSize", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "firstPage", "lastPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "getRowCount", "_table$options$rowCou", "rowCount", "rows", "RowPinning", "rowPinning", "top", "bottom", "onRowPinningChange", "includeLeafRows", "includeParentRows", "leafRowIds", "parentRowIds", "rowIds", "setRowPinning", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "_old$top2", "_old$bottom2", "has", "enableRowPinning", "isTop", "isBottom", "_ref4", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "getTopRows", "getBottomRows", "_ref5", "resetRowPinning", "_table$initialState$r", "getIsSomeRowsPinned", "_pinningState$top", "_pinningState$bottom", "_getPinnedRows", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "keepPinnedRows", "allRows", "topPinnedRowIds", "bottomPinnedRowIds", "getCenterRows", "topAndBottom", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected", "reSplitAlphaNumeric", "compareBasic", "compareAlphanumeric", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "alphanumeric", "rowA", "rowB", "alphanumericCaseSensitive", "text", "textCaseSensitive", "datetime", "basic", "RowSorting", "sorting", "sortingFn", "sortUndefined", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "sortAction", "newSorting", "nextDesc", "_table$options$maxMul", "getCanMultiSort", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "_column$columnDef$ena2", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "builtInFeatures", "expandRows", "expandedRows", "handleRow", "filterRows", "filterRowImpl", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "filterRowModelFromLeafs", "_table$options$maxLea2", "filterRowModelFromRoot", "accessor", "display", "group", "_options$_features", "_options$initialState", "defaultOptions", "assign", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "mergeOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "searchAll", "defaultColumn", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "data", "accessRows", "originalRows", "_row$originalSubRows", "getSubRows", "originalSubRows", "_table$getColumn", "facetedRowModel", "uniqueValues", "flatRow", "_flatRow$getUniqueVal", "facetedMinValue", "facetedMaxValue", "preRowModel", "filterableIds", "facetedUniqueValues", "j", "_facetedUniqueValues$", "set", "get", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "globallyFilterableColumns", "currentColumnFilter", "currentGlobalFilter", "_globalFilterFn$resol", "filterMeta", "__global__", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupMap", "res<PERSON>ey", "previous", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "pageStart", "pageEnd", "paginatedRowModel", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "sortInt", "aUndefined", "bUndefined"], "mappings": ";;;;;;;;;;iPAgFO,SAASA,EAAoBC,EAAqBC,GACvD,MAA0B,mBAAZD,EACTA,EAA4BC,GAC7BD,CACN,CAMO,SAASE,EACdC,EACAC,GAEA,OAAQJ,IACJI,EAAiBC,UAAuBC,IACjC,IACFA,EACHH,CAACA,GAAMJ,EAAiBC,EAAUM,EAAYH,OAEhD,CAEN,CAIO,SAASI,EAAkCC,GAChD,OAAOA,aAAaC,QACtB,CAEO,SAASC,EAAcF,GAC5B,OAAOG,MAAMC,QAAQJ,IAAMA,EAAEK,OAAMC,GAAsB,iBAARA,GACnD,CAEO,SAASC,EACdC,EACAC,GAEA,MAAMC,EAAgB,GAEhBC,EAAWC,IACfA,EAAOC,SAAQC,IACbJ,EAAKK,KAAKD,GACV,MAAME,EAAWP,EAAYK,GACjB,MAARE,GAAAA,EAAUC,QACZN,EAAQK,EACV,GACA,EAKJ,OAFAL,EAAQH,GAEDE,CACT,CAEO,SAASQ,EACdC,EACAC,EACAC,GAMA,IACIC,EADAC,EAAc,GAGlB,OAAOC,IACL,IAAIC,EACAJ,EAAK1B,KAAO0B,EAAKK,QAAOD,EAAUE,KAAKC,OAE3C,MAAMC,EAAUV,EAAQK,GAMxB,KAHEK,EAAQZ,SAAWM,EAAKN,QACxBY,EAAQC,MAAK,CAACC,EAAUC,IAAkBT,EAAKS,KAAWD,KAG1D,OAAOT,EAKT,IAAIW,EAMJ,GARAV,EAAOM,EAGHR,EAAK1B,KAAO0B,EAAKK,QAAOO,EAAaN,KAAKC,OAE9CN,EAASF,KAAMS,GACX,MAAJR,GAAc,MAAdA,EAAMa,UAANb,EAAMa,SAAWZ,GAEbD,EAAK1B,KAAO0B,EAAKK,OACfL,MAAAA,GAAAA,EAAMK,QAAS,CACjB,MAAMS,EAAaC,KAAKC,MAAgC,KAAzBV,KAAKC,MAAQH,IAAmB,IACzDa,EAAgBF,KAAKC,MAAmC,KAA5BV,KAAKC,MAAQK,IAAsB,IAC/DM,EAAsBD,EAAgB,GAEtCE,EAAMA,CAACC,EAAsBC,KAEjC,IADAD,EAAME,OAAOF,GACNA,EAAIxB,OAASyB,GAClBD,EAAM,IAAMA,EAEd,OAAOA,CAAG,EAGZG,QAAQC,KACN,OAAOL,EAAIF,EAAe,OAAOE,EAAIL,EAAY,QACjD,2FAGeC,KAAKU,IAChB,EACAV,KAAKW,IAAI,IAAM,IAAMR,EAAqB,sBAE9ClB,MAAAA,OAAAA,EAAAA,EAAM1B,IAEV,CAGF,OAAO2B,CAAM,CAEjB,CAEO,SAAS0B,EACdC,EACAC,EAOAvD,EACAuC,GAEA,MAAO,CACLR,MAAOA,KAAA,IAAAyB,EAAA,OAA4B,OAA5BA,EAAkB,MAAZF,OAAY,EAAZA,EAAcG,UAAQD,EAAIF,EAAaC,EAAW,EAC/DvD,KAAK0D,EACLnB,WAEJ,CCvKO,SAASoB,EACdC,EACAC,EACAC,EACAC,GAEA,MAGMC,EAAgC,CACpCC,GAAI,GAAGJ,EAAII,MAAMH,EAAOG,KACxBJ,MACAC,SACAI,SAAUA,IAAML,EAAIK,SAASH,GAC7BI,YARqBC,KAAA,IAAAC,EAAA,OACNA,OADMA,EACrBL,EAAKE,YAAUG,EAAIT,EAAMU,QAAQC,mBAAmB,EAQpDC,WAAYjD,GACV,IAAM,CAACqC,EAAOE,EAAQD,EAAKG,KAC3B,CAACJ,EAAOE,EAAQD,EAAKG,KAAU,CAC7BJ,QACAE,SACAD,MACAG,KAAMA,EACNE,SAAUF,EAAKE,SACfC,YAAaH,EAAKG,eAEpBd,EAAeO,EAAMU,QAAS,gBAalC,OATAV,EAAMa,UAAUvD,SAAQwD,IACtBA,MAAAA,EAAQf,YAARe,EAAQf,WACNK,EACAF,EACAD,EACAD,EACD,GACA,CAAE,GAEEI,CACT,CC1BO,SAASW,EACdf,EACAgB,EACAC,EACAC,GACuB,IAAAC,EAAAC,EACvB,MAEMC,EAAoB,IAFJrB,EAAMsB,0BAIvBN,GAGCO,EAAcF,EAAkBE,YAEtC,IAWIC,EAXAnB,EAMW,OANTc,EACgBC,OADhBA,EACJC,EAAkBhB,IAAEe,EACnBG,EAC0C,mBAAhCnC,OAAOqC,UAAUC,WACtBH,EAAYG,WAAW,IAAK,KAC5BH,EAAYI,QAAQ,MAAO,UAC7BC,GAAST,EACwB,iBAA7BE,EAAkBQ,OACtBR,EAAkBQ,YAClBD,EA6BN,GAzBIP,EAAkBG,WACpBA,EAAaH,EAAkBG,WACtBD,IAGPC,EADED,EAAYO,SAAS,KACTC,IACZ,IAAIhE,EAASgE,EAEb,IAAK,MAAM3F,KAAOmF,EAAYS,MAAM,KAAM,CAAA,IAAAC,EACxClE,SAAMkE,EAAGlE,UAAAkE,EAAS7F,EAMpB,CAEA,OAAO2B,CAAM,EAGDgE,GACXA,EAAoBV,EAAkBE,eAIxClB,EAQH,MAAM,IAAI6B,MAGZ,IAAIhC,EAAiC,CACnCG,GAAI,GAAGjB,OAAOiB,KACdmB,aACAN,OAAQA,EACRD,QACAD,UAAWK,EACXc,QAAS,GACTC,eAAgBzE,GACd,IAAM,EAAC,KACP,KAAM,IAAA0E,EACJ,MAAO,CACLnC,KACGmC,OAAHA,EAAGnC,EAAOiC,cAAPE,EAAAA,EAAgBC,SAAQ7F,GAAKA,EAAE2F,mBACnC,GAEH3C,EAAeO,EAAMU,QAAS,iBAEhC6B,eAAgB5E,GACd,IAAM,CAACqC,EAAMwC,wBACbC,IAAgB,IAAAC,EACd,GAAIA,OAAJA,EAAIxC,EAAOiC,UAAPO,EAAgBhF,OAAQ,CAC1B,IAAIiF,EAAczC,EAAOiC,QAAQG,SAAQpC,GACvCA,EAAOqC,mBAGT,OAAOE,EAAaE,EACtB,CAEA,MAAO,CAACzC,EAAgC,GAE1CT,EAAeO,EAAMU,QAAS,kBAIlC,IAAK,MAAMI,KAAWd,EAAMa,UACN,MAApBC,EAAQC,cAARD,EAAQC,aAAeb,EAAiCF,GAI1D,OAAOE,CACT,CC9JA,MAAM/B,EAAQ,eA0Md,SAASyE,EACP5C,EACAE,EACAQ,GAOuB,IAAAmC,EAGvB,IAAIhB,EAAoC,CACtCxB,GAHmB,OAAbwC,EAAGnC,EAAQL,IAAEwC,EAAI3C,EAAOG,GAI9BH,SACAzB,MAAOiC,EAAQjC,MACfqE,gBAAiBpC,EAAQoC,cACzBC,cAAerC,EAAQqC,cACvB9B,MAAOP,EAAQO,MACf+B,WAAY,GACZC,QAAS,EACTC,QAAS,EACTC,YAAa,KACbC,eAAgBA,KACd,MAAMC,EAAwC,GAExCC,EAAiBC,IACjBA,EAAEP,YAAcO,EAAEP,WAAWtF,QAC/B6F,EAAEP,WAAWQ,IAAIF,GAEnBD,EAAY7F,KAAK+F,EAA4B,EAK/C,OAFAD,EAAczB,GAEPwB,CAAW,EAEpBzC,WAAYA,KAAO,CACjBZ,QACA6B,OAAQA,EACR3B,YAQJ,OAJAF,EAAMa,UAAUvD,SAAQwD,IACF,MAApBA,EAAQ8B,cAAR9B,EAAQ8B,aAAef,EAAiC7B,EAAM,IAGzD6B,CACT,CAEO,MAAM4B,EAAwB,CACnCC,YAAqC1D,IAGnCA,EAAM2D,gBAAkBhG,GACtB,IAAM,CACJqC,EAAM4D,gBACN5D,EAAM6D,wBACN7D,EAAM8D,WAAWC,cAAcC,KAC/BhE,EAAM8D,WAAWC,cAAcE,SAEjC,CAACC,EAAYvB,EAAaqB,EAAMC,KAAU,IAAAE,EAAAC,EACxC,MAAMC,EAGcF,OAHHA,EACfH,MAAAA,OAAAA,EAAAA,EACIR,KAAIrD,GAAYwC,EAAY2B,MAAK7H,GAAKA,EAAE4D,KAAOF,MAChDoE,OAAOC,UAAQL,EAAI,GAElBM,EAGcL,OAHFA,EAChBH,MAAAA,OAAAA,EAAAA,EACIT,KAAIrD,GAAYwC,EAAY2B,MAAK7H,GAAKA,EAAE4D,KAAOF,MAChDoE,OAAOC,UAAQJ,EAAI,GAYxB,OANqBM,EACnBR,EACA,IAAIG,KANgB1B,EAAY4B,QAChCrE,KAAe,MAAJ8D,GAAAA,EAAMlC,SAAS5B,EAAOG,WAAQ4D,GAAAA,EAAOnC,SAAS5B,EAAOG,UAK1BoE,GACtCzE,EAGiB,GAErBP,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAM2E,sBAAwBhH,GAC5B,IAAM,CACJqC,EAAM4D,gBACN5D,EAAM6D,wBACN7D,EAAM8D,WAAWC,cAAcC,KAC/BhE,EAAM8D,WAAWC,cAAcE,SAEjC,CAACC,EAAYvB,EAAaqB,EAAMC,IAIvBS,EAAkBR,EAHzBvB,EAAcA,EAAY4B,QACxBrE,KAAe,MAAJ8D,GAAAA,EAAMlC,SAAS5B,EAAOG,WAAQ4D,GAAAA,EAAOnC,SAAS5B,EAAOG,OAEhBL,EAAO,WAE3DP,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAM4E,oBAAsBjH,GAC1B,IAAM,CACJqC,EAAM4D,gBACN5D,EAAM6D,wBACN7D,EAAM8D,WAAWC,cAAcC,QAEjC,CAACE,EAAYvB,EAAaqB,KAAS,IAAAa,EAMjC,OAAOH,EAAkBR,EAFLW,OAHIA,EACtBb,MAAAA,OAAAA,EAAAA,EACIR,KAAIrD,GAAYwC,EAAY2B,MAAK7H,GAAKA,EAAE4D,KAAOF,MAChDoE,OAAOC,UAAQK,EAAI,GAEiC7E,EAAO,OAAO,GAEzEP,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAM8E,qBAAuBnH,GAC3B,IAAM,CACJqC,EAAM4D,gBACN5D,EAAM6D,wBACN7D,EAAM8D,WAAWC,cAAcE,SAEjC,CAACC,EAAYvB,EAAasB,KAAU,IAAAc,EAMlC,OAAOL,EAAkBR,EAFLa,OAHIA,EACtBd,MAAAA,OAAAA,EAAAA,EACIT,KAAIrD,GAAYwC,EAAY2B,MAAK7H,GAAKA,EAAE4D,KAAOF,MAChDoE,OAAOC,UAAQO,EAAI,GAEiC/E,EAAO,QAAQ,GAE1EP,EAAeO,EAAMU,QAASvC,IAKhC6B,EAAMgF,gBAAkBrH,GACtB,IAAM,CAACqC,EAAM2D,qBACbsB,GACS,IAAIA,GAAcC,WAE3BzF,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAMmF,oBAAsBxH,GAC1B,IAAM,CAACqC,EAAM4E,yBACbK,GACS,IAAIA,GAAcC,WAE3BzF,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAMoF,sBAAwBzH,GAC5B,IAAM,CAACqC,EAAM2E,2BACbM,GACS,IAAIA,GAAcC,WAE3BzF,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAMqF,qBAAuB1H,GAC3B,IAAM,CAACqC,EAAM8E,0BACbG,GACS,IAAIA,GAAcC,WAE3BzF,EAAeO,EAAMU,QAASvC,IAKhC6B,EAAMsF,eAAiB3H,GACrB,IAAM,CAACqC,EAAM2D,qBACbsB,GACSA,EACJzB,KAAIL,GACIA,EAAYoC,UAEpBpI,QAELsC,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAMwF,mBAAqB7H,GACzB,IAAM,CAACqC,EAAM4E,yBACbZ,GACSA,EACJR,KAAIL,GACIA,EAAYoC,UAEpBpI,QAELsC,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAMyF,qBAAuB9H,GAC3B,IAAM,CAACqC,EAAM2E,2BACbX,GACSA,EACJR,KAAIL,GACIA,EAAYoC,UAEpBpI,QAELsC,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAM0F,oBAAsB/H,GAC1B,IAAM,CAACqC,EAAM8E,0BACbd,GACSA,EACJR,KAAIL,GACIA,EAAYoC,UAEpBpI,QAELsC,EAAeO,EAAMU,QAASvC,IAKhC6B,EAAM2F,qBAAuBhI,GAC3B,IAAM,CAACqC,EAAMyF,0BACbG,GACSA,EAAYrB,QAAO1C,IAAM,IAAAgE,EAAA,QAAsB,OAAlBA,EAAChE,EAAOmB,aAAP6C,EAAmBnI,OAAM,KAEhE+B,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAM8F,mBAAqBnI,GACzB,IAAM,CAACqC,EAAMwF,wBACbI,GACSA,EAAYrB,QAAO1C,IAAM,IAAAkE,EAAA,QAAsB,OAAlBA,EAAClE,EAAOmB,aAAP+C,EAAmBrI,OAAM,KAEhE+B,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAMgG,oBAAsBrI,GAC1B,IAAM,CAACqC,EAAM0F,yBACbE,GACSA,EAAYrB,QAAO1C,IAAM,IAAAoE,EAAA,QAAsB,OAAlBA,EAACpE,EAAOmB,aAAPiD,EAAmBvI,OAAM,KAEhE+B,EAAeO,EAAMU,QAASvC,IAGhC6B,EAAMoD,eAAiBzF,GACrB,IAAM,CACJqC,EAAM4E,sBACN5E,EAAM2E,wBACN3E,EAAM8E,0BAER,CAACd,EAAMkC,EAAQjC,KAAU,IAAAkC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACvB,MAAO,IACeL,OAApBA,EAAIC,OAAJA,EAAIpC,EAAK,SAALoC,EAAAA,EAASb,SAAOY,EAAI,MACFE,OAAtBA,EAAIC,OAAJA,EAAIJ,EAAO,SAAPI,EAAAA,EAAWf,SAAOc,EAAI,MACL,OAArBE,EAAY,OAAZC,EAAIvC,EAAM,SAAE,EAARuC,EAAUjB,SAAOgB,EAAI,IAExB/C,KAAI3B,GACIA,EAAOuB,mBAEfjG,MAAM,GAEXsC,EAAeO,EAAMU,QAASvC,GAC/B,GAIE,SAASuG,EACdR,EACAuC,EACAzG,EACA0G,GACA,IAAAC,EAAAC,EAOA,IAAIC,EAAW,EAEf,MAAMC,EAAe,SAAC3E,EAAmClB,QAAK,IAALA,IAAAA,EAAQ,GAC/D4F,EAAWhI,KAAKU,IAAIsH,EAAU5F,GAE9BkB,EACGoC,QAAOrE,GAAUA,EAAO6G,iBACxBzJ,SAAQ4C,IAAU,IAAAmC,EACbA,OAAJA,EAAInC,EAAOiC,UAAPE,EAAgB3E,QAClBoJ,EAAa5G,EAAOiC,QAASlB,EAAQ,EACvC,GACC,IAGP6F,EAAa5C,GAEb,IAAIe,EAAqC,GAEzC,MAAM+B,EAAoBA,CACxBC,EACAhG,KAGA,MAAMkC,EAAkC,CACtClC,QACAZ,GAAI,CAACqG,EAAc,GAAGzF,KAASsD,OAAOC,SAAS0C,KAAK,KACpD3B,QAAS,IAIL4B,EAAiD,GAGvDF,EAAe3J,SAAQ8J,IAGrB,MAAMC,EAA4B,IAAIF,GAAsBjC,UAAU,GAItE,IAAIhF,EACA4C,GAAgB,EAWpB,GAdqBsE,EAAclH,OAAOe,QAAUkC,EAAYlC,OAK5CmG,EAAclH,OAAOgB,OAEvChB,EAASkH,EAAclH,OAAOgB,QAG9BhB,EAASkH,EAAclH,OACvB4C,GAAgB,GAIhBuE,IACyB,MAAzBA,OAAyB,EAAzBA,EAA2BnH,UAAWA,EAGtCmH,EAA0BrE,WAAWxF,KAAK4J,OACrC,CAEL,MAAMvF,EAASe,EAAa5C,EAAOE,EAAQ,CACzCG,GAAI,CAACqG,EAAczF,EAAOf,EAAOG,GAAiB,MAAb+G,OAAa,EAAbA,EAAe/G,IACjDkE,OAAOC,SACP0C,KAAK,KACRpE,gBACAC,cAAeD,EACX,GAAGqE,EAAqB5C,QAAO9H,GAAKA,EAAEyD,SAAWA,IAAQxC,cACzDkE,EACJX,QACAxC,MAAO0I,EAAqBzJ,SAI9BmE,EAAOmB,WAAWxF,KAAK4J,GAGvBD,EAAqB3J,KAAKqE,EAC5B,CAEAsB,EAAYoC,QAAQ/H,KAAK4J,GACzBA,EAAcjE,YAAcA,CAAW,IAGzC8B,EAAazH,KAAK2F,GAEdlC,EAAQ,GACV+F,EAAkBG,EAAsBlG,EAAQ,EAClD,EAGIqG,EAAgBb,EAAejD,KAAI,CAACtD,EAAQzB,IAChDmE,EAAa5C,EAAOE,EAAQ,CAC1Be,MAAO4F,EACPpI,YAIJuI,EAAkBM,EAAeT,EAAW,GAE5C5B,EAAaC,UAMb,MAAMqC,EACJhC,GAEwBA,EAAQhB,QAAO1C,GACrCA,EAAO3B,OAAO6G,iBAGOvD,KAAI3B,IACzB,IAAIoB,EAAU,EACVC,EAAU,EACVsE,EAAgB,CAAC,GAEjB3F,EAAOmB,YAAcnB,EAAOmB,WAAWtF,QACzC8J,EAAgB,GAEhBD,EAAuB1F,EAAOmB,YAAY1F,SACxC6D,IAAsD,IAAnD8B,QAASwE,EAAcvE,QAASwE,GAAcvG,EAC/C8B,GAAWwE,EACXD,EAAchK,KAAKkK,EAAa,KAIpCzE,EAAU,EASZ,OALAC,GADwBrE,KAAKW,OAAOgI,GAGpC3F,EAAOoB,QAAUA,EACjBpB,EAAOqB,QAAUA,EAEV,CAAED,UAASC,UAAS,IAM/B,OAFAqE,SAAsBZ,EAAgB,OAAhBC,EAAC3B,EAAa,SAAE,EAAf2B,EAAiBrB,SAAOoB,EAAI,IAE5C1B,CACT,OChiBa0C,EAAYA,CACvB3H,EACAK,EACAuH,EACAC,EACA5G,EACA6G,EACAC,KAEA,IAAI9H,EAAsB,CACxBI,KACA5B,MAAOoJ,EACPD,WACA3G,QACA8G,WACAC,aAAc,CAAE,EAChBC,mBAAoB,CAAE,EACtB3H,SAAUH,IACR,GAAIF,EAAI+H,aAAaE,eAAe/H,GAClC,OAAOF,EAAI+H,aAAa7H,GAG1B,MAAMD,EAASF,EAAMmI,UAAUhI,GAE/B,OAAW,MAAND,GAAAA,EAAQsB,YAIbvB,EAAI+H,aAAa7H,GAAYD,EAAOsB,WAClCvB,EAAI2H,SACJC,GAGK5H,EAAI+H,aAAa7H,SATxB,CASiC,EAEnCiI,gBAAiBjI,IACf,GAAIF,EAAIgI,mBAAmBC,eAAe/H,GACxC,OAAOF,EAAIgI,mBAAmB9H,GAGhC,MAAMD,EAASF,EAAMmI,UAAUhI,GAE/B,OAAW,MAAND,GAAAA,EAAQsB,WAIRtB,EAAOc,UAAUoH,iBAKtBnI,EAAIgI,mBAAmB9H,GAAYD,EAAOc,UAAUoH,gBAClDnI,EAAI2H,SACJC,GAGK5H,EAAIgI,mBAAmB9H,KAT5BF,EAAIgI,mBAAmB9H,GAAY,CAACF,EAAIK,SAASH,IAC1CF,EAAIgI,mBAAmB9H,SANhC,CAcuC,EAEzCI,YAAaJ,IAAQ,IAAAkI,EAAA,OACG,OADHA,EACnBpI,EAAIK,SAASH,IAASkI,EAAIrI,EAAMU,QAAQC,mBAAmB,EAC7DmH,QAASA,MAAAA,EAAAA,EAAW,GACpBQ,YAAaA,IAAMtL,EAAUiD,EAAI6H,SAASrL,GAAKA,EAAEqL,UACjDS,aAAcA,IACZtI,EAAI8H,SAAW/H,EAAMwI,OAAOvI,EAAI8H,UAAU,QAAQnG,EACpD6G,cAAeA,KACb,IAAIC,EAA2B,GAC3BC,EAAa1I,EACjB,OAAa,CACX,MAAM2I,EAAYD,EAAWJ,eAC7B,IAAKK,EAAW,MAChBF,EAAWlL,KAAKoL,GAChBD,EAAaC,CACf,CACA,OAAOF,EAAWxD,SAAS,EAE7B2D,YAAalL,GACX,IAAM,CAACqC,EAAM8I,uBACbnG,GACSA,EAAYa,KAAItD,GACdH,EAAWC,EAAOC,EAAmBC,EAAQA,EAAOG,OAG/DZ,EAAeO,EAAMU,QAAS,cAGhCqI,uBAAwBpL,GACtB,IAAM,CAACsC,EAAI4I,iBACXG,GACSA,EAASC,QACd,CAACC,EAAK9I,KACJ8I,EAAI9I,EAAKF,OAAOG,IAAMD,EACf8I,IAET,CACF,IAEFzJ,EAAeO,EAAMU,QAAS,eAIlC,IAAK,IAAIyI,EAAI,EAAGA,EAAInJ,EAAMa,UAAUnD,OAAQyL,IAAK,CAC/C,MAAMrI,EAAUd,EAAMa,UAAUsI,GAChCrI,MAAAA,GAAAA,MAAAA,EAAS6G,WAAT7G,EAAS6G,UAAY1H,EAAmBD,EAC1C,CAEA,OAAOC,CAAG,ECxJCmJ,EAA+B,CAC1CrI,aAAcA,CACZb,EACAF,KAEAE,EAAOmJ,oBACLrJ,EAAMU,QAAQ4I,oBACdtJ,EAAMU,QAAQ4I,mBAAmBtJ,EAAOE,EAAOG,IACjDH,EAAOoJ,mBAAqB,IACrBpJ,EAAOmJ,oBAILnJ,EAAOmJ,sBAHLrJ,EAAMuJ,yBAKjBrJ,EAAOsJ,wBACLxJ,EAAMU,QAAQ+I,wBACdzJ,EAAMU,QAAQ+I,uBAAuBzJ,EAAOE,EAAOG,IACrDH,EAAOuJ,uBAAyB,IACzBvJ,EAAOsJ,wBAILtJ,EAAOsJ,0BAHL,IAAIE,IAKfxJ,EAAOyJ,wBACL3J,EAAMU,QAAQkJ,wBACd5J,EAAMU,QAAQkJ,uBAAuB5J,EAAOE,EAAOG,IACrDH,EAAO0J,uBAAyB,KAC9B,GAAK1J,EAAOyJ,wBAIZ,OAAOzJ,EAAOyJ,yBAAyB,CACxC,GC/ECE,EAAgCA,CACpC5J,EACAE,EACA2J,KACG,IAAAC,EAAA1B,EACH,MAAM2B,EAAoBD,MAAXD,GAAAC,OAAWA,EAAXD,EAAaG,iBAAbF,EAAAA,EAAyBG,cACxC,OAAO1F,QAE+B,OAFxB6D,EACZpI,EACGK,SAAwBH,KACbkI,OADsBA,EADpCA,EAEI4B,aACa,OADH5B,EAFdA,EAGI6B,oBAAa,EAHjB7B,EAIIvG,SAASkI,GACd,EAGHH,EAAeM,WAAcpN,GAAaqN,EAAWrN,GAErD,MAAMsN,EAAyCA,CAC7CpK,EACAE,EACA2J,KACG,IAAAQ,EACH,OAAO9F,QACgC8F,OADzBA,EACZrK,EAAIK,SAAwBH,KAAqB,OAAZmK,EAArCA,EAAuCL,iBAAU,EAAjDK,EAAmDxI,SAASgI,GAC7D,EAGHO,EAAwBF,WAAcpN,GAAaqN,EAAWrN,GAE9D,MAAMwN,EAA8BA,CAClCtK,EACAE,EACA2J,KACG,IAAAU,EACH,OACuC,OAArCA,EAAAvK,EAAIK,SAAwBH,KAA5BqK,OAAqCA,EAArCA,EAAuCP,iBAAvCO,EAAAA,EAAmDN,kBACnDJ,MAAAA,OAAAA,EAAAA,EAAaI,cAAa,EAI9BK,EAAaJ,WAAcpN,GAAaqN,EAAWrN,GAEnD,MAAM0N,EAA6BA,CACjCxK,EACAE,EACA2J,KACG,IAAAY,EACH,OAAwC,OAAxCA,EAAOzK,EAAIK,SAAoBH,SAAS,EAAjCuK,EAAmC5I,SAASgI,EAAY,EAGjEW,EAAYN,WAAcpN,GAAaqN,EAAWrN,GAElD,MAAM4N,EAAgCA,CACpC1K,EACAE,EACA2J,KAEQA,EAAYvL,MAClBxB,IAAG,IAAA6N,EAAA,eAAIA,EAAC3K,EAAIK,SAAoBH,KAAxByK,EAAmC9I,SAAS/E,GAAI,IAI5D4N,EAAeR,WAAcpN,GAAaqN,EAAWrN,MAASA,MAAAA,GAAAA,EAAKW,QAEnE,MAAMmN,EAAiCA,CACrC5K,EACAE,EACA2J,IAEOA,EAAYvL,MAAKxB,IAAG,IAAA+N,EAAA,OACQ,OADRA,EACzB7K,EAAIK,SAAoBH,SAAS,EAAjC2K,EAAmChJ,SAAS/E,EAAI,IAIpD8N,EAAgBV,WAAcpN,GAAaqN,EAAWrN,MAASA,MAAAA,GAAAA,EAAKW,QAEpE,MAAMqN,EAAwBA,CAAC9K,EAAKE,EAAkB2J,IAC7C7J,EAAIK,SAASH,KAAc2J,EAGpCiB,EAAOZ,WAAcpN,GAAaqN,EAAWrN,GAE7C,MAAMiO,EAA4BA,CAChC/K,EACAE,EACA2J,IAEO7J,EAAIK,SAASH,IAAa2J,EAGnCkB,EAAWb,WAAcpN,GAAaqN,EAAWrN,GAEjD,MAAMkO,EAA+BA,CACnChL,EACAE,EACA2J,KAEA,IAAKtK,EAAKD,GAAOuK,EAEjB,MAAMoB,EAAWjL,EAAIK,SAAiBH,GACtC,OAAO+K,GAAY1L,GAAO0L,GAAY3L,CAAG,EAG3C0L,EAAcE,mBAAsBpO,IAClC,IAAKqO,EAAWC,GAAatO,EAEzBuO,EACmB,iBAAdF,EAAyBG,WAAWH,GAAuBA,EAChEI,EACmB,iBAAdH,EAAyBE,WAAWF,GAAuBA,EAEhE7L,EACY,OAAd4L,GAAsBK,OAAOC,MAAMJ,IAAcK,IAAWL,EAC1D/L,EAAoB,OAAd8L,GAAsBI,OAAOC,MAAMF,GAAaG,IAAWH,EAErE,GAAIhM,EAAMD,EAAK,CACb,MAAMqM,EAAOpM,EACbA,EAAMD,EACNA,EAAMqM,CACR,CAEA,MAAO,CAACpM,EAAKD,EAAI,EAGnB0L,EAAcd,WAAcpN,GAC1BqN,EAAWrN,IAASqN,EAAWrN,EAAI,KAAOqN,EAAWrN,EAAI,IAIpD,MAAM8O,EAAY,CACvBhC,iBACAQ,0BACAE,eACAE,cACAE,iBACAE,kBACAE,SACAC,aACAC,iBAOF,SAASb,EAAWrN,GAClB,OAAOA,SAA6C,KAARA,CAC9C,CC6FO,MAAM+O,EAAgC,CAC3CC,oBAAqBA,KAGZ,CACLC,SAAU,SAIdC,gBAAkBC,IACT,CACLC,cAAe,MACZD,IAIPE,kBACEpM,IAEO,CACLqM,sBAAuBlQ,EAAiB,gBAAiB6D,GACzDsM,oBAAoB,EACpBC,sBAAuB,MAI3BxL,aAAcA,CACZb,EACAF,KAEAE,EAAOsM,gBAAkB,KACvB,MAAMC,EAAWzM,EAAM0M,kBAAkBC,SAAS,GAE5CC,EAAQH,MAAAA,OAAAA,EAAAA,EAAUnM,SAASJ,EAAOG,IAExC,MAAqB,iBAAVuM,EACFf,EAAUhC,eAGE,iBAAV+C,EACFf,EAAUZ,cAGE,kBAAV2B,GAIG,OAAVA,GAAmC,iBAAVA,EAHpBf,EAAUd,OAOfnO,MAAMC,QAAQ+P,GACTf,EAAUpB,YAGZoB,EAAUb,UAAU,EAE7B9K,EAAO2M,YAAc,KAAM,IAAAC,EAAAC,EACzB,OAAOvQ,EAAW0D,EAAOc,UAAUgL,UAC/B9L,EAAOc,UAAUgL,SACa,SAA9B9L,EAAOc,UAAUgL,SACf9L,EAAOsM,kBAEuDM,OAD9DA,EACuB,OADvBC,EACA/M,EAAMU,QAAQmL,gBAAS,EAAvBkB,EAA0B7M,EAAOc,UAAUgL,WAAmBc,EAC9DjB,EAAU3L,EAAOc,UAAUgL,SAA4B,EAE/D9L,EAAO8M,aAAe,KAAM,IAAAC,EAAAC,EAAAC,EAC1B,OACsC,OAApCF,EAAC/M,EAAOc,UAAUoM,qBAAkBH,KACF,OADUC,EAC3ClN,EAAMU,QAAQ2M,sBAAmBH,YAASC,EAC1CnN,EAAMU,QAAQ4M,gBAAaH,MAC1BjN,EAAOsB,UAAU,EAIvBtB,EAAOqN,cAAgB,IAAMrN,EAAOsN,kBAAoB,EAExDtN,EAAOuN,eAAiB,KAAA,IAAAC,EAAA,OACQ,OADRA,EACtB1N,EAAM8D,WAAWqI,gBAAjBuB,OAA8BA,EAA9BA,EAAgCpJ,MAAK7H,GAAKA,EAAE4D,KAAOH,EAAOG,WAA1DqN,EAAAA,EAA+Dd,KAAK,EAEtE1M,EAAOsN,eAAiB,KAAA,IAAAG,EAAAC,EAAA,OAC4C,OAD5CD,EACQ,OADRC,EACtB5N,EAAM8D,WAAWqI,oBAAa,EAA9ByB,EAAgCC,WAAUpR,GAAKA,EAAE4D,KAAOH,EAAOG,MAAGsN,GAAK,CAAC,EAE1EzN,EAAO4N,eAAiBlB,IACtB5M,EAAM+N,kBAAiBxR,IACrB,MAAMyP,EAAW9L,EAAO2M,cAClBmB,EAAiBzR,MAAAA,OAAAA,EAAAA,EAAK+H,MAAK7H,GAAKA,EAAE4D,KAAOH,EAAOG,KAEhD4N,EAAYjS,EAChB4Q,EACAoB,EAAiBA,EAAepB,WAAQhL,GAMxC,IAAAsM,EAFF,GACEC,EAAuBnC,EAA6BiC,EAAW/N,GAE/D,OAA2C,OAA3CgO,EAAU,MAAH3R,OAAG,EAAHA,EAAKgI,QAAO9H,GAAKA,EAAE4D,KAAOH,EAAOG,MAAG6N,EAAI,GAGjD,MAAME,EAAe,CAAE/N,GAAIH,EAAOG,GAAIuM,MAAOqB,GAEzB,IAAAI,EAApB,OAAIL,EAOE,OANJK,EACK,MAAH9R,OAAG,EAAHA,EAAKiH,KAAI/G,GACHA,EAAE4D,KAAOH,EAAOG,GACX+N,EAEF3R,KACP4R,EAAI,GAIH,MAAH9R,GAAAA,EAAKmB,OACA,IAAInB,EAAK6R,GAGX,CAACA,EAAa,GACrB,CACH,EAGHzG,UAAWA,CACT1H,EACAqO,KAEArO,EAAIkM,cAAgB,GACpBlM,EAAIsO,kBAAoB,EAAE,EAG5B7K,YAAqC1D,IACnCA,EAAM+N,iBAAoB9R,IACxB,MAAM0G,EAAc3C,EAAM8I,oBAkB1B9I,MAAAA,EAAMU,QAAQ2L,uBAAdrM,EAAMU,QAAQ2L,uBAhBI9P,IAA4B,IAAAiS,EAC5C,OAAqC,OAArCA,EAAOxS,EAAiBC,EAASM,SAAI,EAA9BiS,EAAgCjK,QAAOA,IAC5C,MAAMrE,EAASyC,EAAY2B,MAAK7H,GAAKA,EAAE4D,KAAOkE,EAAOlE,KAErD,GAAIH,EAAQ,CAGV,GAAIiO,EAFajO,EAAO2M,cAEatI,EAAOqI,MAAO1M,GACjD,OAAO,CAEX,CAEA,OAAO,CAAI,GACX,GAG2C,EAGjDF,EAAMyO,mBAAqBC,IAAgB,IAAAC,EAAAC,EACzC5O,EAAM+N,iBACJW,EAAe,GAAsCC,OAApCA,SAAAC,EAAG5O,EAAM6O,qBAAND,EAAoBzC,eAAawC,EAAI,GAC1D,EAGH3O,EAAMuJ,uBAAyB,IAAMvJ,EAAM0M,kBAC3C1M,EAAM8O,oBAAsB,MACrB9O,EAAM+O,sBAAwB/O,EAAMU,QAAQoO,sBAC/C9O,EAAM+O,qBAAuB/O,EAAMU,QAAQoO,oBAAoB9O,IAG7DA,EAAMU,QAAQsO,kBAAoBhP,EAAM+O,qBACnC/O,EAAMuJ,yBAGRvJ,EAAM+O,uBACd,GAIE,SAASZ,EACdnC,EACAY,EACA1M,GAEA,SACG8L,IAAYA,EAAS7B,aAClB6B,EAAS7B,WAAWyC,EAAO1M,SAEd,IAAV0M,GACW,iBAAVA,IAAuBA,CAEnC,CCzaA,MA2GaqC,EAAiB,CAC5BC,IA5G8BA,CAAC/O,EAAUgP,EAAWC,IAG7CA,EAAUnG,QAAO,CAACiG,EAAKG,KAC5B,MAAMC,EAAYD,EAAK/O,SAASH,GAChC,OAAO+O,GAA4B,iBAAdI,EAAyBA,EAAY,EAAE,GAC3D,GAuGH9P,IApG8BA,CAACW,EAAUgP,EAAWC,KACpD,IAAI5P,EAaJ,OAXA4P,EAAU9R,SAAQ2C,IAChB,MAAM2M,EAAQ3M,EAAIK,SAAiBH,GAGxB,MAATyM,IACCpN,EAAOoN,QAAkBhL,IAARpC,GAAqBoN,GAASA,KAEhDpN,EAAMoN,EACR,IAGKpN,CAAG,EAuFVD,IApF8BA,CAACY,EAAUgP,EAAWC,KACpD,IAAI7P,EAYJ,OAVA6P,EAAU9R,SAAQ2C,IAChB,MAAM2M,EAAQ3M,EAAIK,SAAiBH,GAExB,MAATyM,IACCrN,EAAOqN,QAAkBhL,IAARrC,GAAqBqN,GAASA,KAEhDrN,EAAMqN,EACR,IAGKrN,CAAG,EAwEVgQ,OArEiCA,CAACpP,EAAUgP,EAAWC,KACvD,IAAI5P,EACAD,EAcJ,OAZA6P,EAAU9R,SAAQ2C,IAChB,MAAM2M,EAAQ3M,EAAIK,SAAiBH,GACtB,MAATyM,SACUhL,IAARpC,EACEoN,GAASA,IAAOpN,EAAMD,EAAMqN,IAE5BpN,EAAMoN,IAAOpN,EAAMoN,GACnBrN,EAAOqN,IAAOrN,EAAMqN,IAE5B,IAGK,CAACpN,EAAKD,EAAI,EAsDjBiQ,KAnD+BA,CAACrP,EAAUsP,KAC1C,IAAIC,EAAQ,EACRR,EAAM,EASV,GAPAO,EAASnS,SAAQ2C,IACf,IAAI2M,EAAQ3M,EAAIK,SAAiBH,GACpB,MAATyM,IAAkBA,GAASA,IAAUA,MACrC8C,EAAQR,GAAOtC,EACnB,IAGE8C,EAAO,OAAOR,EAAMQ,CAExB,EAuCAC,OApCiCA,CAACxP,EAAUsP,KAC5C,IAAKA,EAAS/R,OACZ,OAGF,MAAMkS,EAASH,EAASjM,KAAIvD,GAAOA,EAAIK,SAASH,KAChD,IAAKxD,EAAciT,GACjB,OAEF,GAAsB,IAAlBA,EAAOlS,OACT,OAAOkS,EAAO,GAGhB,MAAMC,EAAMhR,KAAKiR,MAAMF,EAAOlS,OAAS,GACjCqS,EAAOH,EAAOI,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IACvC,OAAON,EAAOlS,OAAS,GAAM,EAAIqS,EAAKF,IAAQE,EAAKF,EAAM,GAAME,EAAKF,IAAS,CAAC,EAsB9EM,OAnBiCA,CAAChQ,EAAUsP,IACrC7S,MAAMwT,KAAK,IAAIC,IAAIZ,EAASjM,KAAI/G,GAAKA,EAAE6D,SAASH,MAAYyP,UAmBnEU,YAhBsCA,CAACnQ,EAAUsP,IAC1C,IAAIY,IAAIZ,EAASjM,KAAI/G,GAAKA,EAAE6D,SAASH,MAAYoQ,KAgBxDb,MAbgCA,CAACc,EAAWf,IACrCA,EAAS/R,QCsIL+S,EAA+B,CAC1C1E,oBAAqBA,KAIZ,CACL2E,eAAgBC,IAAK,IAAAC,EAAAC,EAAA,OAA2C,OAA3CD,EAAIC,OAAJA,EAAKF,EAAMrQ,aAA4B,MAAnCuQ,EAA2B5G,cAAQ,EAAnC4G,EAA2B5G,YAAY2G,EAAI,IAAI,EACxEE,cAAe,SAInB7E,gBAAkBC,IACT,CACL6E,SAAU,MACP7E,IAIPE,kBACEpM,IAEO,CACLgR,iBAAkB7U,EAAiB,WAAY6D,GAC/CiR,kBAAmB,YAIvBlQ,aAAcA,CACZb,EACAF,KAEAE,EAAOgR,eAAiB,KACtBlR,EAAMmR,aAAY5U,GAEZA,MAAAA,GAAAA,EAAKuF,SAAS5B,EAAOG,IAChB9D,EAAIgI,QAAO9H,GAAKA,IAAMyD,EAAOG,KAG/B,IAAQ,MAAH9D,EAAAA,EAAO,GAAK2D,EAAOG,KAC/B,EAGJH,EAAOkR,YAAc,KAAM,IAAAnE,EAAAC,EACzB,OACkCD,OAAhCA,EAAC/M,EAAOc,UAAUqQ,iBAAcpE,KACH,OADWC,EACvClN,EAAMU,QAAQ2Q,iBAAcnE,OAC1BhN,EAAOsB,cAAgBtB,EAAOc,UAAUsQ,iBAAiB,EAIhEpR,EAAOqR,aAAe,KAAM,IAAAC,EAC1B,OAAgC,OAAhCA,EAAOxR,EAAM8D,WAAWiN,eAAQ,EAAzBS,EAA2B1P,SAAS5B,EAAOG,GAAG,EAGvDH,EAAOuR,gBAAkB,KAAA,IAAAC,EAAA,OAA+B,OAA/BA,EAAM1R,EAAM8D,WAAWiN,eAAQ,EAAzBW,EAA2BC,QAAQzR,EAAOG,GAAG,EAE5EH,EAAO0R,yBAA2B,KAChC,MAAMC,EAAW3R,EAAOkR,cAExB,MAAO,KACAS,GACL3R,EAAOgR,gBAAgB,CACxB,EAEHhR,EAAO4R,qBAAuB,KAC5B,MAAMrF,EAAWzM,EAAM0M,kBAAkBC,SAAS,GAE5CC,EAAQH,MAAAA,OAAAA,EAAAA,EAAUnM,SAASJ,EAAOG,IAExC,MAAqB,iBAAVuM,EACFqC,EAAeC,IAGsB,kBAA1C6C,OAAOtQ,UAAUwI,SAAS+H,KAAKpF,GAC1BqC,EAAeM,YADxB,CAEA,EAEFrP,EAAO+R,iBAAmB,KAAM,IAAAC,EAAAC,EAC9B,IAAKjS,EACH,MAAM,IAAIgC,MAGZ,OAAO1F,EAAW0D,EAAOc,UAAU8P,eAC/B5Q,EAAOc,UAAU8P,cACkB,SAAnC5Q,EAAOc,UAAU8P,cACf5Q,EAAO4R,uBAGNI,OAH4BA,EACD,OADCC,EAC7BnS,EAAMU,QAAQuO,qBAAc,EAA5BkD,EACEjS,EAAOc,UAAU8P,gBAClBoB,EACDjD,EACE/O,EAAOc,UAAU8P,cAClB,CACR,EAGHpN,YAAqC1D,IACnCA,EAAMmR,YAAclV,GAAyC,MAA9B+D,EAAMU,QAAQsQ,sBAAgB,EAA9BhR,EAAMU,QAAQsQ,iBAAmB/U,GAEhE+D,EAAMoS,cAAgB1D,IAAgB,IAAA2D,EAAAzD,EACpC5O,EAAMmR,YAAYzC,EAAe,GAAiC2D,OAA/BA,SAAAzD,EAAG5O,EAAM6O,qBAAND,EAAoBmC,UAAQsB,EAAI,GAAG,EAG3ErS,EAAMsS,sBAAwB,IAAMtS,EAAM8O,sBAC1C9O,EAAMuS,mBAAqB,MACpBvS,EAAMwS,qBAAuBxS,EAAMU,QAAQ6R,qBAC9CvS,EAAMwS,oBAAsBxS,EAAMU,QAAQ6R,mBAAmBvS,IAG3DA,EAAMU,QAAQ+R,iBAAmBzS,EAAMwS,oBAClCxS,EAAMsS,wBAGRtS,EAAMwS,sBACd,EAGH7K,UAAWA,CACT1H,EACAD,KAEAC,EAAIsR,aAAe,MAAQtR,EAAIyS,iBAC/BzS,EAAIqR,iBAAmBnR,IACrB,GAAIF,EAAI0S,qBAAqBzK,eAAe/H,GAC1C,OAAOF,EAAI0S,qBAAqBxS,GAGlC,MAAMD,EAASF,EAAMmI,UAAUhI,GAE/B,OAAKD,MAAAA,GAAAA,EAAQc,UAAUsQ,kBAIvBrR,EAAI0S,qBAAqBxS,GAAYD,EAAOc,UAAUsQ,iBACpDrR,EAAI2H,UAGC3H,EAAI0S,qBAAqBxS,IAPvBF,EAAIK,SAASH,EAOmB,EAE3CF,EAAI0S,qBAAuB,EAAE,EAG/B5S,WAAYA,CACVK,EACAF,EACAD,EACAD,KAKAI,EAAKmR,aAAe,IAClBrR,EAAOqR,gBAAkBrR,EAAOG,KAAOJ,EAAIyS,iBAC7CtS,EAAKwS,iBAAmB,KAAOxS,EAAKmR,gBAAkBrR,EAAOqR,eAC7DnR,EAAKyS,gBAAkB,KAAA,IAAAC,EAAA,OACpB1S,EAAKmR,iBAAmBnR,EAAKwS,sBAAmC,OAAZE,EAAC7S,EAAI6H,WAAJgL,EAAapV,OAAM,CAAA,GAIxE,SAAS+E,EACdE,EACAoO,EACAE,GAEA,GAAKF,MAAAA,IAAAA,EAAUrT,SAAWuT,EACxB,OAAOtO,EAGT,MAAMoQ,EAAqBpQ,EAAY4B,QACrCyO,IAAQjC,EAASjP,SAASkR,EAAI3S,MAGhC,GAA0B,WAAtB4Q,EACF,OAAO8B,EAOT,MAAO,IAJiBhC,EACrBvN,KAAIyP,GAAKtQ,EAAY2B,MAAK0O,GAAOA,EAAI3S,KAAO4S,MAC5C1O,OAAOC,YAEqBuO,EACjC,CC3VO,MAAMG,EAA+B,CAC1CjH,gBAAkBC,IACT,CACLiH,YAAa,MACVjH,IAIPE,kBACEpM,IAEO,CACLoT,oBAAqBjX,EAAiB,cAAe6D,KAIzDe,aAAcA,CACZb,EACAF,KAEAE,EAAOmT,SAAW1V,GAChB2V,GAAY,CAACC,EAAuBvT,EAAOsT,MAC3CnR,GAAWA,EAAQ0L,WAAUpR,GAAKA,EAAE4D,KAAOH,EAAOG,MAClDZ,EAAeO,EAAMU,QAAS,iBAEhCR,EAAOsT,iBAAmBF,IAAY,IAAAG,EAEpC,OAAOA,OAAAA,EADSF,EAAuBvT,EAAOsT,GAC/B,SAARG,EAAAA,EAAYpT,MAAOH,EAAOG,EAAE,EAErCH,EAAOwT,gBAAkBJ,IAAY,IAAAK,EACnC,MAAMxR,EAAUoR,EAAuBvT,EAAOsT,GAC9C,cAAOK,EAAAxR,EAAQA,EAAQzE,OAAS,WAAzBiW,EAA6BtT,MAAOH,EAAOG,EAAE,CACrD,EAGHqD,YAAqC1D,IACnCA,EAAM4T,eAAiB3X,GACY,MAAjC+D,EAAMU,QAAQ0S,yBAAmB,EAAjCpT,EAAMU,QAAQ0S,oBAAsBnX,GACtC+D,EAAM6T,iBAAmBnF,IAAgB,IAAAC,EACvC3O,EAAM4T,eACJlF,EAAe,UAAEC,EAAG3O,EAAM6O,aAAasE,aAAWxE,EAAI,GACvD,EAEH3O,EAAMwC,mBAAqB7E,GACzB,IAAM,CACJqC,EAAM8D,WAAWqP,YACjBnT,EAAM8D,WAAWiN,SACjB/Q,EAAMU,QAAQuQ,qBAEhB,CAACkC,EAAapC,EAAUE,IACrB9O,IAGC,IAAI2R,EAA2C,GAG/C,GAAgB,MAAXX,GAAAA,EAAazV,OAEX,CACL,MAAMqW,EAAkB,IAAIZ,GAGtBa,EAAc,IAAI7R,GAKxB,KAAO6R,EAAYtW,QAAUqW,EAAgBrW,QAAQ,CACnD,MAAMuW,EAAiBF,EAAgBG,QACjCC,EAAaH,EAAYnG,WAC7BpR,GAAKA,EAAE4D,KAAO4T,IAEZE,GAAc,GAChBL,EAAetW,KAAKwW,EAAYI,OAAOD,EAAY,GAAG,GAE1D,CAGAL,EAAiB,IAAIA,KAAmBE,EAC1C,MAtBEF,EAAiB3R,EAwBnB,OAAOM,EAAaqR,EAAgB/C,EAAUE,EAAkB,GAEpExR,EAAeO,EAAMU,QAAS,cAC/B,GCNQ2T,EAA8B,CACzCpI,gBAAkBC,IACT,CACLnI,cAR0D,CAC9DC,KAAM,GACNC,MAAO,OAOAiI,IAIPE,kBACEpM,IAEO,CACLsU,sBAAuBnY,EAAiB,gBAAiB6D,KAI7De,aAAcA,CACZb,EACAF,KAEAE,EAAOqU,IAAMjB,IACX,MAAMkB,EAAYtU,EACfqC,iBACAiB,KAAI/G,GAAKA,EAAE4D,KACXkE,OAAOC,SAEVxE,EAAMyU,kBAAiBlY,IAAO,IAAAmY,EAAAC,EACFC,EAAAC,EAUDC,EAAAC,EAVzB,MAAiB,UAAbzB,EACK,CACLtP,MAAgB4Q,OAAVA,EAACrY,MAAAA,OAAAA,EAAAA,EAAKyH,MAAI4Q,EAAI,IAAIrQ,QAAO9H,KAAe,MAAT+X,GAAAA,EAAW1S,SAASrF,MACzDwH,MAAO,KACS4Q,OAAXA,EAACtY,MAAAA,OAAAA,EAAAA,EAAK0H,OAAK4Q,EAAI,IAAItQ,QAAO9H,WAAM+X,GAAAA,EAAW1S,SAASrF,SACpD+X,IAKQ,SAAblB,EACK,CACLtP,KAAM,KACS8Q,OAAVA,EAACvY,MAAAA,OAAAA,EAAAA,EAAKyH,MAAI8Q,EAAI,IAAIvQ,QAAO9H,WAAM+X,GAAAA,EAAW1S,SAASrF,SACnD+X,GAELvQ,OAAkB8Q,OAAXA,EAACxY,MAAAA,OAAAA,EAAAA,EAAK0H,OAAK8Q,EAAI,IAAIxQ,QAAO9H,KAAM+X,MAAAA,GAAAA,EAAW1S,SAASrF,OAIxD,CACLuH,MAAgB0Q,OAAVA,EAACnY,MAAAA,OAAAA,EAAAA,EAAKyH,MAAI0Q,EAAI,IAAInQ,QAAO9H,KAAe,MAAT+X,GAAAA,EAAW1S,SAASrF,MACzDwH,OAAkB0Q,OAAXA,EAACpY,MAAAA,OAAAA,EAAAA,EAAK0H,OAAK0Q,EAAI,IAAIpQ,QAAO9H,KAAM+X,MAAAA,GAAAA,EAAW1S,SAASrF,MAC5D,GACD,EAGJyD,EAAO8U,UAAY,IACG9U,EAAOqC,iBAERhE,MACjB9B,IAAC,IAAAwY,EAAA9T,EAAA+L,EAAA,OAC2B,OAA1B+H,EAACxY,EAAEuE,UAAUkU,gBAAaD,KAEG,OAFK9T,EACA,OADA+L,EACjClN,EAAMU,QAAQyU,qBAAmBjI,EAChClN,EAAMU,QAAQwU,gBAAa/T,EACtB,IAIbjB,EAAOkV,YAAc,KACnB,MAAMC,EAAgBnV,EAAOqC,iBAAiBiB,KAAI/G,GAAKA,EAAE4D,MAEnD2D,KAAEA,EAAIC,MAAEA,GAAUjE,EAAM8D,WAAWC,cAEnCuR,EAASD,EAAc9W,MAAK9B,GAAS,MAAJuH,OAAI,EAAJA,EAAMlC,SAASrF,KAChD8Y,EAAUF,EAAc9W,MAAK9B,GAAU,MAALwH,OAAK,EAALA,EAAOnC,SAASrF,KAExD,OAAO6Y,EAAS,SAASC,GAAU,OAAe,EAGpDrV,EAAOsV,eAAiB,KAAM,IAAA9H,EAAAC,EAC5B,MAAM2F,EAAWpT,EAAOkV,cAExB,OAAO9B,SAAQ5F,EACmB,OADnBC,EACX3N,EAAM8D,WAAWC,gBAAjB4J,OAA8BA,EAA9BA,EAAiC2F,SAAjC3F,EAAAA,EAA4CgE,QAAQzR,EAAOG,KAAGqN,GAAK,EACnE,CAAC,CACN,EAGH/F,UAAWA,CACT1H,EACAD,KAEAC,EAAIwV,sBAAwB9X,GAC1B,IAAM,CACJsC,EAAIyV,sBACJ1V,EAAM8D,WAAWC,cAAcC,KAC/BhE,EAAM8D,WAAWC,cAAcE,SAEjC,CAAC+E,EAAUhF,EAAMC,KACf,MAAM0R,EAAyB,IAAS,MAAJ3R,EAAAA,EAAQ,MAAc,MAALC,EAAAA,EAAS,IAE9D,OAAO+E,EAASzE,QAAO9H,IAAMkZ,EAAa7T,SAASrF,EAAEyD,OAAOG,KAAI,GAElEZ,EAAeO,EAAMU,QAAS,cAEhCT,EAAI2V,oBAAsBjY,GACxB,IAAM,CAACsC,EAAIyV,sBAAuB1V,EAAM8D,WAAWC,cAAcC,QACjE,CAACgF,EAAUhF,KACU,MAAJA,EAAAA,EAAQ,IACpBR,KAAIrD,GAAY6I,EAAS1E,MAAKlE,GAAQA,EAAKF,OAAOG,KAAOF,MACzDoE,OAAOC,SACPhB,KAAI/G,IAAM,IAAKA,EAAG6W,SAAU,YAIjC7T,EAAeO,EAAMU,QAAS,cAEhCT,EAAI4V,qBAAuBlY,GACzB,IAAM,CAACsC,EAAIyV,sBAAuB1V,EAAM8D,WAAWC,cAAcE,SACjE,CAAC+E,EAAU/E,KACW,MAALA,EAAAA,EAAS,IACrBT,KAAIrD,GAAY6I,EAAS1E,MAAKlE,GAAQA,EAAKF,OAAOG,KAAOF,MACzDoE,OAAOC,SACPhB,KAAI/G,IAAM,IAAKA,EAAG6W,SAAU,aAIjC7T,EAAeO,EAAMU,QAAS,aAC/B,EAGHgD,YAAqC1D,IACnCA,EAAMyU,iBAAmBxY,GACY,MAAnC+D,EAAMU,QAAQ4T,2BAAqB,EAAnCtU,EAAMU,QAAQ4T,sBAAwBrY,GAExC+D,EAAM8V,mBAAqBpH,IAAY,IAAAC,EAAAC,EAAA,OACrC5O,EAAMyU,iBACJ/F,EA5IwD,CAC9D1K,KAAM,GACNC,MAAO,IA4IoC0K,OADHA,EAC9BC,OAD8BA,EAC9B5O,EAAM6O,mBAAND,EAAAA,EAAoB7K,eAAa4K,EA9ImB,CAC9D3K,KAAM,GACNC,MAAO,IA6IF,EAEHjE,EAAM+V,uBAAyBzC,IAAY,IAAA0C,EACzC,MAAMC,EAAejW,EAAM8D,WAAWC,cAEvB,IAAAmS,EAAAC,EAAf,OAAK7C,EAGE9O,QAAQwR,OAADA,EAACC,EAAa3C,SAAb0C,EAAAA,EAAwBtY,QAF9B8G,SAAyB,OAAjB0R,EAAAD,EAAajS,WAAI,EAAjBkS,EAAmBxY,UAAUyY,OAAJA,EAAIF,EAAahS,YAAbkS,EAAAA,EAAoBzY,QAEpB,EAGhDsC,EAAMoW,mBAAqBzY,GACzB,IAAM,CAACqC,EAAM8I,oBAAqB9I,EAAM8D,WAAWC,cAAcC,QACjE,CAACE,EAAYF,KACHA,MAAAA,EAAAA,EAAQ,IACbR,KAAIrD,GAAY+D,EAAWI,MAAKpE,GAAUA,EAAOG,KAAOF,MACxDoE,OAAOC,UAEZ/E,EAAeO,EAAMU,QAAS,iBAGhCV,EAAMqW,oBAAsB1Y,GAC1B,IAAM,CAACqC,EAAM8I,oBAAqB9I,EAAM8D,WAAWC,cAAcE,SACjE,CAACC,EAAYD,KACHA,MAAAA,EAAAA,EAAS,IACdT,KAAIrD,GAAY+D,EAAWI,MAAKpE,GAAUA,EAAOG,KAAOF,MACxDoE,OAAOC,UAEZ/E,EAAeO,EAAMU,QAAS,iBAGhCV,EAAMsW,qBAAuB3Y,GAC3B,IAAM,CACJqC,EAAM8I,oBACN9I,EAAM8D,WAAWC,cAAcC,KAC/BhE,EAAM8D,WAAWC,cAAcE,SAEjC,CAACC,EAAYF,EAAMC,KACjB,MAAM0R,EAAyB,IAAS,MAAJ3R,EAAAA,EAAQ,MAAc,MAALC,EAAAA,EAAS,IAE9D,OAAOC,EAAWK,QAAO9H,IAAMkZ,EAAa7T,SAASrF,EAAE4D,KAAI,GAE7DZ,EAAeO,EAAMU,QAAS,gBAC/B,GClHE,MAAM6V,EAAsB,CACjChG,KAAM,IACNiG,QAAS,GACTC,QAAShL,OAAOiL,kBAYLC,EAA6B,CACxC5K,oBAAqBA,IACZwK,EAETtK,gBAAkBC,IACT,CACL0K,aAAc,CAAE,EAChBC,iBAhBgE,CACpEC,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,OAWZjL,IAIPE,kBACEpM,IAEO,CACLoX,iBAAkB,QAClBC,sBAAuB,MACvBC,qBAAsBnb,EAAiB,eAAgB6D,GACvDuX,yBAA0Bpb,EAAiB,mBAAoB6D,KAInEe,aAAcA,CACZb,EACAF,KAEAE,EAAOsX,QAAU,KAAM,IAAAC,EAAAtW,EAAAuW,EACrB,MAAMC,EAAa3X,EAAM8D,WAAW8S,aAAa1W,EAAOG,IAExD,OAAOxB,KAAKW,IACVX,KAAKU,IACqBkY,OADlBA,EACNvX,EAAOc,UAAUwV,SAAOiB,EAAIlB,EAAoBC,QACb,OADoBrV,EACvDwW,MAAAA,EAAAA,EAAczX,EAAOc,UAAUuP,MAAIpP,EAAIoV,EAAoBhG,MAErCmH,OADvBA,EACDxX,EAAOc,UAAUyV,SAAOiB,EAAInB,EAAoBE,QACjD,EAGHvW,EAAO0X,SAAWja,GAChB2V,GAAY,CACVA,EACAC,EAAuBvT,EAAOsT,GAC9BtT,EAAM8D,WAAW8S,gBAEnB,CAACtD,EAAUnR,IACTA,EACG0V,MAAM,EAAG3X,EAAOmT,SAASC,IACzBrK,QAAO,CAACiG,EAAKhP,IAAWgP,EAAMhP,EAAOsX,WAAW,IACrD/X,EAAeO,EAAMU,QAAS,iBAGhCR,EAAO4X,SAAWna,GAChB2V,GAAY,CACVA,EACAC,EAAuBvT,EAAOsT,GAC9BtT,EAAM8D,WAAW8S,gBAEnB,CAACtD,EAAUnR,IACTA,EACG0V,MAAM3X,EAAOmT,SAASC,GAAY,GAClCrK,QAAO,CAACiG,EAAKhP,IAAWgP,EAAMhP,EAAOsX,WAAW,IACrD/X,EAAeO,EAAMU,QAAS,iBAGhCR,EAAO6X,UAAY,KACjB/X,EAAMgY,iBAAgBC,IAAiC,IAA9B,CAAC/X,EAAOG,IAAK6X,KAAMC,GAAMF,EAChD,OAAOE,CAAI,GACX,EAEJjY,EAAOkY,aAAe,KAAM,IAAAnL,EAAAC,EAC1B,OACkCD,OAAhCA,EAAC/M,EAAOc,UAAUqX,iBAAcpL,KACGC,OADKA,EACvClN,EAAMU,QAAQ4X,uBAAoBpL,EAAS,EAGhDhN,EAAOqY,cAAgB,IACdvY,EAAM8D,WAAW+S,iBAAiBK,mBAAqBhX,EAAOG,EACtE,EAGHuC,aAAcA,CACZf,EACA7B,KAEA6B,EAAO2V,QAAU,KACf,IAAItI,EAAM,EAEV,MAAM9R,EAAWyE,IAGR,IAAA2W,EAFH3W,EAAOmB,WAAWtF,OACpBmE,EAAOmB,WAAW1F,QAAQF,GAE1B8R,GAA8BsJ,OAA3BA,EAAI3W,EAAO3B,OAAOsX,WAASgB,EAAI,CACpC,EAKF,OAFApb,EAAQyE,GAEDqN,CAAG,EAEZrN,EAAO+V,SAAW,KAChB,GAAI/V,EAAOpD,MAAQ,EAAG,CACpB,MAAMga,EAAoB5W,EAAOsB,YAAYoC,QAAQ1D,EAAOpD,MAAQ,GACpE,OAAOga,EAAkBb,WAAaa,EAAkBjB,SAC1D,CAEA,OAAO,CAAC,EAEV3V,EAAO6W,iBAAmBC,IACxB,MAAMzY,EAASF,EAAMmI,UAAUtG,EAAO3B,OAAOG,IACvCuY,EAAkB,MAAN1Y,OAAM,EAANA,EAAQkY,eAE1B,OAAQS,IACN,IAAK3Y,IAAW0Y,EACd,OAKF,GAFmB,MAAjBC,EAAUC,SAAVD,EAAUC,UAERC,EAAkBF,IAEhBA,EAAEG,SAAWH,EAAEG,QAAQtb,OAAS,EAClC,OAIJ,MAAMqZ,EAAYlV,EAAO2V,UAEnBL,EAAwCtV,EAC1CA,EAAOuB,iBAAiBI,KAAI/G,GAAK,CAACA,EAAEyD,OAAOG,GAAI5D,EAAEyD,OAAOsX,aACxD,CAAC,CAACtX,EAAOG,GAAIH,EAAOsX,YAElByB,EAAUF,EAAkBF,GAC9Bha,KAAKC,MAAM+Z,EAAEG,QAAQ,GAAIC,SACxBJ,EAAiBI,QAEhBC,EAAqC,CAAA,EAErCC,EAAeA,CACnBC,EACAC,KAE0B,iBAAfA,IAIXrZ,EAAMsZ,qBAAoB/c,IAAO,IAAAgd,EAAAC,EAC/B,MAAMC,EACoC,QAAxCzZ,EAAMU,QAAQ2W,uBAAmC,EAAI,EACjDL,GACHqC,GAA8BE,OAApBA,EAAO,MAAHhd,OAAG,EAAHA,EAAKua,aAAWyC,EAAI,IAAME,EACrCxC,EAAkBpY,KAAKU,IAC3ByX,GAA6B,OAAlBwC,QAAIjd,SAAAA,EAAKwa,WAASyC,EAAI,IAChC,SAUH,OAPAjd,EAAI4a,kBAAkB7Z,SAAQoc,IAA4B,IAA1BvZ,EAAUwZ,GAAWD,EACnDR,EAAgB/Y,GACdtB,KAAKC,MACsD,IAAzDD,KAAKU,IAAIoa,EAAaA,EAAa1C,EAAiB,IAClD,GAAG,IAGJ,IACF1a,EACHya,cACAC,kBACD,IAIkC,aAAnCjX,EAAMU,QAAQ0W,kBACA,QAAdgC,GAEApZ,EAAMgY,iBAAgBzb,IAAQ,IACzBA,KACA2c,MAEP,EAGIU,EAAUP,GAAwBF,EAAa,OAAQE,GAEvDQ,EAASR,IACbF,EAAa,MAAOE,GAEpBrZ,EAAMsZ,qBAAoB/c,IAAQ,IAC7BA,EACH2a,kBAAkB,EAClBJ,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBE,kBAAmB,MAClB,EAGC2C,EAAuCnB,IC9aV,oBAAboB,SAA2BA,SAAW,MDgb5D,MAAMC,EAAc,CAClBC,YAAcpB,GAAkBe,EAAOf,EAAEI,SACzCiB,UAAYrB,IACK,MAAfiB,GAAAA,EAAiBK,oBACf,YACAH,EAAYC,aAEC,MAAfH,GAAAA,EAAiBK,oBACf,UACAH,EAAYE,WAEdL,EAAMhB,EAAEI,QAAQ,GAIdmB,EAAc,CAClBH,YAAcpB,IACRA,EAAEwB,aACJxB,EAAEyB,iBACFzB,EAAE0B,mBAEJX,EAAOf,EAAEG,QAAQ,GAAIC,UACd,GAETiB,UAAYrB,IAAkB,IAAA2B,EACb,MAAfV,GAAAA,EAAiBK,oBACf,YACAC,EAAYH,aAEC,MAAfH,GAAAA,EAAiBK,oBACf,WACAC,EAAYF,WAEVrB,EAAEwB,aACJxB,EAAEyB,iBACFzB,EAAE0B,mBAEJV,EAAkB,OAAbW,EAAC3B,EAAEG,QAAQ,SAAE,EAAZwB,EAAcvB,QAAQ,GAI1BwB,IAAqBC,KACvB,CAAEC,SAAS,GAGX5B,EAAkBF,IACpBiB,MAAAA,GAAAA,EAAiBc,iBACf,YACAR,EAAYH,YACZQ,GAEFX,MAAAA,GAAAA,EAAiBc,iBACf,WACAR,EAAYF,UACZO,KAGFX,MAAAA,GAAAA,EAAiBc,iBACf,YACAZ,EAAYC,YACZQ,GAEFX,MAAAA,GAAAA,EAAiBc,iBACf,UACAZ,EAAYE,UACZO,IAIJza,EAAMsZ,qBAAoB/c,IAAQ,IAC7BA,EACHua,YAAamC,EACblC,YACAC,YAAa,EACbC,gBAAiB,EACjBE,oBACAD,iBAAkBhX,EAAOG,MACxB,CACJ,CACF,EAGHqD,YAAqC1D,IACnCA,EAAMgY,gBAAkB/b,GACY,MAAlC+D,EAAMU,QAAQ4W,0BAAoB,EAAlCtX,EAAMU,QAAQ4W,qBAAuBrb,GACvC+D,EAAMsZ,oBAAsBrd,GACY,MAAtC+D,EAAMU,QAAQ6W,8BAAwB,EAAtCvX,EAAMU,QAAQ6W,yBAA2Btb,GAC3C+D,EAAM6a,kBAAoBnM,IAAgB,IAAAC,EACxC3O,EAAMgY,gBACJtJ,EAAe,CAAA,EAAoC,OAAlCC,EAAG3O,EAAM6O,aAAa+H,cAAYjI,EAAI,CACzD,EAAC,EAEH3O,EAAM8a,oBAAsBpM,IAAgB,IAAAqM,EAC1C/a,EAAMsZ,oBACJ5K,EA9S8D,CACpEoI,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,IA0S0B,OADF4D,EACjC/a,EAAM6O,aAAagI,kBAAgBkE,EAhTuB,CACpEjE,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,IA4Sd,EAEHnX,EAAMgb,aAAe,KAAA,IAAAC,EAAAC,EAAA,OAGdD,OAHcA,EACnBC,OADmBA,EACnBlb,EAAM2D,kBAAkB,SAAxBuX,EAAAA,EAA4B3V,QAAQ0D,QAAO,CAACiG,EAAKrN,IACxCqN,EAAMrN,EAAO2V,WACnB,IAAEyD,EAAI,CAAC,EACZjb,EAAMmb,iBAAmB,KAAA,IAAAC,EAAAC,EAAA,OAGlBD,OAHkBA,EACvBC,OADuBA,EACvBrb,EAAM4E,sBAAsB,SAA5ByW,EAAAA,EAAgC9V,QAAQ0D,QAAO,CAACiG,EAAKrN,IAC5CqN,EAAMrN,EAAO2V,WACnB,IAAE4D,EAAI,CAAC,EACZpb,EAAMsb,mBAAqB,KAAA,IAAAC,EAAAC,EAAA,OAGpBD,OAHoBA,EACzBC,OADyBA,EACzBxb,EAAM2E,wBAAwB,SAA9B6W,EAAAA,EAAkCjW,QAAQ0D,QAAO,CAACiG,EAAKrN,IAC9CqN,EAAMrN,EAAO2V,WACnB,IAAE+D,EAAI,CAAC,EACZvb,EAAMyb,kBAAoB,KAAA,IAAAC,EAAAC,EAAA,OAGnBD,OAHmBA,EACxBC,OADwBA,EACxB3b,EAAM8E,uBAAuB,SAA7B6W,EAAAA,EAAiCpW,QAAQ0D,QAAO,CAACiG,EAAKrN,IAC7CqN,EAAMrN,EAAO2V,WACnB,IAAEkE,EAAI,CAAC,CAAA,GAIhB,IAAIE,EAAmC,KAChC,SAASlB,IACd,GAAgC,kBAArBkB,EAAgC,OAAOA,EAElD,IAAIC,GAAY,EAChB,IACE,MAAMnb,EAAU,CACd,WAAIia,GAEF,OADAkB,GAAY,GACL,CACT,GAGIC,EAAOA,OAEbC,OAAOnB,iBAAiB,OAAQkB,EAAMpb,GACtCqb,OAAO5B,oBAAoB,OAAQ2B,EACpC,CAAC,MAAOE,GACPH,GAAY,CACd,CAEA,OADAD,EAAmBC,EACZD,CACT,CAEA,SAAS7C,EAAkBF,GACzB,MAAkC,eAA1BA,EAAiBoD,IAC3B,CE3aO,MAAMC,EAAiC,CAC5CjQ,gBAAkBC,IACT,CACLiQ,iBAAkB,CAAE,KACjBjQ,IAIPE,kBACEpM,IAEO,CACLoc,yBAA0BjgB,EAAiB,mBAAoB6D,KAInEe,aAAcA,CACZb,EACAF,KAEAE,EAAOmc,iBAAmBzP,IACpB1M,EAAOoc,cACTtc,EAAMuc,qBAAoBhgB,IAAQ,IAC7BA,EACH,CAAC2D,EAAOG,IAAU,MAALuM,EAAAA,GAAU1M,EAAO6G,kBAElC,EAEF7G,EAAO6G,aAAe,KAAM,IAAA5F,EAAAuM,EAC1B,MAAM8O,EAAetc,EAAOiC,QAC5B,OAGoDhB,OAHpDA,EACGqb,EAAa9e,OACV8e,EAAaje,MAAKke,GAAKA,EAAE1V,iBACQ,OADO2G,EACxC1N,EAAM8D,WAAWqY,uBAAgB,EAAjCzO,EAAoCxN,EAAOG,MAAGc,CAAS,EAI/DjB,EAAOoc,WAAa,KAAM,IAAArP,EAAAC,EACxB,OACgCD,OAA9BA,EAAC/M,EAAOc,UAAU0b,eAAYzP,KACHC,OADWA,EACrClN,EAAMU,QAAQgc,eAAYxP,EAAS,EAGxChN,EAAOyc,2BAA6B,IAC1B9D,IACN3Y,MAAAA,EAAOmc,kBAAPnc,EAAOmc,iBACHxD,EAAiB+D,OAA4BC,QAChD,CAEJ,EAGHlV,UAAWA,CACT1H,EACAD,KAEAC,EAAIyV,oBAAsB/X,GACxB,IAAM,CAACsC,EAAI4I,cAAe7I,EAAM8D,WAAWqY,oBAC3CW,GACSA,EAAMvY,QAAOnE,GAAQA,EAAKF,OAAO6G,kBAE1CtH,EAAeO,EAAMU,QAAS,cAEhCT,EAAI8c,gBAAkBpf,GACpB,IAAM,CACJsC,EAAI2V,sBACJ3V,EAAIwV,wBACJxV,EAAI4V,0BAEN,CAAC7R,EAAMkC,EAAQjC,IAAU,IAAID,KAASkC,KAAWjC,IACjDxE,EAAeO,EAAMU,QAAS,aAC/B,EAGHgD,YAAqC1D,IACnC,MAAMgd,EAA2BA,CAC/B5gB,EACA6gB,IAEOtf,GACL,IAAM,CACJsf,IACAA,IACG1Y,QAAO9H,GAAKA,EAAEsK,iBACdvD,KAAI/G,GAAKA,EAAE4D,KACX6G,KAAK,QAEV/E,GACSA,EAAQoC,QAAO9H,SAAKA,EAAEsK,oBAAFtK,EAAEsK,kBAE/BtH,EAAeO,EAAMU,QAAS,iBAIlCV,EAAMkd,sBAAwBF,EAC5B,GACA,IAAMhd,EAAMmd,sBAEdnd,EAAM6D,sBAAwBmZ,EAC5B,GACA,IAAMhd,EAAM8I,sBAEd9I,EAAMod,0BAA4BJ,EAChC,GACA,IAAMhd,EAAMoW,uBAEdpW,EAAMqd,2BAA6BL,EACjC,GACA,IAAMhd,EAAMqW,wBAEdrW,EAAMsd,4BAA8BN,EAClC,GACA,IAAMhd,EAAMsW,yBAGdtW,EAAMuc,oBAAsBtgB,GACY,MAAtC+D,EAAMU,QAAQ0b,8BAAwB,EAAtCpc,EAAMU,QAAQ0b,yBAA2BngB,GAE3C+D,EAAMud,sBAAwB7O,IAAgB,IAAAC,EAC5C3O,EAAMuc,oBACJ7N,EAAe,CAAA,EAAwC,OAAtCC,EAAG3O,EAAM6O,aAAasN,kBAAgBxN,EAAI,CAC7D,EAAC,EAGH3O,EAAMwd,wBAA0B5Q,IAAS,IAAA6Q,EACvC7Q,EAAa6Q,OAARA,EAAG7Q,GAAK6Q,GAAKzd,EAAM0d,yBAExB1d,EAAMuc,oBACJvc,EAAM8I,oBAAoBG,QACxB,CAAC0U,EAAKzd,KAAY,IACbyd,EACH,CAACzd,EAAOG,IAAMuM,KAAS1M,MAAAA,EAAOoc,YAAPpc,EAAOoc,iBAEhC,CACF,GACD,EAGHtc,EAAM0d,uBAAyB,KAC5B1d,EAAM8I,oBAAoBvK,MAAK2B,KAAWA,MAAAA,EAAO6G,cAAP7G,EAAO6G,kBAEpD/G,EAAM4d,wBAA0B,IAC9B5d,EAAM8I,oBAAoBvK,MAAK2B,GAA6B,MAAnBA,EAAO6G,kBAAY,EAAnB7G,EAAO6G,iBAElD/G,EAAM6d,qCAAuC,IACnChF,IAAe,IAAAiF,EACrB9d,EAAMwd,wBACJM,OAD2BA,EACzBjF,EAAiB+D,aAAnBkB,EAAAA,EAAgDjB,QACjD,CAEJ,GAIE,SAAStJ,EACdvT,EACAsT,GAEA,OAAQA,EAES,WAAbA,EACEtT,EAAMsd,8BACO,SAAbhK,EACEtT,EAAMod,4BACNpd,EAAMqd,6BALVrd,EAAM6D,uBAMZ,CC/RO,MAAMka,EAA+B,CAC1Cra,YAAqC1D,IACnCA,EAAMge,0BACJhe,EAAMU,QAAQ4I,oBACdtJ,EAAMU,QAAQ4I,mBAAmBtJ,EAAO,cAE1CA,EAAMie,yBAA2B,IAC3Bje,EAAMU,QAAQsO,kBAAoBhP,EAAMge,0BACnChe,EAAMuJ,yBAGRvJ,EAAMge,4BAGfhe,EAAMke,8BACJle,EAAMU,QAAQ+I,wBACdzJ,EAAMU,QAAQ+I,uBAAuBzJ,EAAO,cAC9CA,EAAMme,6BAA+B,IAC9Bne,EAAMke,8BAIJle,EAAMke,gCAHJ,IAAIxU,IAMf1J,EAAMoe,8BACJpe,EAAMU,QAAQkJ,wBACd5J,EAAMU,QAAQkJ,uBAAuB5J,EAAO,cAC9CA,EAAMqe,6BAA+B,KACnC,GAAKre,EAAMoe,8BAIX,OAAOpe,EAAMoe,+BAA+B,CAC7C,GCgCQE,EAAgC,CAC3CrS,gBAAkBC,IACT,CACLqS,kBAAc3c,KACXsK,IAIPE,kBACEpM,IAEO,CACLwe,qBAAsBriB,EAAiB,eAAgB6D,GACvDye,eAAgB,OAChBC,yBAA0Bxe,IAAU,IAAAye,EAClC,MAAM/R,EAEQ+R,OAFHA,EAAG3e,EACX0M,kBACAC,SAAS,KAFEgS,OAEAA,EAFAA,EAEE5V,yBACb7I,EAAOG,UAHIse,EAAAA,EAGCre,WAEf,MAAwB,iBAAVsM,GAAuC,iBAAVA,CAAkB,IAKnE7L,aAAcA,CACZb,EACAF,KAEAE,EAAO0e,mBAAqB,KAAM,IAAA3R,EAAAC,EAAAC,EAAA0R,EAChC,OACsC5R,OAApCA,EAAC/M,EAAOc,UAAU8d,qBAAkB7R,KACH,OADWC,EAC3ClN,EAAMU,QAAQoe,qBAAkB5R,KACL,OADcC,EACzCnN,EAAMU,QAAQ4M,gBAAaH,YAAS0R,QACpC7e,EAAMU,QAAQge,gCAAd1e,EAAMU,QAAQge,yBAA2Bxe,KAAO2e,MAC/C3e,EAAOsB,UAAU,CAEtB,EAGHkC,YAAqC1D,IACnCA,EAAM+e,sBAAwB,IACrBlT,EAAUhC,eAGnB7J,EAAMgf,kBAAoB,KAAM,IAAAlS,EAAAC,EAC9B,MAAQ0R,eAAgBA,GAAmBze,EAAMU,QAEjD,OAAOlE,EAAWiiB,GACdA,EACmB,SAAnBA,EACEze,EAAM+e,wBAC6CjS,OADtBA,EAC7BC,OAD6BA,EAC7B/M,EAAMU,QAAQmL,gBAAdkB,EAAAA,EAA0B0R,IAAyB3R,EACnDjB,EAAU4S,EAAkC,EAGpDze,EAAMif,gBAAkBhjB,IACtB+D,MAAAA,EAAMU,QAAQ8d,sBAAdxe,EAAMU,QAAQ8d,qBAAuBviB,EAAQ,EAG/C+D,EAAMkf,kBAAoBxQ,IACxB1O,EAAMif,gBACJvQ,OAAe9M,EAAY5B,EAAM6O,aAAa0P,aAC/C,CACF,GCSQY,EAA6B,CACxClT,gBAAkBC,IACT,CACLkT,SAAU,CAAE,KACTlT,IAIPE,kBACEpM,IAEO,CACLqf,iBAAkBljB,EAAiB,WAAY6D,GAC/Csf,sBAAsB,IAI1B5b,YAAqC1D,IACnC,IAAIuf,GAAa,EACbC,GAAS,EAEbxf,EAAMyf,mBAAqB,KAAM,IAAAte,EAAAue,EAC/B,GAAKH,GAOL,GAEiC,OAFjCpe,EAC4Bue,OAD5BA,EACE1f,EAAMU,QAAQif,cAAYD,EAC1B1f,EAAMU,QAAQkf,mBAAiBze,GAC9BnB,EAAMU,QAAQmf,gBACf,CACA,GAAIL,EAAQ,OACZA,GAAS,EACTxf,EAAM8f,QAAO,KACX9f,EAAM+f,gBACNP,GAAS,CAAK,GAElB,OAjBExf,EAAM8f,QAAO,KACXP,GAAa,CAAI,GAgBrB,EAEFvf,EAAMggB,YAAc/jB,GAAyC,MAA9B+D,EAAMU,QAAQ2e,sBAAgB,EAA9Brf,EAAMU,QAAQ2e,iBAAmBpjB,GAChE+D,EAAMigB,sBAAwBb,KACxBA,MAAAA,EAAAA,GAAapf,EAAMkgB,wBACrBlgB,EAAMggB,aAAY,GAElBhgB,EAAMggB,YAAY,CAAA,EACpB,EAEFhgB,EAAM+f,cAAgBrR,IAAgB,IAAAyR,EAAAvR,EACpC5O,EAAMggB,YAAYtR,EAAe,CAAA,SAAEyR,EAAGvR,OAAHA,EAAG5O,EAAM6O,mBAAND,EAAAA,EAAoBwQ,UAAQe,EAAI,CAAA,EAAG,EAE3EngB,EAAMogB,qBAAuB,IACpBpgB,EACJqgB,2BACA1T,SAASpO,MAAK0B,GAAOA,EAAIqgB,iBAE9BtgB,EAAMugB,gCAAkC,IAC9B1H,IACa,MAAjBA,EAAUC,SAAVD,EAAUC,UACZ9Y,EAAMigB,uBAAuB,EAGjCjgB,EAAMwgB,sBAAwB,KAC5B,MAAMpB,EAAWpf,EAAM8D,WAAWsb,SAClC,OAAoB,IAAbA,GAAqBrN,OAAOnC,OAAOwP,GAAU7gB,KAAKiG,QAAQ,EAEnExE,EAAMkgB,qBAAuB,KAC3B,MAAMd,EAAWpf,EAAM8D,WAAWsb,SAGlC,MAAwB,kBAAbA,GACW,IAAbA,IAGJrN,OAAO0O,KAAKrB,GAAU1hB,SAKvBsC,EAAM0gB,cAAc/T,SAASpO,MAAK0B,IAAQA,EAAI0gB,iBAKvC,EAEb3gB,EAAM4gB,iBAAmB,KACvB,IAAI/Z,EAAW,EAYf,QATgC,IAA9B7G,EAAM8D,WAAWsb,SACbrN,OAAO0O,KAAKzgB,EAAM0gB,cAAcG,UAChC9O,OAAO0O,KAAKzgB,EAAM8D,WAAWsb,WAE5B9hB,SAAQ+C,IACb,MAAMygB,EAAUzgB,EAAG2B,MAAM,KACzB6E,EAAWhI,KAAKU,IAAIsH,EAAUia,EAAQpjB,OAAO,IAGxCmJ,CAAQ,EAEjB7G,EAAM+gB,uBAAyB,IAAM/gB,EAAMghB,oBAC3ChhB,EAAMihB,oBAAsB,MACrBjhB,EAAMkhB,sBAAwBlhB,EAAMU,QAAQugB,sBAC/CjhB,EAAMkhB,qBAAuBlhB,EAAMU,QAAQugB,oBAAoBjhB,IAG7DA,EAAMU,QAAQmf,kBAAoB7f,EAAMkhB,qBACnClhB,EAAM+gB,yBAGR/gB,EAAMkhB,uBACd,EAGHvZ,UAAWA,CACT1H,EACAD,KAEAC,EAAIkhB,eAAiB/B,IACnBpf,EAAMggB,aAAYzjB,IAAO,IAAA6kB,EACvB,MAAMC,GAAiB,IAAR9kB,KAAwBA,MAAAA,IAAAA,EAAM0D,EAAII,KAEjD,IAAIihB,EAAiC,CAAA,EAYrC,IAVY,IAAR/kB,EACFwV,OAAO0O,KAAKzgB,EAAM0gB,cAAcG,UAAUvjB,SAAQikB,IAChDD,EAAYC,IAAS,CAAI,IAG3BD,EAAc/kB,EAGhB6iB,SAAQgC,EAAGhC,GAAQgC,GAAKC,GAEnBA,GAAUjC,EACb,MAAO,IACFkC,EACH,CAACrhB,EAAII,KAAK,GAId,GAAIghB,IAAWjC,EAAU,CACvB,MAAQ,CAACnf,EAAII,IAAK6X,KAAMC,GAASmJ,EACjC,OAAOnJ,CACT,CAEA,OAAO5b,CAAG,GACV,EAEJ0D,EAAI0gB,cAAgB,KAAM,IAAAa,EACxB,MAAMpC,EAAWpf,EAAM8D,WAAWsb,SAElC,SACuCoC,OAD/BA,EACNxhB,MAAAA,EAAMU,QAAQ+gB,sBAAdzhB,EAAAA,EAAMU,QAAQ+gB,iBAAmBxhB,IAAIuhB,GACvB,IAAbpC,IAA6B,MAARA,OAAQ,EAARA,EAAWnf,EAAII,KACtC,EAEHJ,EAAIqgB,aAAe,KAAM,IAAAoB,EAAAxU,EAAA4F,EACvB,OACsC4O,OADtCA,EACE1hB,MAAAA,EAAMU,QAAQihB,qBAAd3hB,EAAAA,EAAMU,QAAQihB,gBAAkB1hB,IAAIyhB,GACLxU,OAA9BA,EAAClN,EAAMU,QAAQkhB,kBAAe1U,MAAe4F,OAADA,EAAC7S,EAAI6H,WAAJgL,EAAapV,OAAM,EAGrEuC,EAAI4hB,wBAA0B,KAC5B,IAAIC,GAAkB,EAClBnZ,EAAa1I,EAEjB,KAAO6hB,GAAmBnZ,EAAWZ,UACnCY,EAAa3I,EAAMwI,OAAOG,EAAWZ,UAAU,GAC/C+Z,EAAkBnZ,EAAWgY,gBAG/B,OAAOmB,CAAe,EAExB7hB,EAAI8hB,yBAA2B,KAC7B,MAAMC,EAAY/hB,EAAIqgB,eAEtB,MAAO,KACA0B,GACL/hB,EAAIkhB,gBAAgB,CACrB,CACF,GC9JQc,EAA8B,CACzChW,gBAAkBC,IACT,IACFA,EACHgW,WAAY,CARhBC,UAJuB,EAKvBC,SAJsB,MAaR,MAALlW,OAAK,EAALA,EAAOgW,cAKhB9V,kBACEpM,IAEO,CACLqiB,mBAAoBlmB,EAAiB,aAAc6D,KAIvD0D,YAAqC1D,IACnC,IAAIuf,GAAa,EACbC,GAAS,EAEbxf,EAAMsiB,oBAAsB,KAAM,IAAAnhB,EAAAue,EAChC,GAAKH,GAOL,GAEkC,OAFlCpe,EAC4Bue,OAD5BA,EACE1f,EAAMU,QAAQif,cAAYD,EAC1B1f,EAAMU,QAAQ6hB,oBAAkBphB,GAC/BnB,EAAMU,QAAQ8hB,iBACf,CACA,GAAIhD,EAAQ,OACZA,GAAS,EACTxf,EAAM8f,QAAO,KACX9f,EAAMyiB,iBACNjD,GAAS,CAAK,GAElB,OAjBExf,EAAM8f,QAAO,KACXP,GAAa,CAAI,GAgBrB,EAEFvf,EAAM0iB,cAAgBzmB,GAOmB,MAAhC+D,EAAMU,QAAQ2hB,wBAAkB,EAAhCriB,EAAMU,QAAQ2hB,oBANyB9lB,GAC7BP,EAAiBC,EAASM,KAO7CyD,EAAM2iB,gBAAkBjU,IAAgB,IAAAkU,EACtC5iB,EAAM0iB,cACJhU,EA5DkD,CACxDyT,UAJuB,EAKvBC,SAJsB,IAgEiB,OADFQ,EAC3B5iB,EAAM6O,aAAaqT,YAAUU,EA9DiB,CACxDT,UAJuB,EAKvBC,SAJsB,IAiEjB,EAEHpiB,EAAM6iB,aAAe5mB,IACnB+D,EAAM0iB,eAAcnmB,IAClB,IAAI4lB,EAAYnmB,EAAiBC,EAASM,EAAI4lB,WAE9C,MAAMW,OAC+B,IAA5B9iB,EAAMU,QAAQqiB,YACQ,IAA7B/iB,EAAMU,QAAQqiB,UACVtX,OAAOiL,iBACP1W,EAAMU,QAAQqiB,UAAY,EAIhC,OAFAZ,EAAYtjB,KAAKU,IAAI,EAAGV,KAAKW,IAAI2iB,EAAWW,IAErC,IACFvmB,EACH4lB,YACD,GACD,EAEJniB,EAAMyiB,eAAiB/T,IAAgB,IAAAsU,EAAApU,EACrC5O,EAAM6iB,aACJnU,EAxFiB,EA0F4BsU,OADzBA,EACEpU,OADFA,EAChB5O,EAAM6O,eAAND,OAAkBA,EAAlBA,EAAoBsT,iBAApBtT,EAAAA,EAAgCuT,WAASa,EA1F5B,EA2FlB,EAEHhjB,EAAMijB,cAAgBvU,IAAgB,IAAAwU,EAAAC,EACpCnjB,EAAMojB,YACJ1U,EA9FgB,GAgG4BwU,OADzBA,EACGC,OADHA,EACfnjB,EAAM6O,eAANsU,OAAkBA,EAAlBA,EAAoBjB,iBAApBiB,EAAAA,EAAgCf,UAAQc,EAhG5B,GAiGjB,EAEHljB,EAAMojB,YAAcnnB,IAClB+D,EAAM0iB,eAAcnmB,IAClB,MAAM6lB,EAAWvjB,KAAKU,IAAI,EAAGvD,EAAiBC,EAASM,EAAI6lB,WACrDiB,EAAc9mB,EAAI6lB,SAAW7lB,EAAI4lB,UACjCA,EAAYtjB,KAAKiR,MAAMuT,EAAcjB,GAE3C,MAAO,IACF7lB,EACH4lB,YACAC,WACD,GACD,EAGJpiB,EAAMsjB,aAAernB,GACnB+D,EAAM0iB,eAAcnmB,IAAO,IAAAgnB,EACzB,IAAIC,EAAexnB,EACjBC,EACuB,OADhBsnB,EACPvjB,EAAMU,QAAQqiB,WAASQ,GAAK,GAO9B,MAJ4B,iBAAjBC,IACTA,EAAe3kB,KAAKU,KAAK,EAAGikB,IAGvB,IACFjnB,EACHwmB,UAAWS,EACZ,IAGLxjB,EAAMyjB,eAAiB9lB,GACrB,IAAM,CAACqC,EAAM0jB,kBACbX,IACE,IAAIY,EAAwB,GAI5B,OAHIZ,GAAaA,EAAY,IAC3BY,EAAc,IAAI,IAAI/mB,MAAMmmB,IAAYa,KAAK,MAAMpgB,KAAI,CAAC0U,EAAG/O,IAAMA,KAE5Dwa,CAAW,GAEpBlkB,EAAeO,EAAMU,QAAS,eAGhCV,EAAM6jB,mBAAqB,IAAM7jB,EAAM8D,WAAWoe,WAAWC,UAAY,EAEzEniB,EAAM8jB,eAAiB,KACrB,MAAM3B,UAAEA,GAAcniB,EAAM8D,WAAWoe,WAEjCa,EAAY/iB,EAAM0jB,eAExB,OAAmB,IAAfX,GAIc,IAAdA,GAIGZ,EAAYY,EAAY,CAAC,EAGlC/iB,EAAM+jB,aAAe,IACZ/jB,EAAM6iB,cAAatmB,GAAOA,EAAM,IAGzCyD,EAAMgkB,SAAW,IACRhkB,EAAM6iB,cAAatmB,GACjBA,EAAM,IAIjByD,EAAMikB,UAAY,IACTjkB,EAAM6iB,aAAa,GAG5B7iB,EAAMkkB,SAAW,IACRlkB,EAAM6iB,aAAa7iB,EAAM0jB,eAAiB,GAGnD1jB,EAAMqgB,yBAA2B,IAAMrgB,EAAMihB,sBAC7CjhB,EAAMmkB,sBAAwB,MAEzBnkB,EAAMokB,wBACPpkB,EAAMU,QAAQyjB,wBAEdnkB,EAAMokB,uBACJpkB,EAAMU,QAAQyjB,sBAAsBnkB,IAGpCA,EAAMU,QAAQ8hB,mBAAqBxiB,EAAMokB,uBACpCpkB,EAAMqgB,2BAGRrgB,EAAMokB,0BAGfpkB,EAAM0jB,aAAe,KAAM,IAAAW,EACzB,OACyB,OADzBA,EACErkB,EAAMU,QAAQqiB,WAASsB,EACvBxlB,KAAKylB,KAAKtkB,EAAMukB,cAAgBvkB,EAAM8D,WAAWoe,WAAWE,SAAS,EAIzEpiB,EAAMukB,YAAc,KAAM,IAAAC,EACxB,OACwBA,OADxBA,EACExkB,EAAMU,QAAQ+jB,UAAQD,EAAIxkB,EAAMqgB,2BAA2BqE,KAAKhnB,MAAM,CAEzE,GCzQQinB,EAA2B,CACtC1Y,gBAAkBC,IACT,CACL0Y,WARoD,CACxDC,IAAK,GACLC,OAAQ,OAOD5Y,IAIPE,kBACEpM,IAEO,CACL+kB,mBAAoB5oB,EAAiB,aAAc6D,KAIvD2H,UAAWA,CACT1H,EACAD,KAEAC,EAAIsU,IAAM,CAACjB,EAAU0R,EAAiBC,KACpC,MAAMC,EAAaF,EACf/kB,EAAIqI,cAAc9E,KAAIrC,IAAA,IAACd,GAAEA,GAAIc,EAAA,OAAKd,CAAE,IACpC,GACE8kB,EAAeF,EACjBhlB,EAAIwI,gBAAgBjF,KAAIyU,IAAA,IAAC5X,GAAEA,GAAI4X,EAAA,OAAK5X,CAAE,IACtC,GACE+kB,EAAS,IAAI/U,IAAI,IAAI8U,EAAcllB,EAAII,MAAO6kB,IAEpDllB,EAAMqlB,eAAc9oB,IAAO,IAAA+oB,EAAAC,EACEC,EAAAC,EAUHC,EAAAC,EAVxB,MAAiB,WAAbrS,EACK,CACLuR,KAAcW,OAATA,EAACjpB,MAAAA,OAAAA,EAAAA,EAAKsoB,KAAGW,EAAI,IAAIjhB,QAAO9H,KAAY,MAAN2oB,GAAAA,EAAQQ,IAAInpB,MAC/CqoB,OAAQ,KACSW,OAAZA,EAAI,MAAHlpB,OAAG,EAAHA,EAAKuoB,QAAMW,EAAI,IAAIlhB,QAAO9H,KAAY,MAAN2oB,GAAAA,EAAQQ,IAAInpB,SAC7CG,MAAMwT,KAAKgV,KAKH,QAAb9R,EACK,CACLuR,IAAK,KACSa,OAATA,EAAI,MAAHnpB,OAAG,EAAHA,EAAKsoB,KAAGa,EAAI,IAAInhB,QAAO9H,KAAM2oB,MAAAA,GAAAA,EAAQQ,IAAInpB,SAC1CG,MAAMwT,KAAKgV,IAEhBN,QAAoBa,OAAZA,EAACppB,MAAAA,OAAAA,EAAAA,EAAKuoB,QAAMa,EAAI,IAAIphB,QAAO9H,KAAM2oB,MAAAA,GAAAA,EAAQQ,IAAInpB,OAIlD,CACLooB,KAAcS,OAATA,EAAC/oB,MAAAA,OAAAA,EAAAA,EAAKsoB,KAAGS,EAAI,IAAI/gB,QAAO9H,KAAY,MAAN2oB,GAAAA,EAAQQ,IAAInpB,MAC/CqoB,QAAoBS,OAAZA,EAAChpB,MAAAA,OAAAA,EAAAA,EAAKuoB,QAAMS,EAAI,IAAIhhB,QAAO9H,KAAM2oB,MAAAA,GAAAA,EAAQQ,IAAInpB,MACtD,GACD,EAEJwD,EAAI+U,UAAY,KAAM,IAAA0E,EACpB,MAAMmM,iBAAEA,EAAgB3Q,cAAEA,GAAkBlV,EAAMU,QAClD,MAAgC,mBAArBmlB,EACFA,EAAiB5lB,GAEc,OAAxCyZ,EAAOmM,MAAAA,EAAAA,EAAoB3Q,IAAawE,CAAQ,EAElDzZ,EAAImV,YAAc,KAChB,MAAMgQ,EAAS,CAACnlB,EAAII,KAEdwkB,IAAEA,EAAGC,OAAEA,GAAW9kB,EAAM8D,WAAW8gB,WAEnCkB,EAAQV,EAAO7mB,MAAK9B,GAAQ,MAAHooB,OAAG,EAAHA,EAAK/iB,SAASrF,KACvCspB,EAAWX,EAAO7mB,MAAK9B,GAAW,MAANqoB,OAAM,EAANA,EAAQhjB,SAASrF,KAEnD,OAAOqpB,EAAQ,QAAQC,GAAW,QAAgB,EAEpD9lB,EAAIuV,eAAiB,KAAM,IAAAwQ,EAAAC,EACzB,MAAM3S,EAAWrT,EAAImV,cACrB,IAAK9B,EAAU,OAAQ,EAEvB,MAAM4S,SAAmBF,EACV,QAAb1S,EAAqBtT,EAAMmmB,aAAenmB,EAAMomB,wBADtBJ,EAEzBxiB,KAAI6iB,IAAA,IAAChmB,GAAEA,GAAIgmB,EAAA,OAAKhmB,CAAE,IAErB,OAA2C,OAA3C4lB,EAAOC,MAAAA,OAAAA,EAAAA,EAAqBvU,QAAQ1R,EAAII,KAAG4lB,GAAK,CAAC,CAClD,EAGHviB,YAAqC1D,IACnCA,EAAMqlB,cAAgBppB,GAA2C,MAAhC+D,EAAMU,QAAQqkB,wBAAkB,EAAhC/kB,EAAMU,QAAQqkB,mBAAqB9oB,GAEpE+D,EAAMsmB,gBAAkB5X,IAAY,IAAA6X,EAAA3X,EAAA,OAClC5O,EAAMqlB,cACJ3W,EA/FkD,CACxDmW,IAAK,GACLC,OAAQ,IA+FgCyB,OADHA,EAC3B3X,OAD2BA,EAC3B5O,EAAM6O,mBAAND,EAAAA,EAAoBgW,YAAU2B,EAjGgB,CACxD1B,IAAK,GACLC,OAAQ,IAgGH,EAEH9kB,EAAMwmB,oBAAsBlT,IAAY,IAAA0C,EACtC,MAAMC,EAAejW,EAAM8D,WAAW8gB,WAEvB,IAAA6B,EAAAC,EAAf,OAAKpT,EAGE9O,QAAQwR,OAADA,EAACC,EAAa3C,SAAb0C,EAAAA,EAAwBtY,QAF9B8G,SAAwB,OAAhBiiB,EAAAxQ,EAAa4O,UAAG,EAAhB4B,EAAkB/oB,UAAUgpB,OAAJA,EAAIzQ,EAAa6O,aAAb4B,EAAAA,EAAqBhpB,QAEpB,EAGhDsC,EAAM2mB,eAAiB,CAACC,EAAaC,EAAcvT,KAAa,IAAAwT,EAc9D,OAZ8BA,OAA5BA,EAAA9mB,EAAMU,QAAQqmB,iBAAcD,SAGvBD,EAAAA,EAAgB,IAAIrjB,KAAI+d,IACvB,MAAMthB,EAAMD,EAAMwI,OAAO+Y,GAAO,GAChC,OAAOthB,EAAI4hB,0BAA4B5hB,EAAM,IAAI,WAGlD4mB,EAAAA,EAAgB,IAAIrjB,KACnB+d,GAASqF,EAAYtiB,MAAKrE,GAAOA,EAAII,KAAOkhB,OAGxChd,OAAOC,SAAShB,KAAI/G,IAAM,IAAKA,EAAG6W,cAAY,EAG5DtT,EAAMmmB,WAAaxoB,GACjB,IAAM,CAACqC,EAAM0gB,cAAcgE,KAAM1kB,EAAM8D,WAAW8gB,WAAWC,OAC7D,CAACmC,EAASC,IACRjnB,EAAM2mB,eAAeK,EAASC,EAAiB,QACjDxnB,EAAeO,EAAMU,QAAS,cAGhCV,EAAMomB,cAAgBzoB,GACpB,IAAM,CAACqC,EAAM0gB,cAAcgE,KAAM1kB,EAAM8D,WAAW8gB,WAAWE,UAC7D,CAACkC,EAASE,IACRlnB,EAAM2mB,eAAeK,EAASE,EAAoB,WACpDznB,EAAeO,EAAMU,QAAS,cAGhCV,EAAMmnB,cAAgBxpB,GACpB,IAAM,CACJqC,EAAM0gB,cAAcgE,KACpB1kB,EAAM8D,WAAW8gB,WAAWC,IAC5B7kB,EAAM8D,WAAW8gB,WAAWE,UAE9B,CAACkC,EAASnC,EAAKC,KACb,MAAMsC,EAAe,IAAI/W,IAAI,IAAQ,MAAHwU,EAAAA,EAAO,MAAe,MAANC,EAAAA,EAAU,KAC5D,OAAOkC,EAAQziB,QAAO9H,IAAM2qB,EAAaxB,IAAInpB,EAAE4D,KAAI,GAErDZ,EAAeO,EAAMU,QAAS,aAC/B,GC5EQ2mB,EAA6B,CACxCpb,gBAAkBC,IACT,CACLob,aAAc,CAAE,KACbpb,IAIPE,kBACEpM,IAEO,CACLunB,qBAAsBprB,EAAiB,eAAgB6D,GACvDwnB,oBAAoB,EACpBC,yBAAyB,EACzBC,uBAAuB,IAO3BhkB,YAAqC1D,IACnCA,EAAM2nB,gBAAkB1rB,GACY,MAAlC+D,EAAMU,QAAQ6mB,0BAAoB,EAAlCvnB,EAAMU,QAAQ6mB,qBAAuBtrB,GACvC+D,EAAM4nB,kBAAoBlZ,IAAY,IAAA6X,EAAA,OACpCvmB,EAAM2nB,gBACJjZ,EAAe,CAAA,SAAE6X,EAAGvmB,EAAM6O,aAAayY,cAAYf,EAAI,CACzD,EAAC,EACHvmB,EAAM6nB,sBAAwBjb,IAC5B5M,EAAM2nB,iBAAgBprB,IACpBqQ,OACmB,IAAVA,EAAwBA,GAAS5M,EAAM8nB,uBAEhD,MAAMR,EAAe,IAAK/qB,GAEpBwrB,EAAqB/nB,EAAMsS,wBAAwB3F,SAiBzD,OAbIC,EACFmb,EAAmBzqB,SAAQ2C,IACpBA,EAAI+nB,iBAGTV,EAAarnB,EAAII,KAAM,EAAI,IAG7B0nB,EAAmBzqB,SAAQ2C,WAClBqnB,EAAarnB,EAAII,GAAG,IAIxBinB,CAAY,GACnB,EAEJtnB,EAAMioB,0BAA4Brb,GAChC5M,EAAM2nB,iBAAgBprB,IACpB,MAAM2rB,OACa,IAAVtb,EACHA,GACC5M,EAAMmoB,2BAEPb,EAAkC,IAAK/qB,GAM7C,OAJAyD,EAAM0gB,cAAcgE,KAAKpnB,SAAQ2C,IAC/BmoB,EAAoBd,EAAcrnB,EAAII,GAAI6nB,GAAe,EAAMloB,EAAM,IAGhEsnB,CAAY,IA6DvBtnB,EAAMqoB,uBAAyB,IAAMroB,EAAM0M,kBAC3C1M,EAAMsoB,oBAAsB3qB,GAC1B,IAAM,CAACqC,EAAM8D,WAAWwjB,aAActnB,EAAM0M,qBAC5C,CAAC4a,EAAciB,IACRxW,OAAO0O,KAAK6G,GAAc5pB,OAQxB8qB,EAAaxoB,EAAOuoB,GAPlB,CACL7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAC,IAMjBphB,EAAeO,EAAMU,QAAS,eAGhCV,EAAMyoB,4BAA8B9qB,GAClC,IAAM,CAACqC,EAAM8D,WAAWwjB,aAActnB,EAAM8O,yBAC5C,CAACwY,EAAciB,IACRxW,OAAO0O,KAAK6G,GAAc5pB,OAQxB8qB,EAAaxoB,EAAOuoB,GAPlB,CACL7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAC,IAMjBphB,EAAeO,EAAMU,QAAS,eAGhCV,EAAM0oB,2BAA6B/qB,GACjC,IAAM,CAACqC,EAAM8D,WAAWwjB,aAActnB,EAAMghB,uBAC5C,CAACsG,EAAciB,IACRxW,OAAO0O,KAAK6G,GAAc5pB,OAQxB8qB,EAAaxoB,EAAOuoB,GAPlB,CACL7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAC,IAMjBphB,EAAeO,EAAMU,QAAS,eAmBhCV,EAAM8nB,qBAAuB,KAC3B,MAAMC,EAAqB/nB,EAAM8O,sBAAsBnC,UACjD2a,aAAEA,GAAiBtnB,EAAM8D,WAE/B,IAAI6kB,EAAoBnkB,QACtBujB,EAAmBrqB,QAAUqU,OAAO0O,KAAK6G,GAAc5pB,QAazD,OAVIirB,GAEAZ,EAAmBxpB,MACjB0B,GAAOA,EAAI+nB,iBAAmBV,EAAarnB,EAAII,QAGjDsoB,GAAoB,GAIjBA,CAAiB,EAG1B3oB,EAAMmoB,yBAA2B,KAC/B,MAAMS,EAAqB5oB,EACxBmkB,wBACAxX,SAASpI,QAAOtE,GAAOA,EAAI+nB,kBACxBV,aAAEA,GAAiBtnB,EAAM8D,WAE/B,IAAI+kB,IAA0BD,EAAmBlrB,OASjD,OANEmrB,GACAD,EAAmBrqB,MAAK0B,IAAQqnB,EAAarnB,EAAII,QAEjDwoB,GAAwB,GAGnBA,CAAqB,EAG9B7oB,EAAM8oB,sBAAwB,KAAM,IAAAC,EAClC,MAAMC,EAAgBjX,OAAO0O,KACEsI,OADEA,EAC/B/oB,EAAM8D,WAAWwjB,cAAYyB,EAAI,CACnC,GAAErrB,OACF,OACEsrB,EAAgB,GAChBA,EAAgBhpB,EAAM8O,sBAAsBnC,SAASjP,MAAM,EAI/DsC,EAAMipB,0BAA4B,KAChC,MAAML,EAAqB5oB,EAAMmkB,wBAAwBxX,SACzD,OAAO3M,EAAMmoB,4BAETS,EACGrkB,QAAOtE,GAAOA,EAAI+nB,iBAClBzpB,MAAK9B,GAAKA,EAAEysB,iBAAmBzsB,EAAE0sB,qBAAoB,EAG9DnpB,EAAMopB,gCAAkC,IAC9BvQ,IACN7Y,EAAM6nB,sBACFhP,EAAiB+D,OAA4BC,QAChD,EAIL7c,EAAMqpB,oCAAsC,IAClCxQ,IACN7Y,EAAMioB,0BACFpP,EAAiB+D,OAA4BC,QAChD,CAEJ,EAGHlV,UAAWA,CACT1H,EACAD,KAEAC,EAAIqpB,eAAiB,CAAC1c,EAAO9O,KAC3B,MAAMyrB,EAAatpB,EAAIipB,gBAEvBlpB,EAAM2nB,iBAAgBprB,IAAO,IAAAitB,EAG3B,GAFA5c,OAAyB,IAAVA,EAAwBA,GAAS2c,EAE5CtpB,EAAI+nB,gBAAkBuB,IAAe3c,EACvC,OAAOrQ,EAGT,MAAMktB,EAAiB,IAAKltB,GAU5B,OARA6rB,EACEqB,EACAxpB,EAAII,GACJuM,EACoB,OADf4c,EACL1rB,MAAAA,OAAAA,EAAAA,EAAM4rB,iBAAcF,EACpBxpB,GAGKypB,CAAc,GACrB,EAEJxpB,EAAIipB,cAAgB,KAClB,MAAM5B,aAAEA,GAAiBtnB,EAAM8D,WAC/B,OAAO6lB,EAAc1pB,EAAKqnB,EAAa,EAGzCrnB,EAAIkpB,kBAAoB,KACtB,MAAM7B,aAAEA,GAAiBtnB,EAAM8D,WAC/B,MAAsD,SAA/C8lB,EAAiB3pB,EAAKqnB,EAA+B,EAG9DrnB,EAAI4pB,wBAA0B,KAC5B,MAAMvC,aAAEA,GAAiBtnB,EAAM8D,WAC/B,MAAsD,QAA/C8lB,EAAiB3pB,EAAKqnB,EAA8B,EAG7DrnB,EAAI+nB,aAAe,KAAM,IAAA9a,EACvB,MAAgD,mBAArClN,EAAMU,QAAQ8mB,mBAChBxnB,EAAMU,QAAQ8mB,mBAAmBvnB,GAGH,OAAvCiN,EAAOlN,EAAMU,QAAQ8mB,qBAAkBta,CAAQ,EAGjDjN,EAAI6pB,oBAAsB,KAAM,IAAA3c,EAC9B,MAAmD,mBAAxCnN,EAAMU,QAAQgnB,sBAChB1nB,EAAMU,QAAQgnB,sBAAsBznB,GAGH,OAA1CkN,EAAOnN,EAAMU,QAAQgnB,wBAAqBva,CAAQ,EAGpDlN,EAAI8pB,kBAAoB,KAAM,IAAAC,EAC5B,MAAqD,mBAA1ChqB,EAAMU,QAAQ+mB,wBAChBznB,EAAMU,QAAQ+mB,wBAAwBxnB,GAGH,OAA5C+pB,EAAOhqB,EAAMU,QAAQ+mB,0BAAuBuC,CAAQ,EAEtD/pB,EAAIgqB,yBAA2B,KAC7B,MAAMC,EAAYjqB,EAAI+nB,eAEtB,OAAQnP,IAAe,IAAAiF,EAChBoM,GACLjqB,EAAIqpB,eACFxL,OADgBA,EACdjF,EAAiB+D,aAAnBkB,EAAAA,EAAgDjB,QACjD,CACF,CACF,GAICuL,EAAsBA,CAC1BqB,EACAppB,EACAuM,EACAud,EACAnqB,KACG,IAAA8S,EACH,MAAM7S,EAAMD,EAAMwI,OAAOnI,GAAI,GAQzBuM,GACG3M,EAAI8pB,qBACPhY,OAAO0O,KAAKgJ,GAAgBnsB,SAAQlB,UAAcqtB,EAAertB,KAE/D6D,EAAI+nB,iBACNyB,EAAeppB,IAAM,WAGhBopB,EAAeppB,GAIpB8pB,GAA8B,OAAfrX,EAAI7S,EAAI6H,UAAJgL,EAAapV,QAAUuC,EAAI6pB,uBAChD7pB,EAAI6H,QAAQxK,SAAQ2C,GAClBmoB,EAAoBqB,EAAgBxpB,EAAII,GAAIuM,EAAOud,EAAiBnqB,IAExE,EAGK,SAASwoB,EACdxoB,EACAuoB,GAEA,MAAMjB,EAAetnB,EAAM8D,WAAWwjB,aAEhC8C,EAAoC,GACpCC,EAAkD,CAAA,EAGlDC,EAAc,SAAC5F,EAAoBzjB,GACvC,OAAOyjB,EACJlhB,KAAIvD,IAAO,IAAAsqB,EACV,MAAMhB,EAAaI,EAAc1pB,EAAKqnB,GActC,GAZIiC,IACFa,EAAoB5sB,KAAKyC,GACzBoqB,EAAoBpqB,EAAII,IAAMJ,GAG5BsqB,OAAJA,EAAItqB,EAAI6H,UAAJyiB,EAAa7sB,SACfuC,EAAM,IACDA,EACH6H,QAASwiB,EAAYrqB,EAAI6H,WAIzByhB,EACF,OAAOtpB,CACT,IAEDsE,OAAOC,UAGZ,MAAO,CACLkgB,KAAM4F,EAAY/B,EAAS7D,MAC3B/X,SAAUyd,EACVvJ,SAAUwJ,EAEd,CAEO,SAASV,EACd1pB,EACAuqB,GACS,IAAAC,EACT,OAAwB,OAAxBA,EAAOD,EAAUvqB,EAAII,MAAGoqB,CAC1B,CAEO,SAASb,EACd3pB,EACAuqB,EACAxqB,GAC0B,IAAA0qB,EAC1B,GAAKA,OAADA,EAACzqB,EAAI6H,WAAJ4iB,EAAahtB,OAAQ,OAAO,EAEjC,IAAIitB,GAAsB,EACtBC,GAAe,EA8BnB,OA5BA3qB,EAAI6H,QAAQxK,SAAQutB,IAElB,KAAID,GAAiBD,KAIjBE,EAAO7C,iBACL2B,EAAckB,EAAQL,GACxBI,GAAe,EAEfD,GAAsB,GAKtBE,EAAO/iB,SAAW+iB,EAAO/iB,QAAQpK,QAAQ,CAC3C,MAAMotB,EAAyBlB,EAAiBiB,EAAQL,GACzB,QAA3BM,EACFF,GAAe,EACqB,SAA3BE,GACTF,GAAe,EACfD,GAAsB,GAEtBA,GAAsB,CAE1B,KAGKA,EAAsB,QAAQC,GAAe,MACtD,CCzpBaG,MAAAA,EAAsB,aAkDnC,SAASC,EAAa/a,EAAQC,GAC5B,OAAOD,IAAMC,EAAI,EAAID,EAAIC,EAAI,GAAK,CACpC,CAEA,SAASjG,EAASgG,GAChB,MAAiB,iBAANA,EACLvE,MAAMuE,IAAMA,IAAMtE,KAAYsE,KAAOtE,IAChC,GAEFvM,OAAO6Q,GAEC,iBAANA,EACFA,EAEF,EACT,CAKA,SAASgb,GAAoBC,EAAcC,GAGzC,MAAMlb,EAAIib,EAAKlpB,MAAM+oB,GAAqBxmB,OAAOC,SAC3C0L,EAAIib,EAAKnpB,MAAM+oB,GAAqBxmB,OAAOC,SAGjD,KAAOyL,EAAEvS,QAAUwS,EAAExS,QAAQ,CAC3B,MAAM0tB,EAAKnb,EAAEiE,QACPmX,EAAKnb,EAAEgE,QAEPoX,EAAKC,SAASH,EAAI,IAClBI,EAAKD,SAASF,EAAI,IAElBI,EAAQ,CAACH,EAAIE,GAAIxb,OAGvB,GAAItE,MAAM+f,EAAM,IAAhB,CACE,GAAIL,EAAKC,EACP,OAAO,EAET,GAAIA,EAAKD,EACP,OAAQ,CAGZ,KARA,CAWA,GAAI1f,MAAM+f,EAAM,IACd,OAAO/f,MAAM4f,IAAO,EAAI,EAI1B,GAAIA,EAAKE,EACP,OAAO,EAET,GAAIA,EAAKF,EACP,OAAQ,CAZV,CAcF,CAEA,OAAOrb,EAAEvS,OAASwS,EAAExS,MACtB,CAIO,MAAMguB,GAAa,CACxBC,aAnHmCA,CAACC,EAAMC,EAAM1rB,IACzC8qB,GACLhhB,EAAS2hB,EAAKtrB,SAASH,IAAW+J,cAClCD,EAAS4hB,EAAKvrB,SAASH,IAAW+J,eAiHpC4hB,0BA7GgDA,CAACF,EAAMC,EAAM1rB,IACtD8qB,GACLhhB,EAAS2hB,EAAKtrB,SAASH,IACvB8J,EAAS4hB,EAAKvrB,SAASH,KA2GzB4rB,KArG2BA,CAACH,EAAMC,EAAM1rB,IACjC6qB,EACL/gB,EAAS2hB,EAAKtrB,SAASH,IAAW+J,cAClCD,EAAS4hB,EAAKvrB,SAASH,IAAW+J,eAmGpC8hB,kBA7FwCA,CAACJ,EAAMC,EAAM1rB,IAC9C6qB,EACL/gB,EAAS2hB,EAAKtrB,SAASH,IACvB8J,EAAS4hB,EAAKvrB,SAASH,KA2FzB8rB,SAvF+BA,CAACL,EAAMC,EAAM1rB,KAC5C,MAAM8P,EAAI2b,EAAKtrB,SAAeH,GACxB+P,EAAI2b,EAAKvrB,SAAeH,GAK9B,OAAO8P,EAAIC,EAAI,EAAID,EAAIC,GAAK,EAAI,CAAC,EAiFjCgc,MA9E4BA,CAACN,EAAMC,EAAM1rB,IAClC6qB,EAAaY,EAAKtrB,SAASH,GAAW0rB,EAAKvrB,SAASH,KCsOhDgsB,GAA2B,CACtClgB,gBAAkBC,IACT,CACLkgB,QAAS,MACNlgB,IAIPH,oBAAqBA,KACZ,CACLsgB,UAAW,OACXC,cAAe,IAInBlgB,kBACEpM,IAEO,CACLusB,gBAAiBpwB,EAAiB,UAAW6D,GAC7CwsB,iBAAmB3T,GACTA,EAAiB4T,WAK/B1rB,aAAcA,CACZb,EACAF,KAEAE,EAAOwsB,iBAAmB,KACxB,MAAMC,EAAY3sB,EAAM8O,sBAAsBnC,SAASkL,MAAM,IAE7D,IAAI+U,GAAW,EAEf,IAAK,MAAM3sB,KAAO0sB,EAAW,CAC3B,MAAM/f,EAAQ3M,MAAAA,OAAAA,EAAAA,EAAKK,SAASJ,EAAOG,IAEnC,GAA8C,kBAA1C0R,OAAOtQ,UAAUwI,SAAS+H,KAAKpF,GACjC,OAAO8e,GAAWO,SAGpB,GAAqB,iBAAVrf,IACTggB,GAAW,EAEPhgB,EAAM5K,MAAM+oB,GAAqBrtB,OAAS,GAC5C,OAAOguB,GAAWC,YAGxB,CAEA,OAAIiB,EACKlB,GAAWK,KAGbL,GAAWQ,KAAK,EAEzBhsB,EAAO2sB,eAAiB,KACtB,MAAMpgB,EAAWzM,EAAM8O,sBAAsBnC,SAAS,GAItD,MAAqB,iBAFPF,MAAAA,OAAAA,EAAAA,EAAUnM,SAASJ,EAAOG,KAG/B,MAGF,MAAM,EAEfH,EAAO4sB,aAAe,KAAM,IAAAC,EAAAC,EAC1B,IAAK9sB,EACH,MAAM,IAAIgC,MAGZ,OAAO1F,EAAW0D,EAAOc,UAAUqrB,WAC/BnsB,EAAOc,UAAUqrB,UACc,SAA/BnsB,EAAOc,UAAUqrB,UACfnsB,EAAOwsB,mBACyDK,OADvCA,EACD,OADCC,EACzBhtB,EAAMU,QAAQgrB,iBAAU,EAAxBsB,EAA2B9sB,EAAOc,UAAUqrB,YAAoBU,EAChErB,GAAWxrB,EAAOc,UAAUqrB,UAA8B,EAElEnsB,EAAO+sB,cAAgB,CAACC,EAAMC,KAW5B,MAAMC,EAAmBltB,EAAOmtB,sBAC1BC,EAAiB,MAAOJ,EAE9BltB,EAAMutB,YAAWhxB,IAEf,MAAMixB,EAAkBjxB,MAAAA,OAAAA,EAAAA,EAAK+H,MAAK7H,GAAKA,EAAE4D,KAAOH,EAAOG,KACjDotB,EAAgBlxB,MAAAA,OAAAA,EAAAA,EAAKsR,WAAUpR,GAAKA,EAAE4D,KAAOH,EAAOG,KAE1D,IAGIqtB,EAHAC,EAA2B,GAI3BC,EAAWN,EAAiBJ,EAA4B,SAArBE,EA+Bb,IAAAS,GA1BtBH,EAFG,MAAHnxB,GAAAA,EAAKmB,QAAUwC,EAAO4tB,mBAAqBX,EACzCK,EACW,SAEA,MAIR,MAAHjxB,GAAAA,EAAKmB,QAAU+vB,IAAkBlxB,EAAImB,OAAS,EACnC,UACJ8vB,EACI,SAEA,UAKE,WAAfE,IAEGJ,GAEEF,IACHM,EAAa,WAKA,QAAfA,IACFC,EAAa,IACRpxB,EACH,CACE8D,GAAIH,EAAOG,GACX6sB,KAAMU,IAIVD,EAAWvZ,OACT,EACAuZ,EAAWjwB,QAC0BmwB,OADpBA,EACd7tB,EAAMU,QAAQqtB,sBAAoBF,EAAIpiB,OAAOiL,oBAIlDiX,EAFwB,WAAfD,EAEInxB,EAAIiH,KAAI/G,GACfA,EAAE4D,KAAOH,EAAOG,GACX,IACF5D,EACHywB,KAAMU,GAGHnxB,IAEe,WAAfixB,EACInxB,EAAIgI,QAAO9H,GAAKA,EAAE4D,KAAOH,EAAOG,KAEhC,CACX,CACEA,GAAIH,EAAOG,GACX6sB,KAAMU,IAKZ,OAAOD,CAAU,GACjB,EAGJztB,EAAO8tB,gBAAkB,KAAM,IAAA7sB,EAAA8sB,EAK7B,OAF6B,OAFV9sB,EACa,OADb8sB,EACjB/tB,EAAOc,UAAUktB,eAAaD,EAC9BjuB,EAAMU,QAAQwtB,eAAa/sB,EACC,SAA5BjB,EAAO2sB,kBACc,OAAS,KAAK,EAGvC3sB,EAAOmtB,oBAAuBF,IAAoB,IAAAjgB,EAAAC,EAChD,MAAMghB,EAAqBjuB,EAAO8tB,kBAC5BI,EAAWluB,EAAOmuB,cAExB,OAAKD,KAKHA,IAAaD,GACsB,OADJjhB,EAC9BlN,EAAMU,QAAQ4tB,wBAAoBphB,GAClCigB,GAAuC,OAAlChgB,EAAGnN,EAAMU,QAAQ6tB,qBAAiBphB,KAItB,SAAbihB,EAAsB,MAAQ,QAV5BD,CAUkC,EAG7CjuB,EAAOsuB,WAAa,KAAM,IAAAvhB,EAAA+c,EACxB,OACiC/c,OAA/BA,EAAC/M,EAAOc,UAAUytB,gBAAaxhB,KACH,OADW+c,EACtChqB,EAAMU,QAAQ+tB,gBAAazE,MAC1B9pB,EAAOsB,UAAU,EAIvBtB,EAAO4tB,gBAAkB,KAAM,IAAA7V,EAAAyW,EAC7B,OAE+BzW,OAF/BA,EACkC,OADlCyW,EACExuB,EAAOc,UAAU2tB,iBAAeD,EAChC1uB,EAAMU,QAAQiuB,iBAAe1W,IAC3B/X,EAAOsB,UAAU,EAIvBtB,EAAOmuB,YAAc,KAAM,IAAAO,EACzB,MAAMC,EAAqC,OAA3BD,EAAG5uB,EAAM8D,WAAWsoB,cAAO,EAAxBwC,EAA0BtqB,MAAK7H,GAAKA,EAAE4D,KAAOH,EAAOG,KAEvE,QAAQwuB,IAAqBA,EAAW3B,KAAO,OAAS,MAAK,EAG/DhtB,EAAO4uB,aAAe,KAAA,IAAAC,EAAAC,EAAA,OACwC,OADxCD,EACI,OADJC,EACpBhvB,EAAM8D,WAAWsoB,cAAO,EAAxB4C,EAA0BnhB,WAAUpR,GAAKA,EAAE4D,KAAOH,EAAOG,MAAG0uB,GAAK,CAAC,EAEpE7uB,EAAO+uB,aAAe,KAEpBjvB,EAAMutB,YAAWhxB,GACZ,MAAHA,GAAAA,EAAKmB,OAASnB,EAAIgI,QAAO9H,GAAKA,EAAE4D,KAAOH,EAAOG,KAAM,IACrD,EAGHH,EAAOgvB,wBAA0B,KAC/B,MAAMC,EAAUjvB,EAAOsuB,aAEvB,OAAQ3V,IACDsW,IACc,MAAjBtW,EAAUC,SAAVD,EAAUC,UACZ5Y,MAAAA,EAAO+sB,eAAP/sB,EAAO+sB,mBACLrrB,IACA1B,EAAO4tB,oBAAkD,MAA9B9tB,EAAMU,QAAQ8rB,sBAAgB,EAA9BxsB,EAAMU,QAAQ8rB,iBAAmB3T,KAC7D,CACF,CACF,EAGHnV,YAAqC1D,IACnCA,EAAMutB,WAAatxB,GAAwC,MAA7B+D,EAAMU,QAAQ6rB,qBAAe,EAA7BvsB,EAAMU,QAAQ6rB,gBAAkBtwB,GAC9D+D,EAAMovB,aAAe1gB,IAAgB,IAAA2gB,EAAAzgB,EACnC5O,EAAMutB,WAAW7e,EAAe,GAAgC2gB,OAA9BA,SAAAzgB,EAAG5O,EAAM6O,qBAAND,EAAoBwd,SAAOiD,EAAI,GAAG,EAEzErvB,EAAMsvB,qBAAuB,IAAMtvB,EAAMuS,qBACzCvS,EAAMghB,kBAAoB,MACnBhhB,EAAMuvB,oBAAsBvvB,EAAMU,QAAQsgB,oBAC7ChhB,EAAMuvB,mBAAqBvvB,EAAMU,QAAQsgB,kBAAkBhhB,IAGzDA,EAAMU,QAAQ8uB,gBAAkBxvB,EAAMuvB,mBACjCvvB,EAAMsvB,uBAGRtvB,EAAMuvB,qBACd,GCnfCE,GAAkB,CACtBhsB,EACAyY,EACAhJ,EACAmB,EACAjL,EACA0C,EACAiS,EACAO,EACA6N,GACA1b,EACA0O,EACA8C,EACA0C,EACA0C,EACA1Q,GCvBK,SAAS+Y,GAAkCnH,GAChD,MAAMoH,EAA6B,GAE7BC,EAAa3vB,IAAoB,IAAA6S,EACrC6c,EAAanyB,KAAKyC,GAEd6S,OAAAA,EAAA7S,EAAI6H,UAAJgL,EAAapV,QAAUuC,EAAI0gB,iBAC7B1gB,EAAI6H,QAAQxK,QAAQsyB,EACtB,EAKF,OAFArH,EAAS7D,KAAKpnB,QAAQsyB,GAEf,CACLlL,KAAMiL,EACNhjB,SAAU4b,EAAS5b,SACnBkU,SAAU0H,EAAS1H,SAEvB,CC/CO,SAASgP,GACdnL,EACAoL,EACA9vB,GAEA,OAAIA,EAAMU,QAAQ4L,mBAOpB,SACEyjB,EACAC,EACAhwB,GACiB,IAAAiwB,EACjB,MAAMC,EAAoC,GACpCC,EAAkD,CAAA,EAClDtpB,EAA8C,OAAtCopB,EAAGjwB,EAAMU,QAAQ6L,uBAAqB0jB,EAAI,IAElDG,EAAoB,SAACL,EAA4B9uB,QAAK,IAALA,IAAAA,EAAQ,GAC7D,MAAMyjB,EAAqB,GAG3B,IAAK,IAAIvb,EAAI,EAAGA,EAAI4mB,EAAaryB,OAAQyL,IAAK,CAAA,IAAA2J,EAC5C,IAAI7S,EAAM8vB,EAAa5mB,GAEvB,MAAMknB,EAAS1oB,EACb3H,EACAC,EAAII,GACJJ,EAAI2H,SACJ3H,EAAIxB,MACJwB,EAAIgB,WACJW,EACA3B,EAAI8H,UAIN,GAFAsoB,EAAOlkB,cAAgBlM,EAAIkM,cAEZ,OAAX2G,EAAA7S,EAAI6H,UAAJgL,EAAapV,QAAUuD,EAAQ4F,EAAU,CAI3C,GAHAwpB,EAAOvoB,QAAUsoB,EAAkBnwB,EAAI6H,QAAS7G,EAAQ,GACxDhB,EAAMowB,EAEFL,EAAU/vB,KAASowB,EAAOvoB,QAAQpK,OAAQ,CAC5CgnB,EAAKlnB,KAAKyC,GACVkwB,EAAoBlwB,EAAII,IAAMJ,EAC9BiwB,EAAoB1yB,KAAKyC,GACzB,QACF,CAEA,GAAI+vB,EAAU/vB,IAAQowB,EAAOvoB,QAAQpK,OAAQ,CAC3CgnB,EAAKlnB,KAAKyC,GACVkwB,EAAoBlwB,EAAII,IAAMJ,EAC9BiwB,EAAoB1yB,KAAKyC,GACzB,QACF,CACF,MACEA,EAAMowB,EACFL,EAAU/vB,KACZykB,EAAKlnB,KAAKyC,GACVkwB,EAAoBlwB,EAAII,IAAMJ,EAC9BiwB,EAAoB1yB,KAAKyC,GAG/B,CAEA,OAAOykB,GAGT,MAAO,CACLA,KAAM0L,EAAkBL,GACxBpjB,SAAUujB,EACVrP,SAAUsP,EAEd,CApEWG,CAAwB5L,EAAMoL,EAAe9vB,GAsExD,SACE+vB,EACAC,EACAhwB,GACiB,IAAAuwB,EACjB,MAAML,EAAoC,GACpCC,EAAkD,CAAA,EAClDtpB,EAA8C,OAAtC0pB,EAAGvwB,EAAMU,QAAQ6L,uBAAqBgkB,EAAI,IAGlDH,EAAoB,SAACL,EAA4B9uB,QAAK,IAALA,IAAAA,EAAQ,GAG7D,MAAMyjB,EAAqB,GAG3B,IAAK,IAAIvb,EAAI,EAAGA,EAAI4mB,EAAaryB,OAAQyL,IAAK,CAC5C,IAAIlJ,EAAM8vB,EAAa5mB,GAIvB,GAFa6mB,EAAU/vB,GAEb,CAAA,IAAAsqB,EACR,GAAe,OAAXA,EAAAtqB,EAAI6H,UAAJyiB,EAAa7sB,QAAUuD,EAAQ4F,EAAU,CAC3C,MAAMwpB,EAAS1oB,EACb3H,EACAC,EAAII,GACJJ,EAAI2H,SACJ3H,EAAIxB,MACJwB,EAAIgB,WACJW,EACA3B,EAAI8H,UAENsoB,EAAOvoB,QAAUsoB,EAAkBnwB,EAAI6H,QAAS7G,EAAQ,GACxDhB,EAAMowB,CACR,CAEA3L,EAAKlnB,KAAKyC,GACViwB,EAAoB1yB,KAAKyC,GACzBkwB,EAAoBlwB,EAAII,IAAMJ,CAChC,CACF,CAEA,OAAOykB,GAGT,MAAO,CACLA,KAAM0L,EAAkBL,GACxBpjB,SAAUujB,EACVrP,SAAUsP,EAEd,CArHSK,CAAuB9L,EAAMoL,EAAe9vB,EACrD,qYC2DO,WAGL,MAAO,CACLywB,SAAUA,CAACA,EAAUvwB,IACQ,mBAAbuwB,EACT,IACIvwB,EACHsB,WAAYivB,GAEd,IACKvwB,EACHqB,YAAakvB,GAGrBC,QAASxwB,GAAUA,EACnBywB,MAAOzwB,GAAUA,EAErB,8BHgMO,SACLQ,GACc,IAAAkwB,EAAAC,EAQd,MAAMhwB,EAAY,IAAI4uB,MAAsC,OAArBmB,EAAIlwB,EAAQG,WAAS+vB,EAAI,IAEhE,IAAI5wB,EAAQ,CAAEa,aAEd,MAAMiwB,EAAiB9wB,EAAMa,UAAUoI,QAAO,CAAC0U,EAAK7c,IAC3CiR,OAAOgf,OAAOpT,EAA8B,MAAzB7c,EAAQsL,uBAAiB,EAAzBtL,EAAQsL,kBAAoBpM,KACrD,CAAE,GAeL,IAAI6O,EAAe,IAEOgiB,OAAxBA,EAAInwB,EAAQmO,cAAYgiB,EAAI,CAAE,GAGhC7wB,EAAMa,UAAUvD,SAAQwD,IAAW,IAAAkwB,EACjCniB,EAAuDmiB,OAA3CA,EAAIlwB,MAAAA,EAAQmL,qBAARnL,EAAAA,EAAQmL,gBAAkB4C,IAAamiB,EACrDniB,CAA2B,IAG/B,MAAM2Q,EAAyB,GAC/B,IAAIyR,GAAgB,EAEpB,MAAMC,EAAoC,CACxCrwB,YACAH,QAAS,IACJowB,KACApwB,GAELmO,eACAiR,OAAQqR,IACN3R,EAAOhiB,KAAK2zB,GAEPF,IACHA,GAAgB,EAIhBG,QAAQC,UACLC,MAAK,KACJ,KAAO9R,EAAO9hB,QACZ8hB,EAAOtL,OAAPsL,GAEFyR,GAAgB,CAAK,IAEtBM,OAAMC,GACLC,YAAW,KACT,MAAMD,CAAK,MAGnB,EAEFE,MAAOA,KACL1xB,EAAM1D,SAAS0D,EAAM6O,aAAa,EAEpC8iB,WAAY11B,IACV,MAAM21B,EAAa51B,EAAiBC,EAAS+D,EAAMU,SACnDV,EAAMU,QA5DYA,IAChBV,EAAMU,QAAQmxB,aACT7xB,EAAMU,QAAQmxB,aAAaf,EAAgBpwB,GAG7C,IACFowB,KACApwB,GAqDamxB,CAAaD,EAG5B,EAGH9tB,SAAUA,IACD9D,EAAMU,QAAQwL,MAGvB5P,SAAWL,IACT+D,MAAAA,EAAMU,QAAQoxB,eAAd9xB,EAAMU,QAAQoxB,cAAgB71B,EAAQ,EAGxC81B,UAAWA,CAAC9xB,EAAYxB,EAAeyC,KAAmB,IAAAwgB,EAAA,OACZA,OADYA,EACxD1hB,MAAAA,EAAMU,QAAQsxB,cAAdhyB,EAAAA,EAAMU,QAAQsxB,SAAW/xB,EAAKxB,EAAOyC,IAAOwgB,EAC5C,GAAGxgB,EAAS,CAACA,EAAOb,GAAI5B,GAAOyI,KAAK,KAAOzI,GAAO,EAEpDiO,gBAAiBA,KACV1M,EAAMiyB,mBACTjyB,EAAMiyB,iBAAmBjyB,EAAMU,QAAQgM,gBAAgB1M,IAGlDA,EAAMiyB,oBAMfvR,YAAaA,IACJ1gB,EAAMmkB,wBAGf3b,OAAQA,CAACnI,EAAY6xB,KACnB,IAAIjyB,GACFiyB,EAAYlyB,EAAMqgB,2BAA6BrgB,EAAM0gB,eACrDG,SAASxgB,GAEX,IAAKJ,IACHA,EAAMD,EAAM0M,kBAAkBmU,SAASxgB,IAClCJ,GAIH,MAAM,IAAIiC,MAId,OAAOjC,CAAG,EAEZqB,qBAAsB3D,GACpB,IAAM,CAACqC,EAAMU,QAAQyxB,iBACrBA,IAAiB,IAAAC,EAKf,OAJAD,SAAaC,EAAID,GAAaC,EAAI,CAAA,EAI3B,CACLvwB,OAAQ8O,IACN,MAAMtP,EAAoBsP,EAAM9O,OAAO3B,OACpCc,UAEH,OAAIK,EAAkBE,YACbF,EAAkBE,YAGvBF,EAAkBG,WACbH,EAAkBhB,GAGpB,IAAI,EAGbD,KAAMuQ,IAAK,IAAA0hB,EAAAC,EAAA,OAA0C,OAA1CD,EAAIC,OAAJA,EAAI3hB,EAAMpQ,gBAA4B,MAAlC+xB,EAA0BroB,cAAQ,EAAlCqoB,EAA0BroB,YAAYooB,EAAI,IAAI,KAC1DryB,EAAMa,UAAUoI,QAAO,CAAC0U,EAAK7c,IACvBiR,OAAOgf,OAAOpT,QAAK7c,EAAQiL,2BAARjL,EAAQiL,wBACjC,OACAomB,EACJ,GAEH1yB,EAAeiB,EAAS,iBAG1B6xB,eAAgBA,IAAMvyB,EAAMU,QAAQyB,QAEpCyB,cAAejG,GACb,IAAM,CAACqC,EAAMuyB,oBACbC,IACE,MAAMC,EAAiB,SACrBD,EACAtxB,EACAD,GAEA,YAFK,IAALA,IAAAA,EAAQ,GAEDuxB,EAAWhvB,KAAIxC,IACpB,MAAMd,EAASa,EAAaf,EAAOgB,EAAWC,EAAOC,GAE/CwxB,EAAoB1xB,EAS1B,OAJAd,EAAOiC,QAAUuwB,EAAkBvwB,QAC/BswB,EAAeC,EAAkBvwB,QAASjC,EAAQe,EAAQ,GAC1D,GAEGf,CAAM,KAIjB,OAAOuyB,EAAeD,EAAW,GAEnC/yB,EAAeiB,EAAS,iBAG1Byc,kBAAmBxf,GACjB,IAAM,CAACqC,EAAM4D,mBACbM,GACSA,EAAW5B,SAAQpC,GACjBA,EAAOkC,oBAGlB3C,EAAeiB,EAAS,iBAG1BiyB,uBAAwBh1B,GACtB,IAAM,CAACqC,EAAMmd,uBACbyV,GACSA,EAAY3pB,QACjB,CAACC,EAAKhJ,KACJgJ,EAAIhJ,EAAOG,IAAMH,EACVgJ,IAET,CACF,IAEFzJ,EAAeiB,EAAS,iBAG1BoI,kBAAmBnL,GACjB,IAAM,CAACqC,EAAM4D,gBAAiB5D,EAAMwC,wBACpC,CAAC0B,EAAYzB,IAEJA,EADWyB,EAAW5B,SAAQpC,GAAUA,EAAOqC,qBAGxD9C,EAAeiB,EAAS,iBAG1ByH,UAAWhI,GACMH,EAAM2yB,yBAAyBxyB,IAUlD4R,OAAOgf,OAAO/wB,EAAOkxB,GAErB,IAAK,IAAIzyB,EAAQ,EAAGA,EAAQuB,EAAMa,UAAUnD,OAAQe,IAAS,CAC3D,MAAMqC,EAAUd,EAAMa,UAAUpC,GACzB,MAAPqC,GAAoB,MAApBA,EAAS4C,aAAT5C,EAAS4C,YAAc1D,EACzB,CAEA,OAAOA,CACT,6GI1gBO,WAGL,OAAOA,GACLrC,GACE,IAAM,CAACqC,EAAMU,QAAQmyB,QAEnBA,IAMA,MAAMtK,EAA4B,CAChC7D,KAAM,GACN/X,SAAU,GACVkU,SAAU,CAAC,GAGPiS,EAAa,SACjBC,EACA9xB,EACA2H,QADK,IAAL3H,IAAAA,EAAQ,GAGR,MAAMyjB,EAAO,GAEb,IAAK,IAAIvb,EAAI,EAAGA,EAAI4pB,EAAar1B,OAAQyL,IAAK,CAS5C,MAAMlJ,EAAM0H,EACV3H,EACAA,EAAM+xB,UAAUgB,EAAa5pB,GAAKA,EAAGP,GACrCmqB,EAAa5pB,GACbA,EACAlI,OACAW,EACS,MAATgH,OAAS,EAATA,EAAWvI,IAWiB,IAAA2yB,EAA9B,GAPAzK,EAAS5b,SAASnP,KAAKyC,GAEvBsoB,EAAS1H,SAAS5gB,EAAII,IAAMJ,EAE5BykB,EAAKlnB,KAAKyC,GAGND,EAAMU,QAAQuyB,WAChBhzB,EAAIizB,gBAAkBlzB,EAAMU,QAAQuyB,WAClCF,EAAa5pB,GACbA,GAIE6pB,OAAJA,EAAI/yB,EAAIizB,kBAAJF,EAAqBt1B,SACvBuC,EAAI6H,QAAUgrB,EAAW7yB,EAAIizB,gBAAiBjyB,EAAQ,EAAGhB,GAG/D,CAEA,OAAOykB,GAKT,OAFA6D,EAAS7D,KAAOoO,EAAWD,GAEpBtK,CAAQ,GAEjB9oB,EAAeO,EAAMU,QAAS,aAAc,GAAe,IACzDV,EAAMsiB,wBAGd,wBH9EO,WAGL,OAAOtiB,GACLrC,GACE,IAAM,CACJqC,EAAM8D,WAAWsb,SACjBpf,EAAM+gB,yBACN/gB,EAAMU,QAAQ4e,wBAEhB,CAACF,EAAUmJ,EAAUjJ,KAEhBiJ,EAAS7D,KAAKhnB,SACD,IAAb0hB,IAAsBrN,OAAO0O,KAAKrB,MAAAA,EAAAA,EAAY,CAAE,GAAE1hB,OAE5C6qB,EAGJjJ,EAKEoQ,GAAWnH,GAHTA,GAKX9oB,EAAeO,EAAMU,QAAS,cAEpC,2BI3BO,WAIL,MAAO,CAACV,EAAOG,IACbxC,GACE,KAAA,IAAAw1B,EAAA,MAAM,QAAAA,EAACnzB,EAAMmI,UAAUhI,WAAhBgzB,EAA2B7pB,qBAAqB,IACvD8pB,IACE,IAAKA,EAAiB,OAEtB,MAAMC,EAAeD,EAAgBzmB,SAClCrK,SAAQgxB,IAAO,IAAAC,EAAA,OAAqC,OAArCA,EAAID,EAAQlrB,gBAAgBjI,IAASozB,EAAI,EAAE,IAC1D/vB,IAAIiI,QACJlH,QAAOqI,IAAUnB,OAAOC,MAAMkB,KAEjC,IAAKymB,EAAa31B,OAAQ,OAE1B,IAAI81B,EAAkBH,EAAa,GAC/BI,EAAkBJ,EAAaA,EAAa31B,OAAS,GAEzD,IAAK,MAAMkP,KAASymB,EACdzmB,EAAQ4mB,EAAiBA,EAAkB5mB,EACtCA,EAAQ6mB,IAAiBA,EAAkB7mB,GAGtD,MAAO,CAAC4mB,EAAiBC,EAAgB,GAE3Ch0B,EAAeO,EAAMU,QAAS,cAEpC,uBC5BO,WAIL,MAAO,CAACV,EAAOG,IACbxC,GACE,IAAM,CACJqC,EAAMuJ,yBACNvJ,EAAM8D,WAAWqI,cACjBnM,EAAM8D,WAAWya,aACjBve,EAAM8O,yBAER,CAAC4kB,EAAavnB,EAAeoS,KAC3B,IACGmV,EAAYhP,KAAKhnB,SACH,MAAbyO,IAAAA,EAAezO,UAAW6gB,EAE5B,OAAOmV,EAGT,MAAMC,EAAgB,IACjBxnB,EAAc3I,KAAI/G,GAAKA,EAAE4D,KAAIkE,QAAO9H,GAAKA,IAAM0D,IAClDoe,EAAe,kBAAe3c,GAC9B2C,OAAOC,SAYT,OAAOqrB,GAAW6D,EAAYhP,MAVNzkB,IAEtB,IAAK,IAAIkJ,EAAI,EAAGA,EAAIwqB,EAAcj2B,OAAQyL,IACxC,IAA6C,IAAzClJ,EAAIkM,cAAcwnB,EAAcxqB,IAClC,OAAO,EAGX,OAAO,CAAI,GAGuCnJ,EAAM,GAE5DP,EAAeO,EAAMU,QAAS,cAEpC,2BCxCO,WAIL,MAAO,CAACV,EAAOG,IACbxC,GACE,KAAA,IAAAw1B,EAAA,MAAM,QAAAA,EAACnzB,EAAMmI,UAAUhI,WAAhBgzB,EAA2B7pB,qBAAqB,IACvD8pB,IACE,IAAKA,EAAiB,OAAO,IAAI1pB,IAEjC,IAAIkqB,EAAsB,IAAIlqB,IAE9B,IAAK,IAAIP,EAAI,EAAGA,EAAIiqB,EAAgBzmB,SAASjP,OAAQyL,IAAK,CACxD,MAAMyG,EACJwjB,EAAgBzmB,SAASxD,GAAIf,gBAAwBjI,GAEvD,IAAK,IAAI0zB,EAAI,EAAGA,EAAIjkB,EAAOlS,OAAQm2B,IAAK,CACtC,MAAMjnB,EAAQgD,EAAOikB,GAEe,IAAAC,EAApC,GAAIF,EAAoBhO,IAAIhZ,GAC1BgnB,EAAoBG,IAClBnnB,GAC+BknB,OAA/BA,EAACF,EAAoBI,IAAIpnB,IAAMknB,EAAI,GAAK,QAG1CF,EAAoBG,IAAInnB,EAAO,EAEnC,CACF,CAEA,OAAOgnB,CAAmB,GAE5Bn0B,EACEO,EAAMU,QACN,cAIR,wBCpCO,WAGL,OAAOV,GACLrC,GACE,IAAM,CACJqC,EAAMuJ,yBACNvJ,EAAM8D,WAAWqI,cACjBnM,EAAM8D,WAAWya,gBAEnB,CAACgK,EAAUpc,EAAeoS,KACxB,IACGgK,EAAS7D,KAAKhnB,SACA,MAAbyO,IAAAA,EAAezO,UAAW6gB,EAC5B,CACA,IAAK,IAAIpV,EAAI,EAAGA,EAAIof,EAAS5b,SAASjP,OAAQyL,IAC5Cof,EAAS5b,SAASxD,GAAIgD,cAAgB,CAAA,EACtCoc,EAAS5b,SAASxD,GAAIoF,kBAAoB,CAAA,EAE5C,OAAOga,CACT,CAEA,MAAM0L,EAAuD,GACvDC,EAAuD,UAE3D/nB,EAAAA,EAAiB,IAAI7O,SAAQb,IAAK,IAAA03B,EAClC,MAAMj0B,EAASF,EAAMmI,UAAU1L,EAAE4D,IAEjC,IAAKH,EACH,OAGF,MAAM8L,EAAW9L,EAAO2M,cAEnBb,GASLioB,EAAsBz2B,KAAK,CACzB6C,GAAI5D,EAAE4D,GACN2L,WACAkc,qBAAaiM,QAAEnoB,EAASb,0BAATa,EAASb,mBAAqB1O,EAAEmQ,QAAMunB,EAAI13B,EAAEmQ,OAC3D,IAGJ,MAAM+mB,GAA8B,MAAbxnB,EAAAA,EAAiB,IAAI3I,KAAI/G,GAAKA,EAAE4D,KAEjDoe,EAAiBze,EAAMgf,oBAEvBoV,EAA4Bp0B,EAC/B8I,oBACAvE,QAAOrE,GAAUA,EAAO0e,uBAoB3B,IAAIyV,EACAC,EAlBF/V,GACAE,GACA2V,EAA0B12B,SAE1Bi2B,EAAcn2B,KAAK,cAEnB42B,EAA0B92B,SAAQ4C,IAAU,IAAAq0B,EAC1CL,EAAsB12B,KAAK,CACzB6C,GAAIH,EAAOG,GACX2L,SAAUyS,EACVyJ,cACmD,OADtCqM,EACsB,MAAjC9V,EAAetT,wBAAkB,EAAjCsT,EAAetT,mBAAqBoT,IAAagW,EACjDhW,GACF,KAQN,IAAK,IAAIsV,EAAI,EAAGA,EAAItL,EAAS5b,SAASjP,OAAQm2B,IAAK,CACjD,MAAM5zB,EAAMsoB,EAAS5b,SAASknB,GAI9B,GAFA5zB,EAAIkM,cAAgB,GAEhB8nB,EAAsBv2B,OACxB,IAAK,IAAIyL,EAAI,EAAGA,EAAI8qB,EAAsBv2B,OAAQyL,IAAK,CACrDkrB,EAAsBJ,EAAsB9qB,GAC5C,MAAM9I,EAAKg0B,EAAoBh0B,GAG/BJ,EAAIkM,cAAc9L,GAAMg0B,EAAoBroB,SAC1C/L,EACAI,EACAg0B,EAAoBnM,eACpBsM,IACEv0B,EAAIsO,kBAAkBlO,GAAMm0B,CAAU,GAG5C,CAGF,GAAIN,EAAsBx2B,OAAQ,CAChC,IAAK,IAAIyL,EAAI,EAAGA,EAAI+qB,EAAsBx2B,OAAQyL,IAAK,CACrDmrB,EAAsBJ,EAAsB/qB,GAC5C,MAAM9I,EAAKi0B,EAAoBj0B,GAE/B,GACEi0B,EAAoBtoB,SAClB/L,EACAI,EACAi0B,EAAoBpM,eACpBsM,IACEv0B,EAAIsO,kBAAkBlO,GAAMm0B,CAAU,IAG1C,CACAv0B,EAAIkM,cAAcsoB,YAAa,EAC/B,KACF,CACF,EAEqC,IAAjCx0B,EAAIkM,cAAcsoB,aACpBx0B,EAAIkM,cAAcsoB,YAAa,EAEnC,CACF,CAaA,OAAO5E,GAAWtH,EAAS7D,MAXHzkB,IAEtB,IAAK,IAAIkJ,EAAI,EAAGA,EAAIwqB,EAAcj2B,OAAQyL,IACxC,IAA6C,IAAzClJ,EAAIkM,cAAcwnB,EAAcxqB,IAClC,OAAO,EAGX,OAAO,CAAI,GAIoCnJ,EAAM,GAEzDP,EAAeO,EAAMU,QAAS,aAAc,GAAuB,IACjEV,EAAMsiB,wBAGd,uBCjJO,WAGL,OAAOtiB,GACLrC,GACE,IAAM,CAACqC,EAAM8D,WAAWiN,SAAU/Q,EAAMsS,2BACxC,CAACvB,EAAUwX,KACT,IAAKA,EAAS7D,KAAKhnB,SAAWqT,EAASrT,OAKrC,OAJA6qB,EAAS7D,KAAKpnB,SAAQ2C,IACpBA,EAAIgB,MAAQ,EACZhB,EAAI8H,cAAWnG,CAAS,IAEnB2mB,EAIT,MAAMmM,EAAmB3jB,EAASxM,QAAOpE,GACvCH,EAAMmI,UAAUhI,KAGZw0B,EAAgC,GAChCC,EAA8C,CAAA,EAO9CC,EAAqB,SACzBnQ,EACAzjB,EACA8G,GAIA,QALK,IAAL9G,IAAAA,EAAQ,GAKJA,GAASyzB,EAAiBh3B,OAC5B,OAAOgnB,EAAKlhB,KAAIvD,IACdA,EAAIgB,MAAQA,EAEZ0zB,EAAgBn3B,KAAKyC,GACrB20B,EAAgB30B,EAAII,IAAMJ,EAEtBA,EAAI6H,UACN7H,EAAI6H,QAAU+sB,EAAmB50B,EAAI6H,QAAS7G,EAAQ,EAAGhB,EAAII,KAGxDJ,KAIX,MAAME,EAAmBu0B,EAAiBzzB,GAGpC6zB,EAsHhB,SAAwCpQ,EAAoBvkB,GAC1D,MAAM40B,EAAW,IAAIrrB,IAErB,OAAOgb,EAAKzb,QAAO,CAACzF,EAAKvD,KACvB,MAAM+0B,EAAS,GAAG/0B,EAAIqR,iBAAiBnR,KACjC80B,EAAWzxB,EAAIwwB,IAAIgB,GAMzB,OALKC,EAGHA,EAASz3B,KAAKyC,GAFduD,EAAIuwB,IAAIiB,EAAQ,CAAC/0B,IAIZuD,CAAG,GACTuxB,EACL,CAnI+BG,CAAQxQ,EAAMvkB,GAG7Bg1B,EAAwBv4B,MAAMwT,KAAK0kB,EAAaM,WAAW5xB,KAC/D,CAAArC,EAA+B1C,KAAU,IAAvC42B,EAAeC,GAAYn0B,EACvBd,EAAK,GAAGF,KAAYk1B,IACxBh1B,EAAK0H,EAAW,GAAGA,KAAY1H,IAAOA,EAGtC,MAAMyH,EAAU+sB,EAAmBS,EAAar0B,EAAQ,EAAGZ,GAE3DyH,EAAQxK,SAAQutB,IACdA,EAAO9iB,SAAW1H,CAAE,IAItB,MAAMoP,EAAWxO,EACbjE,EAAUs4B,GAAar1B,GAAOA,EAAI6H,UAClCwtB,EAEEr1B,EAAM0H,EACV3H,EACAK,EACAoP,EAAS,GAAI7H,SACbnJ,EACAwC,OACAW,EACAmG,GAuDF,OApDAgK,OAAOgf,OAAO9wB,EAAK,CACjByS,iBAAkBvS,EAClBk1B,gBACAvtB,UACA2H,WACAnP,SAAWH,IAET,GAAIu0B,EAAiB5yB,SAAS3B,GAAW,CACvC,GAAIF,EAAI+H,aAAaE,eAAe/H,GAClC,OAAOF,EAAI+H,aAAa7H,GAGN,IAAAo1B,EAApB,GAAID,EAAY,GACdr1B,EAAI+H,aAAa7H,GACkBo1B,OADTA,EACxBD,EAAY,GAAGh1B,SAASH,IAASo1B,OAAI3zB,EAGzC,OAAO3B,EAAI+H,aAAa7H,EAC1B,CAEA,GAAIF,EAAI0S,qBAAqBzK,eAAe/H,GAC1C,OAAOF,EAAI0S,qBAAqBxS,GAIlC,MAAMD,EAASF,EAAMmI,UAAUhI,GACzBq1B,EAAoB,MAANt1B,OAAM,EAANA,EAAQ+R,mBAE5B,OAAIujB,GACFv1B,EAAI0S,qBAAqBxS,GAAYq1B,EACnCr1B,EACAsP,EACA6lB,GAGKr1B,EAAI0S,qBAAqBxS,SAPlC,CAQA,IAIJ2H,EAAQxK,SAAQutB,IACd8J,EAAgBn3B,KAAKqtB,GACrB+J,EAAgB/J,EAAOxqB,IAAMwqB,CAAM,IAU9B5qB,CAAG,IAId,OAAOk1B,GAGHG,EAAcT,EAAmBtM,EAAS7D,KAAM,GActD,OAZA4Q,EAAYh4B,SAAQutB,IAClB8J,EAAgBn3B,KAAKqtB,GACrB+J,EAAgB/J,EAAOxqB,IAAMwqB,CAAM,IAU9B,CACLnG,KAAM4Q,EACN3oB,SAAUgoB,EACV9T,SAAU+T,EACX,GAEHn1B,EAAeO,EAAMU,QAAS,aAAc,GAAsB,KAChEV,EAAM8f,QAAO,KACX9f,EAAMyf,qBACNzf,EAAMsiB,qBAAqB,GAC3B,IAGV,6CC1KO,SAAsDxkB,GAG3D,OAAOkC,GACLrC,GACE,IAAM,CACJqC,EAAM8D,WAAWoe,WACjBliB,EAAMqgB,2BACNrgB,EAAMU,QAAQ4e,0BACV1d,EACA5B,EAAM8D,WAAWsb,YAEvB,CAAC8C,EAAYqG,KACX,IAAKA,EAAS7D,KAAKhnB,OACjB,OAAO6qB,EAGT,MAAMnG,SAAEA,EAAQD,UAAEA,GAAcD,EAChC,IAAIwC,KAAEA,EAAI/X,SAAEA,EAAQkU,SAAEA,GAAa0H,EACnC,MAAMkN,EAAYrT,EAAWD,EACvBuT,EAAUD,EAAYrT,EAI5B,IAAIuT,EAFJjR,EAAOA,EAAK7M,MAAM4d,EAAWC,GAW3BC,EAPG31B,EAAMU,QAAQ4e,qBAOG,CAClBoF,OACA/X,WACAkU,YATkB6O,GAAW,CAC7BhL,OACA/X,WACAkU,aAUJ8U,EAAkBhpB,SAAW,GAE7B,MAAMijB,EAAa3vB,IACjB01B,EAAkBhpB,SAASnP,KAAKyC,GAC5BA,EAAI6H,QAAQpK,QACduC,EAAI6H,QAAQxK,QAAQsyB,EACtB,EAKF,OAFA+F,EAAkBjR,KAAKpnB,QAAQsyB,GAExB+F,CAAiB,GAE1Bl2B,EAAeO,EAAMU,QAAS,cAEpC,sBCvDO,WAGL,OAAOV,GACLrC,GACE,IAAM,CAACqC,EAAM8D,WAAWsoB,QAASpsB,EAAMsvB,0BACvC,CAAClD,EAAS7D,KACR,IAAKA,EAAS7D,KAAKhnB,QAAkB,MAAP0uB,IAAAA,EAAS1uB,OACrC,OAAO6qB,EAGT,MAAMqN,EAAe51B,EAAM8D,WAAWsoB,QAEhCyJ,EAA+B,GAG/BC,EAAmBF,EAAarxB,QAAOyL,IAAI,IAAAmjB,EAAA,OAC/CA,OAD+CA,EAC/CnzB,EAAMmI,UAAU6H,EAAK3P,UAArB8yB,EAAAA,EAA0B3E,YAAY,IAGlCuH,EAOF,CAAA,EAEJD,EAAiBx4B,SAAQ04B,IACvB,MAAM91B,EAASF,EAAMmI,UAAU6tB,EAAU31B,IACpCH,IAEL61B,EAAeC,EAAU31B,IAAM,CAC7BisB,cAAepsB,EAAOc,UAAUsrB,cAChC2J,cAAe/1B,EAAOc,UAAUi1B,cAChC5J,UAAWnsB,EAAO4sB,gBACnB,IAGH,MAAMoJ,EAAYxR,IAGhB,MAAMyR,EAAazR,EAAKlhB,KAAIvD,IAAQ,IAAKA,MA4DzC,OA1DAk2B,EAAWnmB,MAAK,CAAC4b,EAAMC,KACrB,IAAK,IAAI1iB,EAAI,EAAGA,EAAI2sB,EAAiBp4B,OAAQyL,GAAK,EAAG,CAAA,IAAAitB,EACnD,MAAMJ,EAAYF,EAAiB3sB,GAC7BktB,EAAaN,EAAeC,EAAU31B,IACtCisB,EAAgB+J,EAAW/J,cAC3BgK,EAAwB,OAAlBF,EAAY,MAATJ,OAAS,EAATA,EAAW9I,OAAIkJ,EAE9B,IAAIG,EAAU,EAGd,GAAIjK,EAAe,CACjB,MAGMkK,OAAwB50B,IAHfgqB,EAAKtrB,SAAS01B,EAAU31B,IAIjCo2B,OAAwB70B,IAHfiqB,EAAKvrB,SAAS01B,EAAU31B,IAKvC,GAAIm2B,GAAcC,EAAY,CAC5B,GAAsB,UAAlBnK,EAA2B,OAAOkK,GAAc,EAAI,EACxD,GAAsB,SAAlBlK,EAA0B,OAAOkK,EAAa,GAAK,EACvDD,EACEC,GAAcC,EACV,EACAD,EACElK,GACCA,CACX,CACF,CAOA,GALgB,IAAZiK,IACFA,EAAUF,EAAWhK,UAAUT,EAAMC,EAAMmK,EAAU31B,KAIvC,IAAZk2B,EASF,OARID,IACFC,IAAY,GAGVF,EAAWJ,gBACbM,IAAY,GAGPA,CAEX,CAEA,OAAO3K,EAAKntB,MAAQotB,EAAKptB,KAAK,IAIhC03B,EAAW74B,SAAQ2C,IAAO,IAAA6S,EACxB+iB,EAAer4B,KAAKyC,GAChB6S,OAAJA,EAAI7S,EAAI6H,UAAJgL,EAAapV,SACfuC,EAAI6H,QAAUouB,EAASj2B,EAAI6H,SAC7B,IAGKquB,CAAU,EAGnB,MAAO,CACLzR,KAAMwR,EAAS3N,EAAS7D,MACxB/X,SAAUkpB,EACVhV,SAAU0H,EAAS1H,SACpB,GAEHphB,EAAeO,EAAMU,QAAS,aAAc,GAAqB,IAC/DV,EAAMsiB,wBAGd,+GlClCO,WACL"}
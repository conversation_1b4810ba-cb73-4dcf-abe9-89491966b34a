import {
  Ie,
  Me,
  Ne,
  Wr,
  after,
  argumentsTag,
  arrayBufferTag,
  arrayTag,
  ary,
  booleanTag,
  camelCase,
  capitalize,
  chunk,
  clamp,
  clone,
  cloneDeep,
  cloneDeepWith,
  compact,
  copyProperties,
  dataViewTag,
  dateTag,
  debounce,
  deburr,
  difference,
  differenceBy,
  differenceWith,
  drop,
  dropRight,
  dropRightWhile,
  dropWhile,
  eq,
  escape,
  escapeRegExp,
  fill,
  findKey,
  flatten,
  float32ArrayTag,
  float64ArrayTag,
  flow,
  flowRight,
  getSymbols,
  getTag,
  groupBy,
  head,
  identity,
  inRange,
  initial,
  int16ArrayTag,
  int32ArrayTag,
  int8ArrayTag,
  intersection,
  intersectionBy,
  intersectionWith,
  invert,
  isArray,
  isArrayBuffer,
  isBuffer,
  isDate,
  isEqual,
  isEqualWith,
  isFunction,
  isLength,
  isMap,
  isNil,
  isNull,
  isObjectLike,
  isPlainObject,
  isPrimitive,
  isRegExp,
  isSet,
  isSymbol,
  isSymbol2,
  isTypedArray,
  isUndefined,
  isWeakMap,
  isWeakSet,
  kebabCase,
  last,
  lowerCase,
  lowerFirst,
  mapKeys,
  mapTag,
  mapValues,
  maxBy,
  meanBy,
  minBy,
  negate,
  noop,
  numberTag,
  objectTag,
  once,
  pad,
  partialImpl,
  partialRightImpl,
  pull,
  qe,
  random,
  randomInt,
  range,
  regexpTag,
  remove,
  rest,
  sample,
  sampleSize,
  setTag,
  shuffle,
  snakeCase,
  stringTag,
  symbolTag,
  tail,
  take,
  takeRight,
  toFinite,
  toInteger,
  toNumber,
  trim,
  trimEnd,
  trimStart,
  uint16ArrayTag,
  uint32ArrayTag,
  uint8ArrayTag,
  uint8ClampedArrayTag,
  unary,
  unescape,
  uniq,
  uniqBy,
  uniqWith,
  unzip,
  upperCase,
  upperFirst,
  windowed,
  without,
  words,
  zip
} from "./chunk-Y7R5F67O.js";
import {
  computed,
  createSSRApp,
  defineComponent,
  h,
  isReactive,
  markRaw,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  shallowRef,
  watch
} from "./chunk-ABJBL742.js";
import {
  __export
} from "./chunk-YHHEEY6D.js";

// node_modules/es-toolkit/dist/compat/array/castArray.mjs
function castArray(value) {
  if (arguments.length === 0) {
    return [];
  }
  return Array.isArray(value) ? value : [value];
}

// node_modules/es-toolkit/dist/compat/_internal/toArray.mjs
function toArray(value) {
  return Array.isArray(value) ? value : Array.from(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayLike.mjs
function isArrayLike(value) {
  return value != null && typeof value !== "function" && isLength(value.length);
}

// node_modules/es-toolkit/dist/compat/array/chunk.mjs
function chunk2(arr, size2 = 1) {
  size2 = Math.max(Math.floor(size2), 0);
  if (size2 === 0 || !isArrayLike(arr)) {
    return [];
  }
  return chunk(toArray(arr), size2);
}

// node_modules/es-toolkit/dist/compat/array/compact.mjs
function compact2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return compact(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/concat.mjs
function concat(...values2) {
  return flatten(values2);
}

// node_modules/es-toolkit/dist/compat/_internal/isDeepKey.mjs
function isDeepKey(key) {
  switch (typeof key) {
    case "number":
    case "symbol": {
      return false;
    }
    case "string": {
      return key.includes(".") || key.includes("[") || key.includes("]");
    }
  }
}

// node_modules/es-toolkit/dist/compat/_internal/toKey.mjs
function toKey(value) {
  var _a;
  if (typeof value === "string" || typeof value === "symbol") {
    return value;
  }
  if (Object.is((_a = value == null ? void 0 : value.valueOf) == null ? void 0 : _a.call(value), -0)) {
    return "-0";
  }
  return String(value);
}

// node_modules/es-toolkit/dist/compat/util/toPath.mjs
function toPath(deepKey) {
  const result2 = [];
  const length = deepKey.length;
  if (length === 0) {
    return result2;
  }
  let index = 0;
  let key = "";
  let quoteChar = "";
  let bracket = false;
  if (deepKey.charCodeAt(0) === 46) {
    result2.push("");
    index++;
  }
  while (index < length) {
    const char = deepKey[index];
    if (quoteChar) {
      if (char === "\\" && index + 1 < length) {
        index++;
        key += deepKey[index];
      } else if (char === quoteChar) {
        quoteChar = "";
      } else {
        key += char;
      }
    } else if (bracket) {
      if (char === '"' || char === "'") {
        quoteChar = char;
      } else if (char === "]") {
        bracket = false;
        result2.push(key);
        key = "";
      } else {
        key += char;
      }
    } else {
      if (char === "[") {
        bracket = true;
        if (key) {
          result2.push(key);
          key = "";
        }
      } else if (char === ".") {
        if (key) {
          result2.push(key);
          key = "";
        }
      } else {
        key += char;
      }
    }
    index++;
  }
  if (key) {
    result2.push(key);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/get.mjs
function get(object, path, defaultValue) {
  if (object == null) {
    return defaultValue;
  }
  switch (typeof path) {
    case "string": {
      const result2 = object[path];
      if (result2 === void 0) {
        if (isDeepKey(path)) {
          return get(object, toPath(path), defaultValue);
        } else {
          return defaultValue;
        }
      }
      return result2;
    }
    case "number":
    case "symbol": {
      if (typeof path === "number") {
        path = toKey(path);
      }
      const result2 = object[path];
      if (result2 === void 0) {
        return defaultValue;
      }
      return result2;
    }
    default: {
      if (Array.isArray(path)) {
        return getWithPath(object, path, defaultValue);
      }
      if (Object.is(path == null ? void 0 : path.valueOf(), -0)) {
        path = "-0";
      } else {
        path = String(path);
      }
      const result2 = object[path];
      if (result2 === void 0) {
        return defaultValue;
      }
      return result2;
    }
  }
}
function getWithPath(object, path, defaultValue) {
  if (path.length === 0) {
    return defaultValue;
  }
  let current = object;
  for (let index = 0; index < path.length; index++) {
    if (current == null) {
      return defaultValue;
    }
    current = current[path[index]];
  }
  if (current === void 0) {
    return defaultValue;
  }
  return current;
}

// node_modules/es-toolkit/dist/compat/object/property.mjs
function property(path) {
  return function(object) {
    return get(object, path);
  };
}

// node_modules/es-toolkit/dist/compat/predicate/isObject.mjs
function isObject(value) {
  return value !== null && (typeof value === "object" || typeof value === "function");
}

// node_modules/es-toolkit/dist/compat/predicate/isMatchWith.mjs
function isMatchWith(target, source, compare) {
  compare = typeof compare === "function" ? compare : void 0;
  return isMatchWithInternal(target, source, function doesMatch(objValue, srcValue, key, object, source2, stack) {
    const isEqual2 = compare == null ? void 0 : compare(objValue, srcValue, key, object, source2, stack);
    if (isEqual2 !== void 0) {
      return Boolean(isEqual2);
    }
    return isMatchWithInternal(objValue, srcValue, doesMatch, stack);
  }, /* @__PURE__ */ new Map());
}
function isMatchWithInternal(target, source, compare, stack) {
  if (source === target) {
    return true;
  }
  switch (typeof source) {
    case "object": {
      return isObjectMatch(target, source, compare, stack);
    }
    case "function": {
      const sourceKeys = Object.keys(source);
      if (sourceKeys.length > 0) {
        return isMatchWithInternal(target, { ...source }, compare, stack);
      }
      return eq(target, source);
    }
    default: {
      if (!isObject(target)) {
        return eq(target, source);
      }
      if (typeof source === "string") {
        return source === "";
      }
      return true;
    }
  }
}
function isObjectMatch(target, source, compare, stack) {
  if (source == null) {
    return true;
  }
  if (Array.isArray(source)) {
    return isArrayMatch(target, source, compare, stack);
  }
  if (source instanceof Map) {
    return isMapMatch(target, source, compare, stack);
  }
  if (source instanceof Set) {
    return isSetMatch(target, source, compare, stack);
  }
  const keys2 = Object.keys(source);
  if (target == null) {
    return keys2.length === 0;
  }
  if (keys2.length === 0) {
    return true;
  }
  if (stack && stack.has(source)) {
    return stack.get(source) === target;
  }
  if (stack) {
    stack.set(source, target);
  }
  try {
    for (let i = 0; i < keys2.length; i++) {
      const key = keys2[i];
      if (!isPrimitive(target) && !(key in target)) {
        return false;
      }
      if (source[key] === void 0 && target[key] !== void 0) {
        return false;
      }
      if (source[key] === null && target[key] !== null) {
        return false;
      }
      const isEqual2 = compare(target[key], source[key], key, target, source, stack);
      if (!isEqual2) {
        return false;
      }
    }
    return true;
  } finally {
    if (stack) {
      stack.delete(source);
    }
  }
}
function isMapMatch(target, source, compare, stack) {
  if (source.size === 0) {
    return true;
  }
  if (!(target instanceof Map)) {
    return false;
  }
  for (const [key, sourceValue] of source.entries()) {
    const targetValue = target.get(key);
    const isEqual2 = compare(targetValue, sourceValue, key, target, source, stack);
    if (isEqual2 === false) {
      return false;
    }
  }
  return true;
}
function isArrayMatch(target, source, compare, stack) {
  if (source.length === 0) {
    return true;
  }
  if (!Array.isArray(target)) {
    return false;
  }
  const countedIndex = /* @__PURE__ */ new Set();
  for (let i = 0; i < source.length; i++) {
    const sourceItem = source[i];
    let found = false;
    for (let j = 0; j < target.length; j++) {
      if (countedIndex.has(j)) {
        continue;
      }
      const targetItem = target[j];
      let matches2 = false;
      const isEqual2 = compare(targetItem, sourceItem, i, target, source, stack);
      if (isEqual2) {
        matches2 = true;
      }
      if (matches2) {
        countedIndex.add(j);
        found = true;
        break;
      }
    }
    if (!found) {
      return false;
    }
  }
  return true;
}
function isSetMatch(target, source, compare, stack) {
  if (source.size === 0) {
    return true;
  }
  if (!(target instanceof Set)) {
    return false;
  }
  return isArrayMatch([...target], [...source], compare, stack);
}

// node_modules/es-toolkit/dist/compat/predicate/isMatch.mjs
function isMatch(target, source) {
  return isMatchWith(target, source);
}

// node_modules/es-toolkit/dist/compat/predicate/matches.mjs
function matches(source) {
  source = cloneDeep(source);
  return (target) => {
    return isMatch(target, source);
  };
}

// node_modules/es-toolkit/dist/compat/object/cloneDeepWith.mjs
function cloneDeepWith2(obj, cloneValue) {
  return cloneDeepWith(obj, (value, key, object, stack) => {
    const cloned = cloneValue == null ? void 0 : cloneValue(value, key, object, stack);
    if (cloned != null) {
      return cloned;
    }
    if (typeof obj !== "object") {
      return void 0;
    }
    switch (Object.prototype.toString.call(obj)) {
      case numberTag:
      case stringTag:
      case booleanTag: {
        const result2 = new obj.constructor(obj == null ? void 0 : obj.valueOf());
        copyProperties(result2, obj);
        return result2;
      }
      case argumentsTag: {
        const result2 = {};
        copyProperties(result2, obj);
        result2.length = obj.length;
        result2[Symbol.iterator] = obj[Symbol.iterator];
        return result2;
      }
      default: {
        return void 0;
      }
    }
  });
}

// node_modules/es-toolkit/dist/compat/object/cloneDeep.mjs
function cloneDeep2(obj) {
  return cloneDeepWith2(obj);
}

// node_modules/es-toolkit/dist/compat/_internal/isIndex.mjs
var IS_UNSIGNED_INTEGER = /^(?:0|[1-9]\d*)$/;
function isIndex(value, length = Number.MAX_SAFE_INTEGER) {
  switch (typeof value) {
    case "number": {
      return Number.isInteger(value) && value >= 0 && value < length;
    }
    case "symbol": {
      return false;
    }
    case "string": {
      return IS_UNSIGNED_INTEGER.test(value);
    }
  }
}

// node_modules/es-toolkit/dist/compat/predicate/isArguments.mjs
function isArguments(value) {
  return value !== null && typeof value === "object" && getTag(value) === "[object Arguments]";
}

// node_modules/es-toolkit/dist/compat/object/has.mjs
function has(object, path) {
  let resolvedPath;
  if (Array.isArray(path)) {
    resolvedPath = path;
  } else if (typeof path === "string" && isDeepKey(path) && (object == null ? void 0 : object[path]) == null) {
    resolvedPath = toPath(path);
  } else {
    resolvedPath = [path];
  }
  if (resolvedPath.length === 0) {
    return false;
  }
  let current = object;
  for (let i = 0; i < resolvedPath.length; i++) {
    const key = resolvedPath[i];
    if (current == null || !Object.hasOwn(current, key)) {
      const isSparseIndex = (Array.isArray(current) || isArguments(current)) && isIndex(key) && key < current.length;
      if (!isSparseIndex) {
        return false;
      }
    }
    current = current[key];
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/matchesProperty.mjs
function matchesProperty(property2, source) {
  switch (typeof property2) {
    case "object": {
      if (Object.is(property2 == null ? void 0 : property2.valueOf(), -0)) {
        property2 = "-0";
      }
      break;
    }
    case "number": {
      property2 = toKey(property2);
      break;
    }
  }
  source = cloneDeep2(source);
  return function(target) {
    const result2 = get(target, property2);
    if (result2 === void 0) {
      return has(target, property2);
    }
    if (source === void 0) {
      return result2 === void 0;
    }
    return isMatch(result2, source);
  };
}

// node_modules/es-toolkit/dist/compat/util/iteratee.mjs
function iteratee(value) {
  if (value == null) {
    return identity;
  }
  switch (typeof value) {
    case "function": {
      return value;
    }
    case "object": {
      if (Array.isArray(value) && value.length === 2) {
        return matchesProperty(value[0], value[1]);
      }
      return matches(value);
    }
    case "string":
    case "symbol":
    case "number": {
      return property(value);
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/countBy.mjs
function countBy(collection, iteratee$1) {
  if (collection == null) {
    return {};
  }
  const array = isArrayLike(collection) ? Array.from(collection) : Object.values(collection);
  const mapper = iteratee(iteratee$1 ?? void 0);
  const result2 = /* @__PURE__ */ Object.create(null);
  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    const key = mapper(item);
    result2[key] = (result2[key] ?? 0) + 1;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayLikeObject.mjs
function isArrayLikeObject(value) {
  return isObjectLike(value) && isArrayLike(value);
}

// node_modules/es-toolkit/dist/compat/array/difference.mjs
function difference2(arr, ...values2) {
  if (!isArrayLikeObject(arr)) {
    return [];
  }
  const arr1 = toArray(arr);
  const arr2 = [];
  for (let i = 0; i < values2.length; i++) {
    const value = values2[i];
    if (isArrayLikeObject(value)) {
      arr2.push(...Array.from(value));
    }
  }
  return difference(arr1, arr2);
}

// node_modules/es-toolkit/dist/compat/array/last.mjs
function last2(array) {
  if (!isArrayLike(array)) {
    return void 0;
  }
  return last(toArray(array));
}

// node_modules/es-toolkit/dist/compat/_internal/flattenArrayLike.mjs
function flattenArrayLike(values2) {
  const result2 = [];
  for (let i = 0; i < values2.length; i++) {
    const arrayLike = values2[i];
    if (!isArrayLikeObject(arrayLike)) {
      continue;
    }
    for (let j = 0; j < arrayLike.length; j++) {
      result2.push(arrayLike[j]);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/differenceBy.mjs
function differenceBy2(arr, ..._values) {
  if (!isArrayLikeObject(arr)) {
    return [];
  }
  const iteratee$1 = last2(_values);
  const values2 = flattenArrayLike(_values);
  if (isArrayLikeObject(iteratee$1)) {
    return difference(Array.from(arr), values2);
  }
  return differenceBy(Array.from(arr), values2, iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/array/differenceWith.mjs
function differenceWith2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const comparator = last2(values2);
  const flattenedValues = flattenArrayLike(values2);
  if (typeof comparator === "function") {
    return differenceWith(Array.from(array), flattenedValues, comparator);
  }
  return difference(Array.from(array), flattenedValues);
}

// node_modules/es-toolkit/dist/compat/array/drop.mjs
function drop2(collection, itemsCount = 1, guard) {
  if (!isArrayLike(collection)) {
    return [];
  }
  itemsCount = guard ? 1 : toInteger(itemsCount);
  return drop(toArray(collection), itemsCount);
}

// node_modules/es-toolkit/dist/compat/array/dropRight.mjs
function dropRight2(collection, itemsCount = 1, guard) {
  if (!isArrayLike(collection)) {
    return [];
  }
  itemsCount = guard ? 1 : toInteger(itemsCount);
  return dropRight(toArray(collection), itemsCount);
}

// node_modules/es-toolkit/dist/compat/array/dropRightWhile.mjs
function dropRightWhile2(arr, predicate) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return dropRightWhileImpl(Array.from(arr), predicate);
}
function dropRightWhileImpl(arr, predicate) {
  switch (typeof predicate) {
    case "function": {
      return dropRightWhile(arr, (item, index, arr2) => Boolean(predicate(item, index, arr2)));
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        return dropRightWhile(arr, matchesProperty(key, value));
      } else {
        return dropRightWhile(arr, matches(predicate));
      }
    }
    case "symbol":
    case "number":
    case "string": {
      return dropRightWhile(arr, property(predicate));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/dropWhile.mjs
function dropWhile2(arr, predicate) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return dropWhileImpl(toArray(arr), predicate);
}
function dropWhileImpl(arr, predicate) {
  switch (typeof predicate) {
    case "function": {
      return dropWhile(arr, (item, index, arr2) => Boolean(predicate(item, index, arr2)));
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        return dropWhile(arr, matchesProperty(key, value));
      } else {
        return dropWhile(arr, matches(predicate));
      }
    }
    case "number":
    case "symbol":
    case "string": {
      return dropWhile(arr, property(predicate));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/forEach.mjs
function forEach(collection, callback = identity) {
  if (!collection) {
    return collection;
  }
  const keys2 = isArrayLike(collection) || Array.isArray(collection) ? range(0, collection.length) : Object.keys(collection);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    const result2 = callback(value, key, collection);
    if (result2 === false) {
      break;
    }
  }
  return collection;
}

// node_modules/es-toolkit/dist/compat/array/forEachRight.mjs
function forEachRight(collection, callback = identity) {
  if (!collection) {
    return collection;
  }
  const keys2 = isArrayLike(collection) ? range(0, collection.length) : Object.keys(collection);
  for (let i = keys2.length - 1; i >= 0; i--) {
    const key = keys2[i];
    const value = collection[key];
    const result2 = callback(value, key, collection);
    if (result2 === false) {
      break;
    }
  }
  return collection;
}

// node_modules/es-toolkit/dist/compat/_internal/isIterateeCall.mjs
function isIterateeCall(value, index, object) {
  if (!isObject(object)) {
    return false;
  }
  if (typeof index === "number" && isArrayLike(object) && isIndex(index) && index < object.length || typeof index === "string" && index in object) {
    return eq(object[index], value);
  }
  return false;
}

// node_modules/es-toolkit/dist/compat/array/every.mjs
function every(source, doesMatch, guard) {
  if (!source) {
    return true;
  }
  if (guard && isIterateeCall(source, doesMatch, guard)) {
    doesMatch = void 0;
  }
  if (!doesMatch) {
    doesMatch = identity;
  }
  let predicate;
  switch (typeof doesMatch) {
    case "function": {
      predicate = doesMatch;
      break;
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        predicate = matchesProperty(key, value);
      } else {
        predicate = matches(doesMatch);
      }
      break;
    }
    case "symbol":
    case "number":
    case "string": {
      predicate = property(doesMatch);
    }
  }
  if (!isArrayLike(source)) {
    const keys2 = Object.keys(source);
    for (let i = 0; i < keys2.length; i++) {
      const key = keys2[i];
      const value = source[key];
      if (!predicate(value, key, source)) {
        return false;
      }
    }
    return true;
  }
  for (let i = 0; i < source.length; i++) {
    if (!predicate(source[i], i, source)) {
      return false;
    }
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/isString.mjs
function isString(value) {
  return typeof value === "string" || value instanceof String;
}

// node_modules/es-toolkit/dist/compat/array/fill.mjs
function fill2(array, value, start = 0, end = array ? array.length : 0) {
  if (!isArrayLike(array)) {
    return [];
  }
  if (isString(array)) {
    return array;
  }
  start = Math.floor(start);
  end = Math.floor(end);
  if (!start) {
    start = 0;
  }
  if (!end) {
    end = 0;
  }
  return fill(array, value, start, end);
}

// node_modules/es-toolkit/dist/compat/array/filter.mjs
function filter(source, predicate) {
  if (!source) {
    return [];
  }
  predicate = iteratee(predicate);
  if (!Array.isArray(source)) {
    const result3 = [];
    const keys2 = Object.keys(source);
    const length2 = isArrayLike(source) ? source.length : keys2.length;
    for (let i = 0; i < length2; i++) {
      const key = keys2[i];
      const value = source[key];
      if (predicate(value, key, source)) {
        result3.push(value);
      }
    }
    return result3;
  }
  const result2 = [];
  const length = source.length;
  for (let i = 0; i < length; i++) {
    const value = source[i];
    if (predicate(value, i, source)) {
      result2.push(value);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/find.mjs
function find(source, _doesMatch, fromIndex = 0) {
  if (!source) {
    return void 0;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(source.length + fromIndex, 0);
  }
  const doesMatch = iteratee(_doesMatch);
  if (typeof doesMatch === "function" && !Array.isArray(source)) {
    const keys2 = Object.keys(source);
    for (let i = fromIndex; i < keys2.length; i++) {
      const key = keys2[i];
      const value = source[key];
      if (doesMatch(value, key, source)) {
        return value;
      }
    }
    return void 0;
  }
  const values2 = Array.isArray(source) ? source.slice(fromIndex) : Object.values(source).slice(fromIndex);
  return values2.find(doesMatch);
}

// node_modules/es-toolkit/dist/compat/array/findIndex.mjs
function findIndex(arr, doesMatch, fromIndex = 0) {
  if (!arr) {
    return -1;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(arr.length + fromIndex, 0);
  }
  const subArray = Array.from(arr).slice(fromIndex);
  let index = -1;
  switch (typeof doesMatch) {
    case "function": {
      index = subArray.findIndex(doesMatch);
      break;
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        index = subArray.findIndex(matchesProperty(key, value));
      } else {
        index = subArray.findIndex(matches(doesMatch));
      }
      break;
    }
    case "number":
    case "symbol":
    case "string": {
      index = subArray.findIndex(property(doesMatch));
    }
  }
  return index === -1 ? -1 : index + fromIndex;
}

// node_modules/es-toolkit/dist/compat/array/findLast.mjs
function findLast(source, _doesMatch, fromIndex) {
  if (!source) {
    return void 0;
  }
  const length = Array.isArray(source) ? source.length : Object.keys(source).length;
  fromIndex = toInteger(fromIndex ?? length - 1);
  if (fromIndex < 0) {
    fromIndex = Math.max(length + fromIndex, 0);
  } else {
    fromIndex = Math.min(fromIndex, length - 1);
  }
  const doesMatch = iteratee(_doesMatch);
  if (typeof doesMatch === "function" && !Array.isArray(source)) {
    const keys2 = Object.keys(source);
    for (let i = fromIndex; i >= 0; i--) {
      const key = keys2[i];
      const value = source[key];
      if (doesMatch(value, key, source)) {
        return value;
      }
    }
    return void 0;
  }
  const values2 = Array.isArray(source) ? source.slice(0, fromIndex + 1) : Object.values(source).slice(0, fromIndex + 1);
  return values2.findLast(doesMatch);
}

// node_modules/es-toolkit/dist/compat/array/findLastIndex.mjs
function findLastIndex(arr, doesMatch, fromIndex = arr ? arr.length - 1 : 0) {
  if (!arr) {
    return -1;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(arr.length + fromIndex, 0);
  } else {
    fromIndex = Math.min(fromIndex, arr.length - 1);
  }
  const subArray = toArray(arr).slice(0, fromIndex + 1);
  switch (typeof doesMatch) {
    case "function": {
      return subArray.findLastIndex(doesMatch);
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        return subArray.findLastIndex(matchesProperty(key, value));
      } else {
        return subArray.findLastIndex(matches(doesMatch));
      }
    }
    case "number":
    case "symbol":
    case "string": {
      return subArray.findLastIndex(property(doesMatch));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/head.mjs
function head2(arr) {
  if (!isArrayLike(arr)) {
    return void 0;
  }
  return head(toArray(arr));
}

// node_modules/es-toolkit/dist/compat/array/flatten.mjs
function flatten2(value, depth = 1) {
  const result2 = [];
  const flooredDepth = Math.floor(depth);
  if (!isArrayLike(value)) {
    return result2;
  }
  const recursive = (arr, currentDepth) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i];
      if (currentDepth < flooredDepth && (Array.isArray(item) || Boolean(item == null ? void 0 : item[Symbol.isConcatSpreadable]) || item !== null && typeof item === "object" && Object.prototype.toString.call(item) === "[object Arguments]")) {
        if (Array.isArray(item)) {
          recursive(item, currentDepth + 1);
        } else {
          recursive(Array.from(item), currentDepth + 1);
        }
      } else {
        result2.push(item);
      }
    }
  };
  recursive(Array.from(value), 0);
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/map.mjs
function map(collection, _iteratee) {
  if (!collection) {
    return [];
  }
  const keys2 = isArrayLike(collection) || Array.isArray(collection) ? range(0, collection.length) : Object.keys(collection);
  const iteratee$1 = iteratee(_iteratee ?? identity);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    result2[i] = iteratee$1(value, key, collection);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/flatMap.mjs
function flatMap(collection, iteratee2) {
  if (isNil(collection)) {
    return [];
  }
  const mapped = isNil(iteratee2) ? map(collection) : map(collection, iteratee2);
  return flatten2(mapped, 1);
}

// node_modules/es-toolkit/dist/compat/array/flatMapDepth.mjs
function flatMapDepth(collection, iteratee$1, depth = 1) {
  if (collection == null) {
    return [];
  }
  const iterateeFn = iteratee(iteratee$1);
  const mapped = map(collection, iterateeFn);
  return flatten2(mapped, depth);
}

// node_modules/es-toolkit/dist/compat/array/flatMapDeep.mjs
function flatMapDeep(collection, iteratee2) {
  return flatMapDepth(collection, iteratee2, Infinity);
}

// node_modules/es-toolkit/dist/compat/array/flattenDeep.mjs
function flattenDeep(value) {
  return flatten2(value, Infinity);
}

// node_modules/es-toolkit/dist/compat/array/flattenDepth.mjs
function flattenDepth(value, depth = 1) {
  return flatten2(value, depth);
}

// node_modules/es-toolkit/dist/compat/array/groupBy.mjs
function groupBy2(source, _getKeyFromItem) {
  if (source == null) {
    return {};
  }
  const items = isArrayLike(source) ? Array.from(source) : Object.values(source);
  const getKeyFromItem = iteratee(_getKeyFromItem ?? identity);
  return groupBy(items, getKeyFromItem);
}

// node_modules/es-toolkit/dist/compat/array/includes.mjs
function includes(source, target, fromIndex, guard) {
  if (source == null) {
    return false;
  }
  if (guard || !fromIndex) {
    fromIndex = 0;
  } else {
    fromIndex = toInteger(fromIndex);
  }
  if (isString(source)) {
    if (fromIndex > source.length || target instanceof RegExp) {
      return false;
    }
    if (fromIndex < 0) {
      fromIndex = Math.max(0, source.length + fromIndex);
    }
    return source.includes(target, fromIndex);
  }
  if (Array.isArray(source)) {
    return source.includes(target, fromIndex);
  }
  const keys2 = Object.keys(source);
  if (fromIndex < 0) {
    fromIndex = Math.max(0, keys2.length + fromIndex);
  }
  for (let i = fromIndex; i < keys2.length; i++) {
    const value = Reflect.get(source, keys2[i]);
    if (eq(value, target)) {
      return true;
    }
  }
  return false;
}

// node_modules/es-toolkit/dist/compat/array/indexOf.mjs
function indexOf(array, searchElement, fromIndex) {
  if (!isArrayLike(array)) {
    return -1;
  }
  if (Number.isNaN(searchElement)) {
    fromIndex = fromIndex ?? 0;
    if (fromIndex < 0) {
      fromIndex = Math.max(0, array.length + fromIndex);
    }
    for (let i = fromIndex; i < array.length; i++) {
      if (Number.isNaN(array[i])) {
        return i;
      }
    }
    return -1;
  }
  return Array.from(array).indexOf(searchElement, fromIndex);
}

// node_modules/es-toolkit/dist/compat/array/initial.mjs
function initial2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return initial(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/intersection.mjs
function intersection2(...arrays) {
  if (arrays.length === 0) {
    return [];
  }
  if (!isArrayLikeObject(arrays[0])) {
    return [];
  }
  let result2 = uniq(Array.from(arrays[0]));
  for (let i = 1; i < arrays.length; i++) {
    const array = arrays[i];
    if (!isArrayLikeObject(array)) {
      return [];
    }
    result2 = intersection(result2, Array.from(array));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/intersectionBy.mjs
function intersectionBy2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const lastValue = last(values2);
  if (lastValue === void 0) {
    return Array.from(array);
  }
  let result2 = uniq(Array.from(array));
  const count = isArrayLikeObject(lastValue) ? values2.length : values2.length - 1;
  for (let i = 0; i < count; ++i) {
    const value = values2[i];
    if (!isArrayLikeObject(value)) {
      return [];
    }
    if (isArrayLikeObject(lastValue)) {
      result2 = intersectionBy(result2, Array.from(value), identity);
    } else if (typeof lastValue === "function") {
      result2 = intersectionBy(result2, Array.from(value), (value2) => lastValue(value2));
    } else if (typeof lastValue === "string") {
      result2 = intersectionBy(result2, Array.from(value), property(lastValue));
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/uniq.mjs
function uniq2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return uniq(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/intersectionWith.mjs
function intersectionWith2(firstArr, ...otherArrs) {
  if (firstArr == null) {
    return [];
  }
  const _comparator = last2(otherArrs);
  let comparator = eq;
  let uniq$1 = uniq2;
  if (typeof _comparator === "function") {
    comparator = _comparator;
    uniq$1 = uniqPreserve0;
    otherArrs.pop();
  }
  let result2 = uniq$1(Array.from(firstArr));
  for (let i = 0; i < otherArrs.length; ++i) {
    const otherArr = otherArrs[i];
    if (otherArr == null) {
      return [];
    }
    result2 = intersectionWith(result2, Array.from(otherArr), comparator);
  }
  return result2;
}
function uniqPreserve0(arr) {
  const result2 = [];
  const added = /* @__PURE__ */ new Set();
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (added.has(item)) {
      continue;
    }
    result2.push(item);
    added.add(item);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/invokeMap.mjs
function invokeMap(collection, path, ...args) {
  if (isNil(collection)) {
    return [];
  }
  const values2 = isArrayLike(collection) ? Array.from(collection) : Object.values(collection);
  const result2 = [];
  for (let i = 0; i < values2.length; i++) {
    const value = values2[i];
    if (isFunction(path)) {
      result2.push(path.apply(value, args));
      continue;
    }
    const method2 = get(value, path);
    let thisContext = value;
    if (Array.isArray(path)) {
      const pathExceptLast = path.slice(0, -1);
      if (pathExceptLast.length > 0) {
        thisContext = get(value, pathExceptLast);
      }
    } else if (typeof path === "string" && path.includes(".")) {
      const parts = path.split(".");
      const pathExceptLast = parts.slice(0, -1).join(".");
      thisContext = get(value, pathExceptLast);
    }
    result2.push(method2 == null ? void 0 : method2.apply(thisContext, args));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/join.mjs
function join(array, separator = ",") {
  if (!isArrayLike(array)) {
    return "";
  }
  return Array.from(array).join(separator);
}

// node_modules/es-toolkit/dist/compat/array/reduce.mjs
function reduce(collection, iteratee2 = identity, accumulator) {
  if (!collection) {
    return accumulator;
  }
  let keys2;
  let startIndex = 0;
  if (isArrayLike(collection)) {
    keys2 = range(0, collection.length);
    if (accumulator == null && collection.length > 0) {
      accumulator = collection[0];
      startIndex += 1;
    }
  } else {
    keys2 = Object.keys(collection);
    if (accumulator == null) {
      accumulator = collection[keys2[0]];
      startIndex += 1;
    }
  }
  for (let i = startIndex; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    accumulator = iteratee2(accumulator, value, key, collection);
  }
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/array/keyBy.mjs
function keyBy(collection, iteratee$1) {
  if (!isArrayLike(collection) && !isObjectLike(collection)) {
    return {};
  }
  const keyFn = iteratee(iteratee$1 ?? identity);
  return reduce(collection, (result2, value) => {
    const key = keyFn(value);
    result2[key] = value;
    return result2;
  }, {});
}

// node_modules/es-toolkit/dist/compat/array/lastIndexOf.mjs
function lastIndexOf(array, searchElement, fromIndex) {
  if (!isArrayLike(array) || array.length === 0) {
    return -1;
  }
  const length = array.length;
  let index = fromIndex ?? length - 1;
  if (fromIndex != null) {
    index = index < 0 ? Math.max(length + index, 0) : Math.min(index, length - 1);
  }
  if (Number.isNaN(searchElement)) {
    for (let i = index; i >= 0; i--) {
      if (Number.isNaN(array[i])) {
        return i;
      }
    }
  }
  return Array.from(array).lastIndexOf(searchElement, index);
}

// node_modules/es-toolkit/dist/compat/array/nth.mjs
function nth(array, n = 0) {
  if (!isArrayLikeObject(array) || array.length === 0) {
    return void 0;
  }
  n = toInteger(n);
  if (n < 0) {
    n += array.length;
  }
  return array[n];
}

// node_modules/es-toolkit/dist/compat/_internal/compareValues.mjs
function getPriority(a) {
  if (typeof a === "symbol") {
    return 1;
  }
  if (a === null) {
    return 2;
  }
  if (a === void 0) {
    return 3;
  }
  if (a !== a) {
    return 4;
  }
  return 0;
}
var compareValues = (a, b, order) => {
  if (a !== b) {
    const aPriority = getPriority(a);
    const bPriority = getPriority(b);
    if (aPriority === bPriority && aPriority === 0) {
      if (a < b) {
        return order === "desc" ? 1 : -1;
      }
      if (a > b) {
        return order === "desc" ? -1 : 1;
      }
    }
    return order === "desc" ? bPriority - aPriority : aPriority - bPriority;
  }
  return 0;
};

// node_modules/es-toolkit/dist/compat/_internal/isKey.mjs
var regexIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/;
var regexIsPlainProp = /^\w*$/;
function isKey(value, object) {
  if (Array.isArray(value)) {
    return false;
  }
  if (typeof value === "number" || typeof value === "boolean" || value == null || isSymbol(value)) {
    return true;
  }
  return typeof value === "string" && (regexIsPlainProp.test(value) || !regexIsDeepProp.test(value)) || object != null && Object.hasOwn(object, value);
}

// node_modules/es-toolkit/dist/compat/array/orderBy.mjs
function orderBy(collection, criteria, orders, guard) {
  if (collection == null) {
    return [];
  }
  orders = guard ? void 0 : orders;
  if (!Array.isArray(collection)) {
    collection = Object.values(collection);
  }
  if (!Array.isArray(criteria)) {
    criteria = criteria == null ? [null] : [criteria];
  }
  if (criteria.length === 0) {
    criteria = [null];
  }
  if (!Array.isArray(orders)) {
    orders = orders == null ? [] : [orders];
  }
  orders = orders.map((order) => String(order));
  const getValueByNestedPath = (object, path) => {
    let target = object;
    for (let i = 0; i < path.length && target != null; ++i) {
      target = target[path[i]];
    }
    return target;
  };
  const getValueByCriterion = (criterion, object) => {
    if (object == null || criterion == null) {
      return object;
    }
    if (typeof criterion === "object" && "key" in criterion) {
      if (Object.hasOwn(object, criterion.key)) {
        return object[criterion.key];
      }
      return getValueByNestedPath(object, criterion.path);
    }
    if (typeof criterion === "function") {
      return criterion(object);
    }
    if (Array.isArray(criterion)) {
      return getValueByNestedPath(object, criterion);
    }
    if (typeof object === "object") {
      return object[criterion];
    }
    return object;
  };
  const preparedCriteria = criteria.map((criterion) => {
    if (Array.isArray(criterion) && criterion.length === 1) {
      criterion = criterion[0];
    }
    if (criterion == null || typeof criterion === "function" || Array.isArray(criterion) || isKey(criterion)) {
      return criterion;
    }
    return { key: criterion, path: toPath(criterion) };
  });
  const preparedCollection = collection.map((item) => ({
    original: item,
    criteria: preparedCriteria.map((criterion) => getValueByCriterion(criterion, item))
  }));
  return preparedCollection.slice().sort((a, b) => {
    for (let i = 0; i < preparedCriteria.length; i++) {
      const comparedResult = compareValues(a.criteria[i], b.criteria[i], orders[i]);
      if (comparedResult !== 0) {
        return comparedResult;
      }
    }
    return 0;
  }).map((item) => item.original);
}

// node_modules/es-toolkit/dist/compat/array/partition.mjs
function partition(source, predicate) {
  if (!source) {
    return [[], []];
  }
  const collection = isArrayLike(source) ? source : Object.values(source);
  predicate = iteratee(predicate);
  const matched = [];
  const unmatched = [];
  for (let i = 0; i < collection.length; i++) {
    const value = collection[i];
    if (predicate(value)) {
      matched.push(value);
    } else {
      unmatched.push(value);
    }
  }
  return [matched, unmatched];
}

// node_modules/es-toolkit/dist/compat/array/pull.mjs
function pull2(arr, ...valuesToRemove) {
  return pull(arr, valuesToRemove);
}

// node_modules/es-toolkit/dist/compat/array/pullAll.mjs
function pullAll(arr, valuesToRemove = []) {
  return pull(arr, Array.from(valuesToRemove));
}

// node_modules/es-toolkit/dist/compat/array/pullAllBy.mjs
function pullAllBy(arr, valuesToRemove, _getValue) {
  const getValue = iteratee(_getValue);
  const valuesSet = new Set(Array.from(valuesToRemove).map((x2) => getValue(x2)));
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    const value = getValue(arr[i]);
    if (valuesSet.has(value)) {
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return arr;
}

// node_modules/es-toolkit/dist/compat/_internal/copyArray.mjs
function copyArray(source, array) {
  const length = source.length;
  if (array == null) {
    array = Array(length);
  }
  for (let i = 0; i < length; i++) {
    array[i] = source[i];
  }
  return array;
}

// node_modules/es-toolkit/dist/compat/array/pullAllWith.mjs
function pullAllWith(array, values2, comparator) {
  if ((array == null ? void 0 : array.length) == null || (values2 == null ? void 0 : values2.length) == null) {
    return array;
  }
  if (array === values2) {
    values2 = copyArray(values2);
  }
  let resultLength = 0;
  if (comparator == null) {
    comparator = (a, b) => eq(a, b);
  }
  const valuesArray = Array.isArray(values2) ? values2 : Array.from(values2);
  const hasUndefined = valuesArray.includes(void 0);
  for (let i = 0; i < array.length; i++) {
    if (i in array) {
      const shouldRemove = valuesArray.some((value) => comparator(array[i], value));
      if (!shouldRemove) {
        array[resultLength++] = array[i];
      }
      continue;
    }
    if (!hasUndefined) {
      delete array[resultLength++];
    }
  }
  array.length = resultLength;
  return array;
}

// node_modules/es-toolkit/dist/compat/object/at.mjs
function at(object, ...paths) {
  if (paths.length === 0) {
    return [];
  }
  const allPaths = [];
  for (let i = 0; i < paths.length; i++) {
    const path = paths[i];
    if (!isArrayLike(path) || isString(path)) {
      allPaths.push(path);
      continue;
    }
    for (let j = 0; j < path.length; j++) {
      allPaths.push(path[j]);
    }
  }
  const result2 = [];
  for (let i = 0; i < allPaths.length; i++) {
    result2.push(get(object, allPaths[i]));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/unset.mjs
function unset(obj, path) {
  if (obj == null) {
    return true;
  }
  switch (typeof path) {
    case "symbol":
    case "number":
    case "object": {
      if (Array.isArray(path)) {
        return unsetWithPath(obj, path);
      }
      if (typeof path === "number") {
        path = toKey(path);
      } else if (typeof path === "object") {
        if (Object.is(path == null ? void 0 : path.valueOf(), -0)) {
          path = "-0";
        } else {
          path = String(path);
        }
      }
      if ((obj == null ? void 0 : obj[path]) === void 0) {
        return true;
      }
      try {
        delete obj[path];
        return true;
      } catch {
        return false;
      }
    }
    case "string": {
      if ((obj == null ? void 0 : obj[path]) === void 0 && isDeepKey(path)) {
        return unsetWithPath(obj, toPath(path));
      }
      try {
        delete obj[path];
        return true;
      } catch {
        return false;
      }
    }
  }
}
function unsetWithPath(obj, path) {
  const parent = get(obj, path.slice(0, -1), obj);
  const lastKey = path[path.length - 1];
  if ((parent == null ? void 0 : parent[lastKey]) === void 0) {
    return true;
  }
  try {
    delete parent[lastKey];
    return true;
  } catch {
    return false;
  }
}

// node_modules/es-toolkit/dist/compat/array/pullAt.mjs
function pullAt(array, ..._indices) {
  const indices = flatten2(_indices, 1);
  if (!array) {
    return Array(indices.length);
  }
  const result2 = at(array, indices);
  const indicesToPull = indices.map((index) => isIndex(index, array.length) ? Number(index) : index).sort((a, b) => b - a);
  for (const index of new Set(indicesToPull)) {
    if (isIndex(index, array.length)) {
      Array.prototype.splice.call(array, index, 1);
      continue;
    }
    if (isKey(index, array)) {
      delete array[toKey(index)];
      continue;
    }
    const path = isArray(index) ? index : toPath(index);
    unset(array, path);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/reduceRight.mjs
function reduceRight(collection, iteratee2 = identity, accumulator) {
  if (!collection) {
    return accumulator;
  }
  let keys2;
  let startIndex;
  if (isArrayLike(collection)) {
    keys2 = range(0, collection.length).reverse();
    if (accumulator == null && collection.length > 0) {
      accumulator = collection[collection.length - 1];
      startIndex = 1;
    } else {
      startIndex = 0;
    }
  } else {
    keys2 = Object.keys(collection).reverse();
    if (accumulator == null) {
      accumulator = collection[keys2[0]];
      startIndex = 1;
    } else {
      startIndex = 0;
    }
  }
  for (let i = startIndex; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    accumulator = iteratee2(accumulator, value, key, collection);
  }
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/function/negate.mjs
function negate2(func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return function(...args) {
    return !func.apply(this, args);
  };
}

// node_modules/es-toolkit/dist/compat/array/reject.mjs
function reject(source, predicate) {
  return filter(source, negate2(iteratee(predicate)));
}

// node_modules/es-toolkit/dist/compat/array/remove.mjs
function remove2(arr, shouldRemoveElement) {
  return remove(arr, iteratee(shouldRemoveElement));
}

// node_modules/es-toolkit/dist/compat/array/reverse.mjs
function reverse(array) {
  if (array == null) {
    return array;
  }
  return array.reverse();
}

// node_modules/es-toolkit/dist/compat/array/sample.mjs
function sample2(collection) {
  if (collection == null) {
    return void 0;
  }
  if (isArrayLike(collection)) {
    return sample(toArray(collection));
  }
  return sample(Object.values(collection));
}

// node_modules/es-toolkit/dist/compat/math/clamp.mjs
function clamp2(value, bound1, bound2) {
  if (Number.isNaN(bound1)) {
    bound1 = 0;
  }
  if (Number.isNaN(bound2)) {
    bound2 = 0;
  }
  return clamp(value, bound1, bound2);
}

// node_modules/es-toolkit/dist/compat/predicate/isMap.mjs
function isMap2(value) {
  return isMap(value);
}

// node_modules/es-toolkit/dist/compat/util/toArray.mjs
function toArray2(value) {
  if (value == null) {
    return [];
  }
  if (isArrayLike(value) || isMap2(value)) {
    return Array.from(value);
  }
  if (typeof value === "object") {
    return Object.values(value);
  }
  return [];
}

// node_modules/es-toolkit/dist/compat/array/sampleSize.mjs
function sampleSize2(collection, size2, guard) {
  const arrayCollection = toArray2(collection);
  if (guard ? isIterateeCall(collection, size2, guard) : size2 === void 0) {
    size2 = 1;
  } else {
    size2 = clamp2(toInteger(size2), 0, arrayCollection.length);
  }
  return sampleSize(arrayCollection, size2);
}

// node_modules/es-toolkit/dist/compat/object/values.mjs
function values(object) {
  return Object.values(object);
}

// node_modules/es-toolkit/dist/compat/predicate/isNil.mjs
function isNil2(x2) {
  return x2 == null;
}

// node_modules/es-toolkit/dist/compat/array/shuffle.mjs
function shuffle2(collection) {
  if (isNil2(collection)) {
    return [];
  }
  if (isArray(collection)) {
    return shuffle(collection);
  }
  if (isArrayLike(collection)) {
    return shuffle(Array.from(collection));
  }
  if (isObjectLike(collection)) {
    return shuffle(values(collection));
  }
  return [];
}

// node_modules/es-toolkit/dist/compat/array/size.mjs
function size(target) {
  if (isNil(target)) {
    return 0;
  }
  if (target instanceof Map || target instanceof Set) {
    return target.size;
  }
  return Object.keys(target).length;
}

// node_modules/es-toolkit/dist/compat/array/slice.mjs
function slice(array, start, end) {
  if (!isArrayLike(array)) {
    return [];
  }
  const length = array.length;
  if (end === void 0) {
    end = length;
  } else if (typeof end !== "number" && isIterateeCall(array, start, end)) {
    start = 0;
    end = length;
  }
  start = toInteger(start);
  end = toInteger(end);
  if (start < 0) {
    start = Math.max(length + start, 0);
  } else {
    start = Math.min(start, length);
  }
  if (end < 0) {
    end = Math.max(length + end, 0);
  } else {
    end = Math.min(end, length);
  }
  const resultLength = Math.max(end - start, 0);
  const result2 = new Array(resultLength);
  for (let i = 0; i < resultLength; ++i) {
    result2[i] = array[start + i];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/some.mjs
function some(source, predicate, guard) {
  if (!source) {
    return false;
  }
  if (guard != null) {
    predicate = void 0;
  }
  if (!predicate) {
    predicate = identity;
  }
  const values2 = Array.isArray(source) ? source : Object.values(source);
  switch (typeof predicate) {
    case "function": {
      if (!Array.isArray(source)) {
        const keys2 = Object.keys(source);
        for (let i = 0; i < keys2.length; i++) {
          const key = keys2[i];
          const value = source[key];
          if (predicate(value, key, source)) {
            return true;
          }
        }
        return false;
      }
      for (let i = 0; i < source.length; i++) {
        if (predicate(source[i], i, source)) {
          return true;
        }
      }
      return false;
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        const matchFunc = matchesProperty(key, value);
        if (Array.isArray(source)) {
          for (let i = 0; i < source.length; i++) {
            if (matchFunc(source[i])) {
              return true;
            }
          }
          return false;
        }
        return values2.some(matchFunc);
      } else {
        const matchFunc = matches(predicate);
        if (Array.isArray(source)) {
          for (let i = 0; i < source.length; i++) {
            if (matchFunc(source[i])) {
              return true;
            }
          }
          return false;
        }
        return values2.some(matchFunc);
      }
    }
    case "number":
    case "symbol":
    case "string": {
      const propFunc = property(predicate);
      if (Array.isArray(source)) {
        for (let i = 0; i < source.length; i++) {
          if (propFunc(source[i])) {
            return true;
          }
        }
        return false;
      }
      return values2.some(propFunc);
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/sortBy.mjs
function sortBy(collection, ...criteria) {
  const length = criteria.length;
  if (length > 1 && isIterateeCall(collection, criteria[0], criteria[1])) {
    criteria = [];
  } else if (length > 2 && isIterateeCall(criteria[0], criteria[1], criteria[2])) {
    criteria = [criteria[0]];
  }
  return orderBy(collection, flatten(criteria), ["asc"]);
}

// node_modules/es-toolkit/dist/compat/predicate/isNaN.mjs
function isNaN(value) {
  return Number.isNaN(value);
}

// node_modules/es-toolkit/dist/compat/array/sortedIndexBy.mjs
var MAX_ARRAY_LENGTH = 4294967295;
var MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1;
function sortedIndexBy(array, value, iteratee$1, retHighest) {
  let low = 0;
  let high = array == null ? 0 : array.length;
  if (high === 0 || isNil2(array)) {
    return 0;
  }
  const iterateeFunction = iteratee(iteratee$1);
  const transformedValue = iterateeFunction(value);
  const valIsNaN = isNaN(transformedValue);
  const valIsNull = isNull(transformedValue);
  const valIsSymbol = isSymbol(transformedValue);
  const valIsUndefined = isUndefined(transformedValue);
  while (low < high) {
    let setLow;
    const mid = Math.floor((low + high) / 2);
    const computed2 = iterateeFunction(array[mid]);
    const othIsDefined = !isUndefined(computed2);
    const othIsNull = isNull(computed2);
    const othIsReflexive = !isNaN(computed2);
    const othIsSymbol = isSymbol(computed2);
    if (valIsNaN) {
      setLow = retHighest || othIsReflexive;
    } else if (valIsUndefined) {
      setLow = othIsReflexive && (retHighest || othIsDefined);
    } else if (valIsNull) {
      setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);
    } else if (valIsSymbol) {
      setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);
    } else if (othIsNull || othIsSymbol) {
      setLow = false;
    } else {
      setLow = retHighest ? computed2 <= transformedValue : computed2 < transformedValue;
    }
    if (setLow) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return Math.min(high, MAX_ARRAY_INDEX);
}

// node_modules/es-toolkit/dist/compat/predicate/isNumber.mjs
function isNumber(value) {
  return typeof value === "number" || value instanceof Number;
}

// node_modules/es-toolkit/dist/compat/array/sortedIndex.mjs
var MAX_ARRAY_LENGTH2 = 4294967295;
var HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH2 >>> 1;
function sortedIndex(array, value) {
  if (isNil(array)) {
    return 0;
  }
  let low = 0, high = isNil(array) ? low : array.length;
  if (isNumber(value) && value === value && high <= HALF_MAX_ARRAY_LENGTH) {
    while (low < high) {
      const mid = low + high >>> 1;
      const compute = array[mid];
      if (!isNull(compute) && !isSymbol2(compute) && compute < value) {
        low = mid + 1;
      } else {
        high = mid;
      }
    }
    return high;
  }
  return sortedIndexBy(array, value, (value2) => value2);
}

// node_modules/es-toolkit/dist/compat/array/sortedIndexOf.mjs
function sortedIndexOf(array, value) {
  if (!(array == null ? void 0 : array.length)) {
    return -1;
  }
  const index = sortedIndex(array, value);
  if (index < array.length && eq(array[index], value)) {
    return index;
  }
  return -1;
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndexBy.mjs
function sortedLastIndexBy(array, value, iteratee2) {
  return sortedIndexBy(array, value, iteratee2, true);
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndex.mjs
var MAX_ARRAY_LENGTH3 = 4294967295;
var HALF_MAX_ARRAY_LENGTH2 = MAX_ARRAY_LENGTH3 >>> 1;
function sortedLastIndex(array, value) {
  if (isNil(array)) {
    return 0;
  }
  let high = array.length;
  if (!isNumber(value) || Number.isNaN(value) || high > HALF_MAX_ARRAY_LENGTH2) {
    return sortedLastIndexBy(array, value, (value2) => value2);
  }
  let low = 0;
  while (low < high) {
    const mid = low + high >>> 1;
    const compute = array[mid];
    if (!isNull(compute) && !isSymbol2(compute) && compute <= value) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return high;
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndexOf.mjs
function sortedLastIndexOf(array, value) {
  if (!(array == null ? void 0 : array.length)) {
    return -1;
  }
  const index = sortedLastIndex(array, value) - 1;
  if (index >= 0 && eq(array[index], value)) {
    return index;
  }
  return -1;
}

// node_modules/es-toolkit/dist/compat/array/tail.mjs
function tail2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return tail(toArray(arr));
}

// node_modules/es-toolkit/dist/compat/array/take.mjs
function take2(arr, count = 1, guard) {
  count = guard ? 1 : toInteger(count);
  if (count < 1 || !isArrayLike(arr)) {
    return [];
  }
  return take(toArray(arr), count);
}

// node_modules/es-toolkit/dist/compat/array/takeRight.mjs
function takeRight2(arr, count = 1, guard) {
  count = guard ? 1 : toInteger(count);
  if (count <= 0 || !isArrayLike(arr)) {
    return [];
  }
  return takeRight(toArray(arr), count);
}

// node_modules/es-toolkit/dist/compat/array/takeRightWhile.mjs
function takeRightWhile(_array, predicate) {
  if (!isArrayLikeObject(_array)) {
    return [];
  }
  const array = toArray(_array);
  const index = array.findLastIndex(negate(iteratee(predicate)));
  return array.slice(index + 1);
}

// node_modules/es-toolkit/dist/compat/array/takeWhile.mjs
function takeWhile(array, predicate) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const _array = toArray(array);
  const index = _array.findIndex(negate2(iteratee(predicate)));
  return index === -1 ? _array : _array.slice(0, index);
}

// node_modules/es-toolkit/dist/compat/array/union.mjs
function union(...arrays) {
  const validArrays = arrays.filter(isArrayLikeObject);
  const flattened = flatten2(validArrays, 1);
  return uniq(flattened);
}

// node_modules/es-toolkit/dist/compat/array/unionBy.mjs
function unionBy(...values2) {
  const lastValue = last(values2);
  const flattened = flattenArrayLike(values2);
  if (isArrayLikeObject(lastValue) || lastValue == null) {
    return uniq(flattened);
  }
  return uniqBy(flattened, iteratee(lastValue));
}

// node_modules/es-toolkit/dist/compat/array/unionWith.mjs
function unionWith(...values2) {
  const lastValue = last(values2);
  const flattened = flattenArrayLike(values2);
  if (isArrayLikeObject(lastValue) || lastValue == null) {
    return uniq(flattened);
  }
  return uniqWith(flattened, lastValue);
}

// node_modules/es-toolkit/dist/compat/array/uniqBy.mjs
function uniqBy2(array, iteratee$1) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  return uniqBy(Array.from(array), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/array/uniqWith.mjs
function uniqWith2(arr, comparator) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return typeof comparator === "function" ? uniqWith(Array.from(arr), comparator) : uniq2(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/unzip.mjs
function unzip2(array) {
  if (!isArrayLikeObject(array) || !array.length) {
    return [];
  }
  array = isArray(array) ? array : Array.from(array);
  array = array.filter((item) => isArrayLikeObject(item));
  return unzip(array);
}

// node_modules/es-toolkit/dist/compat/array/unzipWith.mjs
function unzipWith(array, iteratee2) {
  if (!isArrayLikeObject(array) || !array.length) {
    return [];
  }
  const unziped = isArray(array) ? unzip(array) : unzip(Array.from(array, (value) => Array.from(value)));
  if (!iteratee2) {
    return unziped;
  }
  const result2 = new Array(unziped.length);
  for (let i = 0; i < unziped.length; i++) {
    const value = unziped[i];
    result2[i] = iteratee2(...value);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/without.mjs
function without2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  return without(Array.from(array), ...values2);
}

// node_modules/es-toolkit/dist/compat/array/xor.mjs
function xor(...arrays) {
  const itemCounts = /* @__PURE__ */ new Map();
  for (let i = 0; i < arrays.length; i++) {
    const array = arrays[i];
    if (!isArrayLikeObject(array)) {
      continue;
    }
    const itemSet = new Set(toArray2(array));
    for (const item of itemSet) {
      if (!itemCounts.has(item)) {
        itemCounts.set(item, 1);
      } else {
        itemCounts.set(item, itemCounts.get(item) + 1);
      }
    }
  }
  const result2 = [];
  for (const [item, count] of itemCounts) {
    if (count === 1) {
      result2.push(item);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/xorBy.mjs
function xorBy(...values2) {
  const lastValue = last2(values2);
  let mapper = identity;
  if (!isArrayLikeObject(lastValue) && lastValue != null) {
    mapper = iteratee(lastValue);
    values2 = values2.slice(0, -1);
  }
  const arrays = values2.filter(isArrayLikeObject);
  const union2 = unionBy(...arrays, mapper);
  const intersections = windowed(arrays, 2).map(([arr1, arr2]) => intersectionBy2(arr1, arr2, mapper));
  return differenceBy2(union2, unionBy(...intersections, mapper), mapper);
}

// node_modules/es-toolkit/dist/compat/array/xorWith.mjs
function xorWith(...values2) {
  const lastValue = last2(values2);
  let comparator = (a, b) => a === b;
  if (typeof lastValue === "function") {
    comparator = lastValue;
    values2 = values2.slice(0, -1);
  }
  const arrays = values2.filter(isArrayLikeObject);
  const union2 = unionWith(...arrays, comparator);
  const intersections = windowed(arrays, 2).map(([arr1, arr2]) => intersectionWith2(arr1, arr2, comparator));
  return differenceWith2(union2, unionWith(...intersections, comparator), comparator);
}

// node_modules/es-toolkit/dist/compat/array/zip.mjs
function zip2(...arrays) {
  if (!arrays.length) {
    return [];
  }
  return zip(...arrays.filter((group) => isArrayLikeObject(group)));
}

// node_modules/es-toolkit/dist/compat/_internal/assignValue.mjs
var assignValue = (object, key, value) => {
  const objValue = object[key];
  if (!(Object.hasOwn(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) {
    object[key] = value;
  }
};

// node_modules/es-toolkit/dist/compat/array/zipObject.mjs
function zipObject(keys2 = [], values2 = []) {
  const result2 = {};
  for (let i = 0; i < keys2.length; i++) {
    assignValue(result2, keys2[i], values2[i]);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/updateWith.mjs
function updateWith(obj, path, updater, customizer) {
  if (obj == null && !isObject(obj)) {
    return obj;
  }
  const resolvedPath = isKey(path, obj) ? [path] : Array.isArray(path) ? path : typeof path === "string" ? toPath(path) : [path];
  let current = obj;
  for (let i = 0; i < resolvedPath.length && current != null; i++) {
    const key = toKey(resolvedPath[i]);
    let newValue;
    if (i === resolvedPath.length - 1) {
      newValue = updater(current[key]);
    } else {
      const objValue = current[key];
      const customizerResult = customizer(objValue);
      newValue = customizerResult !== void 0 ? customizerResult : isObject(objValue) ? objValue : isIndex(resolvedPath[i + 1]) ? [] : {};
    }
    assignValue(current, key, newValue);
    current = current[key];
  }
  return obj;
}

// node_modules/es-toolkit/dist/compat/object/set.mjs
function set(obj, path, value) {
  return updateWith(obj, path, () => value, () => void 0);
}

// node_modules/es-toolkit/dist/compat/array/zipObjectDeep.mjs
function zipObjectDeep(keys2, values2) {
  const result2 = {};
  if (!isArrayLike(keys2)) {
    return result2;
  }
  if (!isArrayLike(values2)) {
    values2 = [];
  }
  const zipped = zip(Array.from(keys2), Array.from(values2));
  for (let i = 0; i < zipped.length; i++) {
    const [key, value] = zipped[i];
    if (key != null) {
      set(result2, key, value);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/zipWith.mjs
function zipWith(...combine) {
  let iteratee2 = combine.pop();
  if (!isFunction(iteratee2)) {
    combine.push(iteratee2);
    iteratee2 = void 0;
  }
  if (!(combine == null ? void 0 : combine.length)) {
    return [];
  }
  const result2 = unzip2(combine);
  if (iteratee2 == null) {
    return result2;
  }
  return result2.map((group) => iteratee2(...group));
}

// node_modules/es-toolkit/dist/compat/function/after.mjs
function after2(n, func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  n = toInteger(n);
  return function(...args) {
    if (--n < 1) {
      return func.apply(this, args);
    }
  };
}

// node_modules/es-toolkit/dist/compat/function/ary.mjs
function ary2(func, n = func.length, guard) {
  if (guard) {
    n = func.length;
  }
  if (Number.isNaN(n) || n < 0) {
    n = 0;
  }
  return ary(func, n);
}

// node_modules/es-toolkit/dist/compat/function/attempt.mjs
function attempt(func, ...args) {
  try {
    return func(...args);
  } catch (e) {
    return e instanceof Error ? e : new Error(e);
  }
}

// node_modules/es-toolkit/dist/compat/function/before.mjs
function before(n, func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  let result2;
  n = toInteger(n);
  return function(...args) {
    if (--n > 0) {
      result2 = func.apply(this, args);
    }
    if (n <= 1 && func) {
      func = void 0;
    }
    return result2;
  };
}

// node_modules/es-toolkit/dist/compat/function/bind.mjs
function bind(func, thisObj, ...partialArgs) {
  const bound = function(...providedArgs) {
    const args = [];
    let startIndex = 0;
    for (let i = 0; i < partialArgs.length; i++) {
      const arg = partialArgs[i];
      if (arg === bind.placeholder) {
        args.push(providedArgs[startIndex++]);
      } else {
        args.push(arg);
      }
    }
    for (let i = startIndex; i < providedArgs.length; i++) {
      args.push(providedArgs[i]);
    }
    if (this instanceof bound) {
      return new func(...args);
    }
    return func.apply(thisObj, args);
  };
  return bound;
}
var bindPlaceholder = Symbol("bind.placeholder");
bind.placeholder = bindPlaceholder;

// node_modules/es-toolkit/dist/compat/function/bindKey.mjs
function bindKey(object, key, ...partialArgs) {
  const bound = function(...providedArgs) {
    const args = [];
    let startIndex = 0;
    for (let i = 0; i < partialArgs.length; i++) {
      const arg = partialArgs[i];
      if (arg === bindKey.placeholder) {
        args.push(providedArgs[startIndex++]);
      } else {
        args.push(arg);
      }
    }
    for (let i = startIndex; i < providedArgs.length; i++) {
      args.push(providedArgs[i]);
    }
    if (this instanceof bound) {
      return new object[key](...args);
    }
    return object[key].apply(object, args);
  };
  return bound;
}
var bindKeyPlaceholder = Symbol("bindKey.placeholder");
bindKey.placeholder = bindKeyPlaceholder;

// node_modules/es-toolkit/dist/compat/function/curry.mjs
function curry(func, arity = func.length, guard) {
  arity = guard ? func.length : arity;
  arity = Number.parseInt(arity, 10);
  if (Number.isNaN(arity) || arity < 1) {
    arity = 0;
  }
  const wrapper = function(...partialArgs) {
    const holders = partialArgs.filter((item) => item === curry.placeholder);
    const length = partialArgs.length - holders.length;
    if (length < arity) {
      return makeCurry(func, arity - length, partialArgs);
    }
    if (this instanceof wrapper) {
      return new func(...partialArgs);
    }
    return func.apply(this, partialArgs);
  };
  wrapper.placeholder = curryPlaceholder;
  return wrapper;
}
function makeCurry(func, arity, partialArgs) {
  function wrapper(...providedArgs) {
    const holders = providedArgs.filter((item) => item === curry.placeholder);
    const length = providedArgs.length - holders.length;
    providedArgs = composeArgs(providedArgs, partialArgs);
    if (length < arity) {
      return makeCurry(func, arity - length, providedArgs);
    }
    if (this instanceof wrapper) {
      return new func(...providedArgs);
    }
    return func.apply(this, providedArgs);
  }
  wrapper.placeholder = curryPlaceholder;
  return wrapper;
}
function composeArgs(providedArgs, partialArgs) {
  const args = [];
  let startIndex = 0;
  for (let i = 0; i < partialArgs.length; i++) {
    const arg = partialArgs[i];
    if (arg === curry.placeholder && startIndex < providedArgs.length) {
      args.push(providedArgs[startIndex++]);
    } else {
      args.push(arg);
    }
  }
  for (let i = startIndex; i < providedArgs.length; i++) {
    args.push(providedArgs[i]);
  }
  return args;
}
var curryPlaceholder = Symbol("curry.placeholder");
curry.placeholder = curryPlaceholder;

// node_modules/es-toolkit/dist/compat/function/curryRight.mjs
function curryRight(func, arity = func.length, guard) {
  arity = guard ? func.length : arity;
  arity = Number.parseInt(arity, 10);
  if (Number.isNaN(arity) || arity < 1) {
    arity = 0;
  }
  const wrapper = function(...partialArgs) {
    const holders = partialArgs.filter((item) => item === curryRight.placeholder);
    const length = partialArgs.length - holders.length;
    if (length < arity) {
      return makeCurryRight(func, arity - length, partialArgs);
    }
    if (this instanceof wrapper) {
      return new func(...partialArgs);
    }
    return func.apply(this, partialArgs);
  };
  wrapper.placeholder = curryRightPlaceholder;
  return wrapper;
}
function makeCurryRight(func, arity, partialArgs) {
  function wrapper(...providedArgs) {
    const holders = providedArgs.filter((item) => item === curryRight.placeholder);
    const length = providedArgs.length - holders.length;
    providedArgs = composeArgs2(providedArgs, partialArgs);
    if (length < arity) {
      return makeCurryRight(func, arity - length, providedArgs);
    }
    if (this instanceof wrapper) {
      return new func(...providedArgs);
    }
    return func.apply(this, providedArgs);
  }
  wrapper.placeholder = curryRightPlaceholder;
  return wrapper;
}
function composeArgs2(providedArgs, partialArgs) {
  const placeholderLength = partialArgs.filter((arg) => arg === curryRight.placeholder).length;
  const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);
  const args = [];
  let providedIndex = 0;
  for (let i = 0; i < rangeLength; i++) {
    args.push(providedArgs[providedIndex++]);
  }
  for (let i = 0; i < partialArgs.length; i++) {
    const arg = partialArgs[i];
    if (arg === curryRight.placeholder) {
      if (providedIndex < providedArgs.length) {
        args.push(providedArgs[providedIndex++]);
      } else {
        args.push(arg);
      }
    } else {
      args.push(arg);
    }
  }
  return args;
}
var curryRightPlaceholder = Symbol("curryRight.placeholder");
curryRight.placeholder = curryRightPlaceholder;

// node_modules/es-toolkit/dist/compat/function/debounce.mjs
function debounce2(func, debounceMs = 0, options = {}) {
  if (typeof options !== "object") {
    options = {};
  }
  const { signal, leading = false, trailing = true, maxWait } = options;
  const edges = Array(2);
  if (leading) {
    edges[0] = "leading";
  }
  if (trailing) {
    edges[1] = "trailing";
  }
  let result2 = void 0;
  let pendingAt = null;
  const _debounced = debounce(function(...args) {
    result2 = func.apply(this, args);
    pendingAt = null;
  }, debounceMs, { signal, edges });
  const debounced = function(...args) {
    if (maxWait != null) {
      if (pendingAt === null) {
        pendingAt = Date.now();
      }
      if (Date.now() - pendingAt >= maxWait) {
        result2 = func.apply(this, args);
        pendingAt = Date.now();
        _debounced.cancel();
        _debounced.schedule();
        return result2;
      }
    }
    _debounced.apply(this, args);
    return result2;
  };
  const flush = () => {
    _debounced.flush();
    return result2;
  };
  debounced.cancel = _debounced.cancel;
  debounced.flush = flush;
  return debounced;
}

// node_modules/es-toolkit/dist/compat/function/defer.mjs
function defer(func, ...args) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return setTimeout(func, 1, ...args);
}

// node_modules/es-toolkit/dist/compat/function/delay.mjs
function delay(func, wait, ...args) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return setTimeout(func, toNumber(wait) || 0, ...args);
}

// node_modules/es-toolkit/dist/compat/function/flip.mjs
function flip(func) {
  return function(...args) {
    return func.apply(this, args.reverse());
  };
}

// node_modules/es-toolkit/dist/compat/function/flow.mjs
function flow2(...funcs) {
  const flattenFuncs = flatten(funcs, 1);
  if (flattenFuncs.some((func) => typeof func !== "function")) {
    throw new TypeError("Expected a function");
  }
  return flow(...flattenFuncs);
}

// node_modules/es-toolkit/dist/compat/function/flowRight.mjs
function flowRight2(...funcs) {
  const flattenFuncs = flatten(funcs, 1);
  if (flattenFuncs.some((func) => typeof func !== "function")) {
    throw new TypeError("Expected a function");
  }
  return flowRight(...flattenFuncs);
}

// node_modules/es-toolkit/dist/compat/function/memoize.mjs
function memoize(func, resolver) {
  if (typeof func !== "function" || resolver != null && typeof resolver !== "function") {
    throw new TypeError("Expected a function");
  }
  const memoized = function(...args) {
    const key = resolver ? resolver.apply(this, args) : args[0];
    const cache = memoized.cache;
    if (cache.has(key)) {
      return cache.get(key);
    }
    const result2 = func.apply(this, args);
    memoized.cache = cache.set(key, result2) || cache;
    return result2;
  };
  const CacheConstructor = memoize.Cache || Map;
  memoized.cache = new CacheConstructor();
  return memoized;
}
memoize.Cache = Map;

// node_modules/es-toolkit/dist/compat/function/nthArg.mjs
function nthArg(n = 0) {
  return function(...args) {
    return args.at(toInteger(n));
  };
}

// node_modules/es-toolkit/dist/compat/function/overArgs.mjs
function overArgs(func, _transforms) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  const transforms = Array.isArray(_transforms) ? _transforms : [_transforms];
  return function(...args) {
    const length = Math.min(args.length, transforms.length);
    const transformedArgs = [...args];
    for (let i = 0; i < length; i++) {
      const transform2 = iteratee(transforms[i] ?? identity);
      transformedArgs[i] = transform2.call(this, args[i]);
    }
    return func.apply(this, transformedArgs);
  };
}

// node_modules/es-toolkit/dist/compat/function/partial.mjs
function partial(func, ...partialArgs) {
  return partialImpl(func, partial.placeholder, ...partialArgs);
}
partial.placeholder = Symbol("compat.partial.placeholder");

// node_modules/es-toolkit/dist/compat/function/partialRight.mjs
function partialRight(func, ...partialArgs) {
  return partialRightImpl(func, partialRight.placeholder, ...partialArgs);
}
partialRight.placeholder = Symbol("compat.partialRight.placeholder");

// node_modules/es-toolkit/dist/compat/function/rearg.mjs
function rearg(func, ...indices) {
  const flattenIndices = flatten2(indices);
  return function(...args) {
    const reorderedArgs = flattenIndices.map((i) => args[i]).slice(0, args.length);
    for (let i = reorderedArgs.length; i < args.length; i++) {
      reorderedArgs.push(args[i]);
    }
    return func.apply(this, reorderedArgs);
  };
}

// node_modules/es-toolkit/dist/compat/function/rest.mjs
function rest2(func, start = func.length - 1) {
  start = Number.parseInt(start, 10);
  if (Number.isNaN(start) || start < 0) {
    start = func.length - 1;
  }
  return rest(func, start);
}

// node_modules/es-toolkit/dist/compat/function/spread.mjs
function spread(func, argsIndex = 0) {
  argsIndex = Number.parseInt(argsIndex, 10);
  if (Number.isNaN(argsIndex) || argsIndex < 0) {
    argsIndex = 0;
  }
  return function(...args) {
    const array = args[argsIndex];
    const params = args.slice(0, argsIndex);
    if (array) {
      params.push(...array);
    }
    return func.apply(this, params);
  };
}

// node_modules/es-toolkit/dist/compat/function/throttle.mjs
function throttle(func, throttleMs = 0, options = {}) {
  if (typeof options !== "object") {
    options = {};
  }
  const { leading = true, trailing = true, signal } = options;
  return debounce2(func, throttleMs, {
    leading,
    trailing,
    signal,
    maxWait: throttleMs
  });
}

// node_modules/es-toolkit/dist/compat/function/wrap.mjs
function wrap(value, wrapper) {
  return function(...args) {
    const wrapFn = isFunction(wrapper) ? wrapper : identity;
    return wrapFn.apply(this, [value, ...args]);
  };
}

// node_modules/es-toolkit/dist/compat/util/toString.mjs
function toString(value) {
  if (value == null) {
    return "";
  }
  if (typeof value === "string") {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(toString).join(",");
  }
  const result2 = String(value);
  if (result2 === "0" && Object.is(Number(value), -0)) {
    return "-0";
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/add.mjs
function add(value, other) {
  if (value === void 0 && other === void 0) {
    return 0;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value + other;
}

// node_modules/es-toolkit/dist/compat/_internal/decimalAdjust.mjs
function decimalAdjust(type, number, precision = 0) {
  number = Number(number);
  if (Object.is(number, -0)) {
    number = "-0";
  }
  precision = Math.min(Number.parseInt(precision, 10), 292);
  if (precision) {
    const [magnitude, exponent = 0] = number.toString().split("e");
    let adjustedValue = Math[type](Number(`${magnitude}e${Number(exponent) + precision}`));
    if (Object.is(adjustedValue, -0)) {
      adjustedValue = "-0";
    }
    const [newMagnitude, newExponent = 0] = adjustedValue.toString().split("e");
    return Number(`${newMagnitude}e${Number(newExponent) - precision}`);
  }
  return Math[type](Number(number));
}

// node_modules/es-toolkit/dist/compat/math/ceil.mjs
function ceil(number, precision = 0) {
  return decimalAdjust("ceil", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/divide.mjs
function divide(value, other) {
  if (value === void 0 && other === void 0) {
    return 1;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value / other;
}

// node_modules/es-toolkit/dist/compat/math/floor.mjs
function floor(number, precision = 0) {
  return decimalAdjust("floor", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/inRange.mjs
function inRange2(value, minimum, maximum) {
  if (!minimum) {
    minimum = 0;
  }
  if (maximum != null && !maximum) {
    maximum = 0;
  }
  if (minimum != null && typeof minimum !== "number") {
    minimum = Number(minimum);
  }
  if (maximum == null && minimum === 0) {
    return false;
  }
  if (maximum != null && typeof maximum !== "number") {
    maximum = Number(maximum);
  }
  if (maximum != null && minimum > maximum) {
    [minimum, maximum] = [maximum, minimum];
  }
  if (minimum === maximum) {
    return false;
  }
  return inRange(value, minimum, maximum);
}

// node_modules/es-toolkit/dist/compat/math/max.mjs
function max(items) {
  if (!items || items.length === 0) {
    return void 0;
  }
  let maxResult = void 0;
  for (let i = 0; i < items.length; i++) {
    const current = items[i];
    if (current == null || Number.isNaN(current) || typeof current === "symbol") {
      continue;
    }
    if (maxResult === void 0 || current > maxResult) {
      maxResult = current;
    }
  }
  return maxResult;
}

// node_modules/es-toolkit/dist/compat/math/maxBy.mjs
function maxBy2(items, iteratee$1) {
  if (items == null) {
    return void 0;
  }
  return maxBy(Array.from(items), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/math/sumBy.mjs
function sumBy(array, iteratee$1) {
  if (!array || !array.length) {
    return 0;
  }
  if (iteratee$1 != null) {
    iteratee$1 = iteratee(iteratee$1);
  }
  let result2 = void 0;
  for (let i = 0; i < array.length; i++) {
    const current = iteratee$1 ? iteratee$1(array[i]) : array[i];
    if (current !== void 0) {
      if (result2 === void 0) {
        result2 = current;
      } else {
        result2 += current;
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/sum.mjs
function sum(array) {
  return sumBy(array);
}

// node_modules/es-toolkit/dist/compat/math/mean.mjs
function mean(nums) {
  const length = nums ? nums.length : 0;
  return length === 0 ? NaN : sum(nums) / length;
}

// node_modules/es-toolkit/dist/compat/math/meanBy.mjs
function meanBy2(items, iteratee$1) {
  if (items == null) {
    return NaN;
  }
  return meanBy(Array.from(items), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/math/min.mjs
function min(items) {
  if (!items || items.length === 0) {
    return void 0;
  }
  let minResult = void 0;
  for (let i = 0; i < items.length; i++) {
    const current = items[i];
    if (current == null || Number.isNaN(current) || typeof current === "symbol") {
      continue;
    }
    if (minResult === void 0 || current < minResult) {
      minResult = current;
    }
  }
  return minResult;
}

// node_modules/es-toolkit/dist/compat/math/minBy.mjs
function minBy2(items, iteratee$1) {
  if (items == null) {
    return void 0;
  }
  return minBy(Array.from(items), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/math/multiply.mjs
function multiply(value, other) {
  if (value === void 0 && other === void 0) {
    return 1;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value * other;
}

// node_modules/es-toolkit/dist/compat/math/parseInt.mjs
function parseInt(string, radix = 0, guard) {
  if (guard) {
    radix = 0;
  }
  return Number.parseInt(string, radix);
}

// node_modules/es-toolkit/dist/compat/math/random.mjs
function random2(...args) {
  let minimum = 0;
  let maximum = 1;
  let floating = false;
  switch (args.length) {
    case 1: {
      if (typeof args[0] === "boolean") {
        floating = args[0];
      } else {
        maximum = args[0];
      }
      break;
    }
    case 2: {
      if (typeof args[1] === "boolean") {
        maximum = args[0];
        floating = args[1];
      } else {
        minimum = args[0];
        maximum = args[1];
      }
    }
    case 3: {
      if (typeof args[2] === "object" && args[2] != null && args[2][args[1]] === args[0]) {
        minimum = 0;
        maximum = args[0];
        floating = false;
      } else {
        minimum = args[0];
        maximum = args[1];
        floating = args[2];
      }
    }
  }
  if (typeof minimum !== "number") {
    minimum = Number(minimum);
  }
  if (typeof maximum !== "number") {
    minimum = Number(maximum);
  }
  if (!minimum) {
    minimum = 0;
  }
  if (!maximum) {
    maximum = 0;
  }
  if (minimum > maximum) {
    [minimum, maximum] = [maximum, minimum];
  }
  minimum = clamp2(minimum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
  maximum = clamp2(maximum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
  if (minimum === maximum) {
    return minimum;
  }
  if (floating) {
    return random(minimum, maximum + 1);
  } else {
    return randomInt(minimum, maximum + 1);
  }
}

// node_modules/es-toolkit/dist/compat/math/range.mjs
function range2(start, end, step) {
  if (step && typeof step !== "number" && isIterateeCall(start, end, step)) {
    end = step = void 0;
  }
  start = toFinite(start);
  if (end === void 0) {
    end = start;
    start = 0;
  } else {
    end = toFinite(end);
  }
  step = step === void 0 ? start < end ? 1 : -1 : toFinite(step);
  const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);
  const result2 = new Array(length);
  for (let index = 0; index < length; index++) {
    result2[index] = start;
    start += step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/rangeRight.mjs
function rangeRight(start, end, step) {
  if (step && typeof step !== "number" && isIterateeCall(start, end, step)) {
    end = step = void 0;
  }
  start = toFinite(start);
  if (end === void 0) {
    end = start;
    start = 0;
  } else {
    end = toFinite(end);
  }
  step = step === void 0 ? start < end ? 1 : -1 : toFinite(step);
  const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);
  const result2 = new Array(length);
  for (let index = length - 1; index >= 0; index--) {
    result2[index] = start;
    start += step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/round.mjs
function round(number, precision = 0) {
  return decimalAdjust("round", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/subtract.mjs
function subtract(value, other) {
  if (value === void 0 && other === void 0) {
    return 0;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value - other;
}

// node_modules/es-toolkit/dist/compat/_internal/isPrototype.mjs
function isPrototype(value) {
  const constructor = value == null ? void 0 : value.constructor;
  const prototype = typeof constructor === "function" ? constructor.prototype : Object.prototype;
  return value === prototype;
}

// node_modules/es-toolkit/dist/compat/predicate/isTypedArray.mjs
function isTypedArray2(x2) {
  return isTypedArray(x2);
}

// node_modules/es-toolkit/dist/compat/util/times.mjs
function times(n, getValue) {
  n = toInteger(n);
  if (n < 1 || !Number.isSafeInteger(n)) {
    return [];
  }
  const result2 = new Array(n);
  for (let i = 0; i < n; i++) {
    result2[i] = typeof getValue === "function" ? getValue(i) : i;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/keys.mjs
function keys(object) {
  if (isArrayLike(object)) {
    return arrayLikeKeys(object);
  }
  const result2 = Object.keys(Object(object));
  if (!isPrototype(object)) {
    return result2;
  }
  return result2.filter((key) => key !== "constructor");
}
function arrayLikeKeys(object) {
  const indices = times(object.length, (index) => `${index}`);
  const filteredKeys = new Set(indices);
  if (isBuffer(object)) {
    filteredKeys.add("offset");
    filteredKeys.add("parent");
  }
  if (isTypedArray2(object)) {
    filteredKeys.add("buffer");
    filteredKeys.add("byteLength");
    filteredKeys.add("byteOffset");
  }
  return [...indices, ...Object.keys(object).filter((key) => !filteredKeys.has(key))];
}

// node_modules/es-toolkit/dist/compat/object/assign.mjs
function assign(object, ...sources) {
  for (let i = 0; i < sources.length; i++) {
    assignImpl(object, sources[i]);
  }
  return object;
}
function assignImpl(object, source) {
  const keys$1 = keys(source);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    if (!(key in object) || !eq(object[key], source[key])) {
      object[key] = source[key];
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/keysIn.mjs
function keysIn(object) {
  if (object == null) {
    return [];
  }
  switch (typeof object) {
    case "object":
    case "function": {
      if (isArrayLike(object)) {
        return arrayLikeKeysIn(object);
      }
      if (isPrototype(object)) {
        return prototypeKeysIn(object);
      }
      return keysInImpl(object);
    }
    default: {
      return keysInImpl(Object(object));
    }
  }
}
function keysInImpl(object) {
  const result2 = [];
  for (const key in object) {
    result2.push(key);
  }
  return result2;
}
function prototypeKeysIn(object) {
  const keys2 = keysInImpl(object);
  return keys2.filter((key) => key !== "constructor");
}
function arrayLikeKeysIn(object) {
  const indices = times(object.length, (index) => `${index}`);
  const filteredKeys = new Set(indices);
  if (isBuffer(object)) {
    filteredKeys.add("offset");
    filteredKeys.add("parent");
  }
  if (isTypedArray2(object)) {
    filteredKeys.add("buffer");
    filteredKeys.add("byteLength");
    filteredKeys.add("byteOffset");
  }
  return [...indices, ...keysInImpl(object).filter((key) => !filteredKeys.has(key))];
}

// node_modules/es-toolkit/dist/compat/object/assignIn.mjs
function assignIn(object, ...sources) {
  for (let i = 0; i < sources.length; i++) {
    assignInImpl(object, sources[i]);
  }
  return object;
}
function assignInImpl(object, source) {
  const keys2 = keysIn(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    if (!(key in object) || !eq(object[key], source[key])) {
      object[key] = source[key];
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/assignInWith.mjs
function assignInWith(object, ...sources) {
  let getValueToAssign = sources[sources.length - 1];
  if (typeof getValueToAssign === "function") {
    sources.pop();
  } else {
    getValueToAssign = void 0;
  }
  for (let i = 0; i < sources.length; i++) {
    assignInWithImpl(object, sources[i], getValueToAssign);
  }
  return object;
}
function assignInWithImpl(object, source, getValueToAssign) {
  const keys2 = keysIn(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const objValue = object[key];
    const srcValue = source[key];
    const newValue = (getValueToAssign == null ? void 0 : getValueToAssign(objValue, srcValue, key, object, source)) ?? srcValue;
    if (!(key in object) || !eq(objValue, newValue)) {
      object[key] = newValue;
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/assignWith.mjs
function assignWith(object, ...sources) {
  let getValueToAssign = sources[sources.length - 1];
  if (typeof getValueToAssign === "function") {
    sources.pop();
  } else {
    getValueToAssign = void 0;
  }
  for (let i = 0; i < sources.length; i++) {
    assignWithImpl(object, sources[i], getValueToAssign);
  }
  return object;
}
function assignWithImpl(object, source, getValueToAssign) {
  const keys$1 = keys(source);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    const objValue = object[key];
    const srcValue = source[key];
    const newValue = (getValueToAssign == null ? void 0 : getValueToAssign(objValue, srcValue, key, object, source)) ?? srcValue;
    if (!(key in object) || !eq(objValue, newValue)) {
      object[key] = newValue;
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/clone.mjs
function clone2(obj) {
  if (isPrimitive(obj)) {
    return obj;
  }
  const tag = getTag(obj);
  if (!isCloneableObject(obj)) {
    return {};
  }
  if (isArray(obj)) {
    const result3 = Array.from(obj);
    if (obj.length > 0 && typeof obj[0] === "string" && Object.hasOwn(obj, "index")) {
      result3.index = obj.index;
      result3.input = obj.input;
    }
    return result3;
  }
  if (isTypedArray2(obj)) {
    const typedArray = obj;
    const Ctor = typedArray.constructor;
    return new Ctor(typedArray.buffer, typedArray.byteOffset, typedArray.length);
  }
  if (tag === arrayBufferTag) {
    return new ArrayBuffer(obj.byteLength);
  }
  if (tag === dataViewTag) {
    const dataView = obj;
    const buffer = dataView.buffer;
    const byteOffset = dataView.byteOffset;
    const byteLength = dataView.byteLength;
    const clonedBuffer = new ArrayBuffer(byteLength);
    const srcView = new Uint8Array(buffer, byteOffset, byteLength);
    const destView = new Uint8Array(clonedBuffer);
    destView.set(srcView);
    return new DataView(clonedBuffer);
  }
  if (tag === booleanTag || tag === numberTag || tag === stringTag) {
    const Ctor = obj.constructor;
    const clone3 = new Ctor(obj.valueOf());
    if (tag === stringTag) {
      cloneStringObjectProperties(clone3, obj);
    } else {
      copyOwnProperties(clone3, obj);
    }
    return clone3;
  }
  if (tag === dateTag) {
    return new Date(Number(obj));
  }
  if (tag === regexpTag) {
    const regExp = obj;
    const clone3 = new RegExp(regExp.source, regExp.flags);
    clone3.lastIndex = regExp.lastIndex;
    return clone3;
  }
  if (tag === symbolTag) {
    return Object(Symbol.prototype.valueOf.call(obj));
  }
  if (tag === mapTag) {
    const map2 = obj;
    const result3 = /* @__PURE__ */ new Map();
    map2.forEach((obj2, key) => {
      result3.set(key, obj2);
    });
    return result3;
  }
  if (tag === setTag) {
    const set2 = obj;
    const result3 = /* @__PURE__ */ new Set();
    set2.forEach((obj2) => {
      result3.add(obj2);
    });
    return result3;
  }
  if (tag === argumentsTag) {
    const args = obj;
    const result3 = {};
    copyOwnProperties(result3, args);
    result3.length = args.length;
    result3[Symbol.iterator] = args[Symbol.iterator];
    return result3;
  }
  const result2 = {};
  copyPrototype(result2, obj);
  copyOwnProperties(result2, obj);
  copySymbolProperties(result2, obj);
  return result2;
}
function isCloneableObject(object) {
  switch (getTag(object)) {
    case argumentsTag:
    case arrayTag:
    case arrayBufferTag:
    case dataViewTag:
    case booleanTag:
    case dateTag:
    case float32ArrayTag:
    case float64ArrayTag:
    case int8ArrayTag:
    case int16ArrayTag:
    case int32ArrayTag:
    case mapTag:
    case numberTag:
    case objectTag:
    case regexpTag:
    case setTag:
    case stringTag:
    case symbolTag:
    case uint8ArrayTag:
    case uint8ClampedArrayTag:
    case uint16ArrayTag:
    case uint32ArrayTag: {
      return true;
    }
    default: {
      return false;
    }
  }
}
function copyOwnProperties(target, source) {
  for (const key in source) {
    if (Object.hasOwn(source, key)) {
      target[key] = source[key];
    }
  }
}
function copySymbolProperties(target, source) {
  const symbols = Object.getOwnPropertySymbols(source);
  for (let i = 0; i < symbols.length; i++) {
    const symbol = symbols[i];
    if (Object.prototype.propertyIsEnumerable.call(source, symbol)) {
      target[symbol] = source[symbol];
    }
  }
}
function cloneStringObjectProperties(target, source) {
  const stringLength = source.valueOf().length;
  for (const key in source) {
    if (Object.hasOwn(source, key) && (Number.isNaN(Number(key)) || Number(key) >= stringLength)) {
      target[key] = source[key];
    }
  }
}
function copyPrototype(target, source) {
  const proto = Object.getPrototypeOf(source);
  if (proto !== null) {
    const Ctor = source.constructor;
    if (typeof Ctor === "function") {
      Object.setPrototypeOf(target, proto);
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/cloneWith.mjs
function cloneWith(value, customizer) {
  if (!customizer) {
    return clone2(value);
  }
  const result2 = customizer(value);
  if (result2 !== void 0) {
    return result2;
  }
  return clone2(value);
}

// node_modules/es-toolkit/dist/compat/object/create.mjs
function create(prototype, properties) {
  const proto = isObject(prototype) ? Object.create(prototype) : {};
  if (properties != null) {
    const propsKeys = keys(properties);
    for (let i = 0; i < propsKeys.length; i++) {
      const key = propsKeys[i];
      const propsValue = properties[key];
      assignValue(proto, key, propsValue);
    }
  }
  return proto;
}

// node_modules/es-toolkit/dist/compat/object/defaults.mjs
function defaults(object, ...sources) {
  object = Object(object);
  const objectProto = Object.prototype;
  let length = sources.length;
  const guard = length > 2 ? sources[2] : void 0;
  if (guard && isIterateeCall(sources[0], sources[1], guard)) {
    length = 1;
  }
  for (let i = 0; i < length; i++) {
    const source = sources[i];
    const keys2 = Object.keys(source);
    for (let j = 0; j < keys2.length; j++) {
      const key = keys2[j];
      const value = object[key];
      if (value === void 0 || !Object.hasOwn(object, key) && eq(value, objectProto[key])) {
        object[key] = source[key];
      }
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/defaultsDeep.mjs
function defaultsDeep(target, ...sources) {
  target = Object(target);
  for (let i = 0; i < sources.length; i++) {
    const source = sources[i];
    if (source != null) {
      const stack = /* @__PURE__ */ new WeakMap();
      defaultsDeepRecursive(target, source, stack);
    }
  }
  return target;
}
function defaultsDeepRecursive(target, source, stack) {
  for (const key in source) {
    const sourceValue = source[key];
    const targetValue = target[key];
    const targetHasKey = Object.hasOwn(target, key);
    if (!targetHasKey || targetValue === void 0) {
      if (stack.has(sourceValue)) {
        target[key] = stack.get(sourceValue);
      } else if (isPlainObject(sourceValue)) {
        const newObj = {};
        stack.set(sourceValue, newObj);
        target[key] = newObj;
        defaultsDeepRecursive(newObj, sourceValue, stack);
      } else {
        target[key] = sourceValue;
      }
    } else if (isPlainObject(targetValue) && isPlainObject(sourceValue)) {
      const inStack = stack.has(sourceValue);
      if (!inStack || inStack && stack.get(sourceValue) !== targetValue) {
        stack.set(sourceValue, targetValue);
        defaultsDeepRecursive(targetValue, sourceValue, stack);
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/findKey.mjs
function findKey2(obj, predicate) {
  if (!isObject(obj)) {
    return void 0;
  }
  const iteratee$1 = iteratee(predicate);
  return findKey(obj, iteratee$1);
}

// node_modules/es-toolkit/dist/compat/object/findLastKey.mjs
function findLastKey(obj, predicate) {
  if (!isObject(obj)) {
    return void 0;
  }
  const iteratee$1 = iteratee(predicate);
  const keys2 = Object.keys(obj);
  return keys2.findLast((key) => iteratee$1(obj[key], key, obj));
}

// node_modules/es-toolkit/dist/compat/object/forIn.mjs
function forIn(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  for (const key in object) {
    const result2 = iteratee2(object[key], key, object);
    if (result2 === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forInRight.mjs
function forInRight(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const keys2 = [];
  for (const key in object) {
    keys2.push(key);
  }
  for (let i = keys2.length - 1; i >= 0; i--) {
    const key = keys2[i];
    const result2 = iteratee2(object[key], key, object);
    if (result2 === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forOwn.mjs
function forOwn(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const iterable = Object(object);
  const keys$1 = keys(object);
  for (let i = 0; i < keys$1.length; ++i) {
    const key = keys$1[i];
    if (iteratee2(iterable[key], key, iterable) === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forOwnRight.mjs
function forOwnRight(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const iterable = Object(object);
  const keys$1 = keys(object);
  for (let i = keys$1.length - 1; i >= 0; --i) {
    const key = keys$1[i];
    if (iteratee2(iterable[key], key, iterable) === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/fromPairs.mjs
function fromPairs(pairs) {
  if (!isArrayLike(pairs) && !(pairs instanceof Map)) {
    return {};
  }
  const result2 = {};
  for (const [key, value] of pairs) {
    result2[key] = value;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/functions.mjs
function functions(object) {
  if (object == null) {
    return [];
  }
  return keys(object).filter((key) => typeof object[key] === "function");
}

// node_modules/es-toolkit/dist/compat/object/functionsIn.mjs
function functionsIn(object) {
  if (object == null) {
    return [];
  }
  const result2 = [];
  for (const key in object) {
    if (isFunction(object[key])) {
      result2.push(key);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/hasIn.mjs
function hasIn(object, path) {
  let resolvedPath;
  if (Array.isArray(path)) {
    resolvedPath = path;
  } else if (typeof path === "string" && isDeepKey(path) && (object == null ? void 0 : object[path]) == null) {
    resolvedPath = toPath(path);
  } else {
    resolvedPath = [path];
  }
  if (resolvedPath.length === 0) {
    return false;
  }
  let current = object;
  for (let i = 0; i < resolvedPath.length; i++) {
    const key = resolvedPath[i];
    if (current == null || !(key in Object(current))) {
      const isSparseIndex = (Array.isArray(current) || isArguments(current)) && isIndex(key) && key < current.length;
      if (!isSparseIndex) {
        return false;
      }
    }
    current = current[key];
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/object/invertBy.mjs
function invertBy(object, iteratee2) {
  const result2 = {};
  if (isNil(object)) {
    return result2;
  }
  if (iteratee2 == null) {
    iteratee2 = identity;
  }
  const keys2 = Object.keys(object);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    const valueStr = iteratee2(value);
    if (Array.isArray(result2[valueStr])) {
      result2[valueStr].push(key);
    } else {
      result2[valueStr] = [key];
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/mapKeys.mjs
function mapKeys2(object, getNewKey) {
  getNewKey = getNewKey ?? identity;
  switch (typeof getNewKey) {
    case "string":
    case "symbol":
    case "number":
    case "object": {
      return mapKeys(object, property(getNewKey));
    }
    case "function": {
      return mapKeys(object, getNewKey);
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/mapValues.mjs
function mapValues2(object, getNewValue) {
  getNewValue = getNewValue ?? identity;
  switch (typeof getNewValue) {
    case "string":
    case "symbol":
    case "number":
    case "object": {
      return mapValues(object, property(getNewValue));
    }
    case "function": {
      return mapValues(object, getNewValue);
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/mergeWith.mjs
function mergeWith(object, ...otherArgs) {
  const sources = otherArgs.slice(0, -1);
  const merge2 = otherArgs[otherArgs.length - 1];
  let result2 = object;
  for (let i = 0; i < sources.length; i++) {
    const source = sources[i];
    result2 = mergeWithDeep(result2, source, merge2, /* @__PURE__ */ new Map());
  }
  return result2;
}
function mergeWithDeep(target, source, merge2, stack) {
  if (isPrimitive(target)) {
    target = Object(target);
  }
  if (source == null || typeof source !== "object") {
    return target;
  }
  if (stack.has(source)) {
    return clone(stack.get(source));
  }
  stack.set(source, target);
  if (Array.isArray(source)) {
    source = source.slice();
    for (let i = 0; i < source.length; i++) {
      source[i] = source[i] ?? void 0;
    }
  }
  const sourceKeys = [...Object.keys(source), ...getSymbols(source)];
  for (let i = 0; i < sourceKeys.length; i++) {
    const key = sourceKeys[i];
    let sourceValue = source[key];
    let targetValue = target[key];
    if (isArguments(sourceValue)) {
      sourceValue = { ...sourceValue };
    }
    if (isArguments(targetValue)) {
      targetValue = { ...targetValue };
    }
    if (typeof Buffer !== "undefined" && Buffer.isBuffer(sourceValue)) {
      sourceValue = cloneDeep2(sourceValue);
    }
    if (Array.isArray(sourceValue)) {
      if (typeof targetValue === "object" && targetValue != null) {
        const cloned = [];
        const targetKeys = Reflect.ownKeys(targetValue);
        for (let i2 = 0; i2 < targetKeys.length; i2++) {
          const targetKey = targetKeys[i2];
          cloned[targetKey] = targetValue[targetKey];
        }
        targetValue = cloned;
      } else {
        targetValue = [];
      }
    }
    const merged = merge2(targetValue, sourceValue, key, target, source, stack);
    if (merged != null) {
      target[key] = merged;
    } else if (Array.isArray(sourceValue)) {
      target[key] = mergeWithDeep(targetValue, sourceValue, merge2, stack);
    } else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {
      target[key] = mergeWithDeep(targetValue, sourceValue, merge2, stack);
    } else if (targetValue == null && isPlainObject(sourceValue)) {
      target[key] = mergeWithDeep({}, sourceValue, merge2, stack);
    } else if (targetValue == null && isTypedArray2(sourceValue)) {
      target[key] = cloneDeep2(sourceValue);
    } else if (targetValue === void 0 || sourceValue !== void 0) {
      target[key] = sourceValue;
    }
  }
  return target;
}

// node_modules/es-toolkit/dist/compat/object/merge.mjs
function merge(object, ...sources) {
  return mergeWith(object, ...sources, noop);
}

// node_modules/es-toolkit/dist/compat/object/omit.mjs
function omit(obj, ...keysArr) {
  if (obj == null) {
    return {};
  }
  const result2 = cloneDeep(obj);
  for (let i = 0; i < keysArr.length; i++) {
    let keys2 = keysArr[i];
    switch (typeof keys2) {
      case "object": {
        if (!Array.isArray(keys2)) {
          keys2 = Array.from(keys2);
        }
        for (let j = 0; j < keys2.length; j++) {
          const key = keys2[j];
          unset(result2, key);
        }
        break;
      }
      case "string":
      case "symbol":
      case "number": {
        unset(result2, keys2);
        break;
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/_internal/getSymbolsIn.mjs
function getSymbolsIn(object) {
  const result2 = [];
  while (object) {
    result2.push(...getSymbols(object));
    object = Object.getPrototypeOf(object);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/omitBy.mjs
function omitBy(obj, shouldOmit) {
  if (obj == null) {
    return {};
  }
  const result2 = {};
  if (shouldOmit == null) {
    return {};
  }
  const keys2 = isArrayLike(obj) ? range(0, obj.length) : [...keysIn(obj), ...getSymbolsIn(obj)];
  for (let i = 0; i < keys2.length; i++) {
    const key = isSymbol(keys2[i]) ? keys2[i] : keys2[i].toString();
    const value = obj[key];
    if (!shouldOmit(value, key, obj)) {
      result2[key] = value;
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/pick.mjs
function pick(obj, ...keysArr) {
  if (isNil2(obj)) {
    return {};
  }
  const result2 = {};
  for (let i = 0; i < keysArr.length; i++) {
    let keys2 = keysArr[i];
    switch (typeof keys2) {
      case "object": {
        if (!Array.isArray(keys2)) {
          if (isArrayLike(keys2)) {
            keys2 = Array.from(keys2);
          } else {
            keys2 = [keys2];
          }
        }
        break;
      }
      case "string":
      case "symbol":
      case "number": {
        keys2 = [keys2];
        break;
      }
    }
    for (const key of keys2) {
      const value = get(obj, key);
      if (value === void 0 && !has(obj, key)) {
        continue;
      }
      if (typeof key === "string" && Object.hasOwn(obj, key)) {
        result2[key] = value;
      } else {
        set(result2, key, value);
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/pickBy.mjs
function pickBy(obj, shouldPick) {
  if (obj == null) {
    return {};
  }
  const result2 = {};
  if (shouldPick == null) {
    return obj;
  }
  const keys2 = isArrayLike(obj) ? range(0, obj.length) : [...keysIn(obj), ...getSymbolsIn(obj)];
  for (let i = 0; i < keys2.length; i++) {
    const key = isSymbol(keys2[i]) ? keys2[i] : keys2[i].toString();
    const value = obj[key];
    if (shouldPick(value, key, obj)) {
      result2[key] = value;
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/propertyOf.mjs
function propertyOf(object) {
  return function(path) {
    return get(object, path);
  };
}

// node_modules/es-toolkit/dist/compat/object/result.mjs
function result(object, path, defaultValue) {
  if (isKey(path, object)) {
    path = [path];
  } else if (!Array.isArray(path)) {
    path = toPath(toString(path));
  }
  const pathLength = Math.max(path.length, 1);
  for (let index = 0; index < pathLength; index++) {
    const value = object == null ? void 0 : object[toKey(path[index])];
    if (value === void 0) {
      return typeof defaultValue === "function" ? defaultValue.call(object) : defaultValue;
    }
    object = typeof value === "function" ? value.call(object) : value;
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/setWith.mjs
function setWith(obj, path, value, customizer) {
  let customizerFn;
  if (typeof customizer === "function") {
    customizerFn = customizer;
  } else {
    customizerFn = () => void 0;
  }
  return updateWith(obj, path, () => value, customizerFn);
}

// node_modules/es-toolkit/dist/compat/object/toDefaulted.mjs
function toDefaulted(object, ...sources) {
  const cloned = cloneDeep2(object);
  return defaults(cloned, ...sources);
}

// node_modules/es-toolkit/dist/compat/_internal/mapToEntries.mjs
function mapToEntries(map2) {
  const arr = new Array(map2.size);
  const keys2 = map2.keys();
  const values2 = map2.values();
  for (let i = 0; i < arr.length; i++) {
    arr[i] = [keys2.next().value, values2.next().value];
  }
  return arr;
}

// node_modules/es-toolkit/dist/compat/_internal/setToEntries.mjs
function setToEntries(set2) {
  const arr = new Array(set2.size);
  const values2 = set2.values();
  for (let i = 0; i < arr.length; i++) {
    const value = values2.next().value;
    arr[i] = [value, value];
  }
  return arr;
}

// node_modules/es-toolkit/dist/compat/object/toPairs.mjs
function toPairs(object) {
  if (object instanceof Set) {
    return setToEntries(object);
  }
  if (object instanceof Map) {
    return mapToEntries(object);
  }
  const keys$1 = keys(object);
  const result2 = new Array(keys$1.length);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    const value = object[key];
    result2[i] = [key, value];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/toPairsIn.mjs
function toPairsIn(object) {
  if (object instanceof Set) {
    return setToEntries(object);
  }
  if (object instanceof Map) {
    return mapToEntries(object);
  }
  const keys2 = keysIn(object);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    result2[i] = [key, value];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isBuffer.mjs
function isBuffer2(x2) {
  return isBuffer(x2);
}

// node_modules/es-toolkit/dist/compat/object/transform.mjs
function transform(object, iteratee$1 = identity, accumulator) {
  const isArrayOrBufferOrTypedArray = Array.isArray(object) || isBuffer2(object) || isTypedArray2(object);
  iteratee$1 = iteratee(iteratee$1);
  if (accumulator == null) {
    if (isArrayOrBufferOrTypedArray) {
      accumulator = [];
    } else if (isObject(object) && isFunction(object.constructor)) {
      accumulator = Object.create(Object.getPrototypeOf(object));
    } else {
      accumulator = {};
    }
  }
  if (object == null) {
    return accumulator;
  }
  forEach(object, (value, key, object2) => iteratee$1(accumulator, value, key, object2));
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/object/update.mjs
function update(obj, path, updater) {
  return updateWith(obj, path, updater, () => void 0);
}

// node_modules/es-toolkit/dist/compat/object/valuesIn.mjs
function valuesIn(object) {
  const keys2 = keysIn(object);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    result2[i] = object[key];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isNative.mjs
var functionToString = Function.prototype.toString;
var REGEXP_SYNTAX_CHARS = /[\\^$.*+?()[\]{}|]/g;
var IS_NATIVE_FUNCTION_REGEXP = RegExp(`^${functionToString.call(Object.prototype.hasOwnProperty).replace(REGEXP_SYNTAX_CHARS, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?")}$`);
function isNative(value) {
  if (typeof value !== "function") {
    return false;
  }
  if ((globalThis == null ? void 0 : globalThis["__core-js_shared__"]) != null) {
    throw new Error("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");
  }
  return IS_NATIVE_FUNCTION_REGEXP.test(functionToString.call(value));
}

// node_modules/es-toolkit/dist/compat/predicate/conformsTo.mjs
function conformsTo(target, source) {
  if (source == null) {
    return true;
  }
  if (target == null) {
    return Object.keys(source).length === 0;
  }
  const keys2 = Object.keys(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const predicate = source[key];
    const value = target[key];
    if (value === void 0 && !(key in target) || !predicate(value)) {
      return false;
    }
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/conforms.mjs
function conforms(source) {
  source = cloneDeep(source);
  return function(object) {
    return conformsTo(object, source);
  };
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayBuffer.mjs
function isArrayBuffer2(value) {
  return isArrayBuffer(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isBoolean.mjs
function isBoolean(value) {
  return typeof value === "boolean" || value instanceof Boolean;
}

// node_modules/es-toolkit/dist/compat/predicate/isDate.mjs
function isDate2(value) {
  return isDate(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isElement.mjs
function isElement(value) {
  return isObjectLike(value) && value.nodeType === 1 && !isPlainObject(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isEmpty.mjs
function isEmpty(value) {
  if (value == null) {
    return true;
  }
  if (isArrayLike(value)) {
    if (typeof value.splice !== "function" && typeof value !== "string" && (typeof Buffer === "undefined" || !Buffer.isBuffer(value)) && !isTypedArray2(value) && !isArguments(value)) {
      return false;
    }
    return value.length === 0;
  }
  if (typeof value === "object") {
    if (value instanceof Map || value instanceof Set) {
      return value.size === 0;
    }
    const keys2 = Object.keys(value);
    if (isPrototype(value)) {
      return keys2.filter((x2) => x2 !== "constructor").length === 0;
    }
    return keys2.length === 0;
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/isEqualWith.mjs
function isEqualWith2(a, b, areValuesEqual = noop) {
  if (typeof areValuesEqual !== "function") {
    areValuesEqual = noop;
  }
  return isEqualWith(a, b, (...args) => {
    const result2 = areValuesEqual(...args);
    if (result2 !== void 0) {
      return Boolean(result2);
    }
    if (a instanceof Map && b instanceof Map) {
      return isEqualWith2(Array.from(a), Array.from(b), after(2, areValuesEqual));
    }
    if (a instanceof Set && b instanceof Set) {
      return isEqualWith2(Array.from(a), Array.from(b), after(2, areValuesEqual));
    }
  });
}

// node_modules/es-toolkit/dist/compat/predicate/isError.mjs
function isError(value) {
  return getTag(value) === "[object Error]";
}

// node_modules/es-toolkit/dist/compat/predicate/isFinite.mjs
function isFinite(value) {
  return Number.isFinite(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isInteger.mjs
function isInteger(value) {
  return Number.isInteger(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isRegExp.mjs
function isRegExp2(value) {
  return isRegExp(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isSafeInteger.mjs
function isSafeInteger(value) {
  return Number.isSafeInteger(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isSet.mjs
function isSet2(value) {
  return isSet(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isWeakMap.mjs
function isWeakMap2(value) {
  return isWeakMap(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isWeakSet.mjs
function isWeakSet2(value) {
  return isWeakSet(value);
}

// node_modules/es-toolkit/dist/compat/util/bindAll.mjs
function bindAll(object, ...methodNames) {
  if (object == null) {
    return object;
  }
  if (!isObject(object)) {
    return object;
  }
  if (isArray(object) && methodNames.length === 0) {
    return object;
  }
  const methods = [];
  for (let i = 0; i < methodNames.length; i++) {
    const name = methodNames[i];
    if (isArray(name)) {
      methods.push(...name);
    } else if (name && typeof name === "object" && "length" in name) {
      methods.push(...Array.from(name));
    } else {
      methods.push(name);
    }
  }
  if (methods.length === 0) {
    return object;
  }
  for (let i = 0; i < methods.length; i++) {
    const key = methods[i];
    const stringKey = toString(key);
    const func = object[stringKey];
    if (isFunction(func)) {
      object[stringKey] = func.bind(object);
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/_internal/normalizeForCase.mjs
function normalizeForCase(str) {
  if (typeof str !== "string") {
    str = toString(str);
  }
  return str.replace(/['\u2019]/g, "");
}

// node_modules/es-toolkit/dist/compat/string/camelCase.mjs
function camelCase2(str) {
  return camelCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/deburr.mjs
function deburr2(str) {
  return deburr(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/endsWith.mjs
function endsWith(str, target, position = str.length) {
  return str.endsWith(target, position);
}

// node_modules/es-toolkit/dist/compat/string/escape.mjs
function escape2(string) {
  return escape(toString(string));
}

// node_modules/es-toolkit/dist/compat/string/escapeRegExp.mjs
function escapeRegExp2(str) {
  return escapeRegExp(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/kebabCase.mjs
function kebabCase2(str) {
  return kebabCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/lowerCase.mjs
function lowerCase2(str) {
  return lowerCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/lowerFirst.mjs
function lowerFirst2(str) {
  return lowerFirst(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/pad.mjs
function pad2(str, length, chars = " ") {
  return pad(toString(str), length, chars);
}

// node_modules/es-toolkit/dist/compat/string/padEnd.mjs
function padEnd(str, length = 0, chars = " ") {
  return toString(str).padEnd(length, chars);
}

// node_modules/es-toolkit/dist/compat/string/padStart.mjs
function padStart(str, length = 0, chars = " ") {
  return toString(str).padStart(length, chars);
}

// node_modules/es-toolkit/dist/compat/string/repeat.mjs
function repeat(str, n, guard) {
  if (guard ? isIterateeCall(str, n, guard) : n === void 0) {
    n = 1;
  } else {
    n = toInteger(n);
  }
  return toString(str).repeat(n);
}

// node_modules/es-toolkit/dist/compat/string/replace.mjs
function replace(target = "", pattern, replacement) {
  if (arguments.length < 3) {
    return toString(target);
  }
  return toString(target).replace(pattern, replacement);
}

// node_modules/es-toolkit/dist/compat/string/snakeCase.mjs
function snakeCase2(str) {
  return snakeCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/split.mjs
function split(string = "", separator, limit) {
  return toString(string).split(separator, limit);
}

// node_modules/es-toolkit/dist/compat/string/startCase.mjs
function startCase(str) {
  const words$1 = words(normalizeForCase(str).trim());
  let result2 = "";
  for (let i = 0; i < words$1.length; i++) {
    const word = words$1[i];
    if (result2) {
      result2 += " ";
    }
    if (word === word.toUpperCase()) {
      result2 += word;
    } else {
      result2 += word[0].toUpperCase() + word.slice(1).toLowerCase();
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/string/startsWith.mjs
function startsWith(str, target, position = 0) {
  return str.startsWith(target, position);
}

// node_modules/es-toolkit/dist/compat/string/template.mjs
var esTemplateRegExp = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g;
var unEscapedRegExp = /['\n\r\u2028\u2029\\]/g;
var noMatchExp = /($^)/;
var escapeMap = /* @__PURE__ */ new Map([
  ["\\", "\\"],
  ["'", "'"],
  ["\n", "n"],
  ["\r", "r"],
  ["\u2028", "u2028"],
  ["\u2029", "u2029"]
]);
function escapeString(match) {
  return `\\${escapeMap.get(match)}`;
}
var templateSettings = {
  escape: /<%-([\s\S]+?)%>/g,
  evaluate: /<%([\s\S]+?)%>/g,
  interpolate: /<%=([\s\S]+?)%>/g,
  variable: "",
  imports: {
    _: {
      escape: escape2,
      template
    }
  }
};
function template(string, options, guard) {
  var _a, _b, _c;
  string = toString(string);
  if (guard) {
    options = templateSettings;
  }
  options = defaults({ ...options }, templateSettings);
  const delimitersRegExp = new RegExp([
    ((_a = options.escape) == null ? void 0 : _a.source) ?? noMatchExp.source,
    ((_b = options.interpolate) == null ? void 0 : _b.source) ?? noMatchExp.source,
    options.interpolate ? esTemplateRegExp.source : noMatchExp.source,
    ((_c = options.evaluate) == null ? void 0 : _c.source) ?? noMatchExp.source,
    "$"
  ].join("|"), "g");
  let lastIndex = 0;
  let isEvaluated = false;
  let source = `__p += ''`;
  for (const match of string.matchAll(delimitersRegExp)) {
    const [fullMatch, escapeValue, interpolateValue, esTemplateValue, evaluateValue] = match;
    const { index } = match;
    source += ` + '${string.slice(lastIndex, index).replace(unEscapedRegExp, escapeString)}'`;
    if (escapeValue) {
      source += ` + _.escape(${escapeValue})`;
    }
    if (interpolateValue) {
      source += ` + ((${interpolateValue}) == null ? '' : ${interpolateValue})`;
    } else if (esTemplateValue) {
      source += ` + ((${esTemplateValue}) == null ? '' : ${esTemplateValue})`;
    }
    if (evaluateValue) {
      source += `;
${evaluateValue};
 __p += ''`;
      isEvaluated = true;
    }
    lastIndex = index + fullMatch.length;
  }
  const imports = defaults({ ...options.imports }, templateSettings.imports);
  const importsKeys = Object.keys(imports);
  const importValues = Object.values(imports);
  const sourceURL = `//# sourceURL=${options.sourceURL ? String(options.sourceURL).replace(/[\r\n]/g, " ") : `es-toolkit.templateSource[${Date.now()}]`}
`;
  const compiledFunction = `function(${options.variable || "obj"}) {
    let __p = '';
    ${options.variable ? "" : "if (obj == null) { obj = {}; }"}
    ${isEvaluated ? `function print() { __p += Array.prototype.join.call(arguments, ''); }` : ""}
    ${options.variable ? source : `with(obj) {
${source}
}`}
    return __p;
  }`;
  const result2 = attempt(() => new Function(...importsKeys, `${sourceURL}return ${compiledFunction}`)(...importValues));
  result2.source = compiledFunction;
  if (result2 instanceof Error) {
    throw result2;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/string/toLower.mjs
function toLower(value) {
  return toString(value).toLowerCase();
}

// node_modules/es-toolkit/dist/compat/string/toUpper.mjs
function toUpper(value) {
  return toString(value).toUpperCase();
}

// node_modules/es-toolkit/dist/compat/string/trim.mjs
function trim2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trim();
  }
  switch (typeof chars) {
    case "string": {
      return trim(str, chars.toString().split(""));
    }
    case "object": {
      if (Array.isArray(chars)) {
        return trim(str, chars.flatMap((x2) => x2.toString().split("")));
      } else {
        return trim(str, chars.toString().split(""));
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/string/trimEnd.mjs
function trimEnd2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trimEnd();
  }
  switch (typeof chars) {
    case "string": {
      return trimEnd(str, chars.toString().split(""));
    }
    case "object": {
      if (Array.isArray(chars)) {
        return trimEnd(str, chars.flatMap((x2) => x2.toString().split("")));
      } else {
        return trimEnd(str, chars.toString().split(""));
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/string/trimStart.mjs
function trimStart2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trimStart();
  }
  switch (typeof chars) {
    case "string": {
      return trimStart(str, chars.toString().split(""));
    }
    case "object": {
      if (Array.isArray(chars)) {
        return trimStart(str, chars.flatMap((x2) => x2.toString().split("")));
      } else {
        return trimStart(str, chars.toString().split(""));
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/string/truncate.mjs
var regexMultiByte = /[\u200d\ud800-\udfff\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff\ufe0e\ufe0f]/;
function truncate(string, options = {}) {
  string = string != null ? `${string}` : "";
  let length = 30;
  let omission = "...";
  if (isObject(options)) {
    length = parseLength(options.length);
    omission = "omission" in options ? `${options.omission}` : "...";
  }
  let i = string.length;
  const lengthOmission = Array.from(omission).length;
  const lengthBase = Math.max(length - lengthOmission, 0);
  let strArray = void 0;
  const unicode = regexMultiByte.test(string);
  if (unicode) {
    strArray = Array.from(string);
    i = strArray.length;
  }
  if (length >= i) {
    return string;
  }
  if (i <= lengthOmission) {
    return omission;
  }
  let base = strArray === void 0 ? string.slice(0, lengthBase) : strArray == null ? void 0 : strArray.slice(0, lengthBase).join("");
  const separator = options.separator;
  if (!separator) {
    base += omission;
    return base;
  }
  const search = separator instanceof RegExp ? separator.source : separator;
  const flags = "u" + (separator instanceof RegExp ? separator.flags.replace("u", "") : "");
  const withoutSeparator = new RegExp(`(?<result>.*(?:(?!${search}).))(?:${search})`, flags).exec(base);
  return (!(withoutSeparator == null ? void 0 : withoutSeparator.groups) ? base : withoutSeparator.groups.result) + omission;
}
function parseLength(length) {
  if (length == null) {
    return 30;
  }
  if (length <= 0) {
    return 0;
  }
  return length;
}

// node_modules/es-toolkit/dist/compat/string/unescape.mjs
function unescape2(str) {
  return unescape(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/upperCase.mjs
function upperCase2(str) {
  return upperCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/upperFirst.mjs
function upperFirst2(str) {
  return upperFirst(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/words.mjs
var rNonCharLatin = "\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\xd7\\xf7";
var rUnicodeUpper = "\\p{Lu}";
var rUnicodeLower = "\\p{Ll}";
var rMisc = "(?:[\\p{Lm}\\p{Lo}]\\p{M}*)";
var rNumber = "\\d";
var rUnicodeOptContrLower = "(?:['’](?:d|ll|m|re|s|t|ve))?";
var rUnicodeOptContrUpper = "(?:['’](?:D|LL|M|RE|S|T|VE))?";
var rUnicodeBreak = `[\\p{Z}\\p{P}${rNonCharLatin}]`;
var rUnicodeMiscUpper = `(?:${rUnicodeUpper}|${rMisc})`;
var rUnicodeMiscLower = `(?:${rUnicodeLower}|${rMisc})`;
var rUnicodeWord = RegExp([
  `${rUnicodeUpper}?${rUnicodeLower}+${rUnicodeOptContrLower}(?=${rUnicodeBreak}|${rUnicodeUpper}|$)`,
  `${rUnicodeMiscUpper}+${rUnicodeOptContrUpper}(?=${rUnicodeBreak}|${rUnicodeUpper}${rUnicodeMiscLower}|$)`,
  `${rUnicodeUpper}?${rUnicodeMiscLower}+${rUnicodeOptContrLower}`,
  `${rUnicodeUpper}+${rUnicodeOptContrUpper}`,
  `${rNumber}*(?:1ST|2ND|3RD|(?![123])${rNumber}TH)(?=\\b|[a-z_])`,
  `${rNumber}*(?:1st|2nd|3rd|(?![123])${rNumber}th)(?=\\b|[A-Z_])`,
  `${rNumber}+`,
  "\\p{Emoji_Presentation}",
  "\\p{Extended_Pictographic}"
].join("|"), "gu");
function words2(str, pattern = rUnicodeWord, guard) {
  const input = toString(str);
  pattern = guard ? rUnicodeWord : pattern;
  const words3 = Array.from(input.match(pattern) ?? []);
  return words3.filter((x2) => x2 !== "");
}

// node_modules/es-toolkit/dist/compat/util/cond.mjs
function cond(pairs) {
  const length = pairs.length;
  const processedPairs = pairs.map((pair) => {
    const predicate = pair[0];
    const func = pair[1];
    if (!isFunction(func)) {
      throw new TypeError("Expected a function");
    }
    return [iteratee(predicate), func];
  });
  return function(...args) {
    for (let i = 0; i < length; i++) {
      const pair = processedPairs[i];
      const predicate = pair[0];
      const func = pair[1];
      if (predicate.apply(this, args)) {
        return func.apply(this, args);
      }
    }
  };
}

// node_modules/es-toolkit/dist/compat/util/constant.mjs
function constant(value) {
  return () => value;
}

// node_modules/es-toolkit/dist/compat/util/defaultTo.mjs
function defaultTo(value, defaultValue) {
  if (value == null || Number.isNaN(value)) {
    return defaultValue;
  }
  return value;
}

// node_modules/es-toolkit/dist/compat/util/gt.mjs
function gt(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value > other;
  }
  return toNumber(value) > toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/gte.mjs
function gte(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value >= other;
  }
  return toNumber(value) >= toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/invoke.mjs
function invoke(object, path, args = []) {
  if (object == null) {
    return;
  }
  switch (typeof path) {
    case "string": {
      if (typeof object === "object" && Object.hasOwn(object, path)) {
        return invokeImpl(object, [path], args);
      }
      return invokeImpl(object, toPath(path), args);
    }
    case "number":
    case "symbol": {
      return invokeImpl(object, [path], args);
    }
    default: {
      if (Array.isArray(path)) {
        return invokeImpl(object, path, args);
      } else {
        return invokeImpl(object, [path], args);
      }
    }
  }
}
function invokeImpl(object, path, args) {
  const parent = get(object, path.slice(0, -1), object);
  if (parent == null) {
    return void 0;
  }
  let lastKey = last2(path);
  const lastValue = lastKey == null ? void 0 : lastKey.valueOf();
  if (typeof lastValue === "number") {
    lastKey = toKey(lastValue);
  } else {
    lastKey = String(lastKey);
  }
  const func = get(parent, lastKey);
  return func == null ? void 0 : func.apply(parent, args);
}

// node_modules/es-toolkit/dist/compat/util/lt.mjs
function lt(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value < other;
  }
  return toNumber(value) < toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/lte.mjs
function lte(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value <= other;
  }
  return toNumber(value) <= toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/method.mjs
function method(path, ...args) {
  return function(object) {
    return invoke(object, path, args);
  };
}

// node_modules/es-toolkit/dist/compat/util/methodOf.mjs
function methodOf(object, ...args) {
  return function(path) {
    return invoke(object, path, args);
  };
}

// node_modules/es-toolkit/dist/compat/util/now.mjs
function now() {
  return Date.now();
}

// node_modules/es-toolkit/dist/compat/util/over.mjs
function over(...iteratees) {
  if (iteratees.length === 1 && Array.isArray(iteratees[0])) {
    iteratees = iteratees[0];
  }
  const funcs = iteratees.map((item) => iteratee(item));
  return function(...args) {
    return funcs.map((func) => func.apply(this, args));
  };
}

// node_modules/es-toolkit/dist/compat/util/overEvery.mjs
function overEvery(...predicates) {
  return function(...values2) {
    for (let i = 0; i < predicates.length; ++i) {
      const predicate = predicates[i];
      if (!Array.isArray(predicate)) {
        if (!iteratee(predicate).apply(this, values2)) {
          return false;
        }
        continue;
      }
      for (let j = 0; j < predicate.length; ++j) {
        if (!iteratee(predicate[j]).apply(this, values2)) {
          return false;
        }
      }
    }
    return true;
  };
}

// node_modules/es-toolkit/dist/compat/util/overSome.mjs
function overSome(...predicates) {
  return function(...values2) {
    for (let i = 0; i < predicates.length; ++i) {
      const predicate = predicates[i];
      if (!Array.isArray(predicate)) {
        if (iteratee(predicate).apply(this, values2)) {
          return true;
        }
        continue;
      }
      for (let j = 0; j < predicate.length; ++j) {
        if (iteratee(predicate[j]).apply(this, values2)) {
          return true;
        }
      }
    }
    return false;
  };
}

// node_modules/es-toolkit/dist/compat/util/stubArray.mjs
function stubArray() {
  return [];
}

// node_modules/es-toolkit/dist/compat/util/stubFalse.mjs
function stubFalse() {
  return false;
}

// node_modules/es-toolkit/dist/compat/util/stubObject.mjs
function stubObject() {
  return {};
}

// node_modules/es-toolkit/dist/compat/util/stubString.mjs
function stubString() {
  return "";
}

// node_modules/es-toolkit/dist/compat/util/stubTrue.mjs
function stubTrue() {
  return true;
}

// node_modules/es-toolkit/dist/compat/_internal/MAX_ARRAY_LENGTH.mjs
var MAX_ARRAY_LENGTH4 = 4294967295;

// node_modules/es-toolkit/dist/compat/util/toLength.mjs
function toLength(value) {
  if (value == null) {
    return 0;
  }
  const length = Math.floor(Number(value));
  return clamp2(length, 0, MAX_ARRAY_LENGTH4);
}

// node_modules/es-toolkit/dist/compat/util/toPlainObject.mjs
function toPlainObject(value) {
  const plainObject = {};
  const valueKeys = keysIn(value);
  for (let i = 0; i < valueKeys.length; i++) {
    const key = valueKeys[i];
    const objValue = value[key];
    if (key === "__proto__") {
      Object.defineProperty(plainObject, key, {
        configurable: true,
        enumerable: true,
        value: objValue,
        writable: true
      });
    } else {
      plainObject[key] = objValue;
    }
  }
  return plainObject;
}

// node_modules/es-toolkit/dist/compat/_internal/MAX_SAFE_INTEGER.mjs
var MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER;

// node_modules/es-toolkit/dist/compat/util/toSafeInteger.mjs
function toSafeInteger(value) {
  if (value == null) {
    return 0;
  }
  return clamp2(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);
}

// node_modules/es-toolkit/dist/compat/util/uniqueId.mjs
var idCounter = 0;
function uniqueId(prefix = "") {
  const id = ++idCounter;
  return `${prefix}${id}`;
}

// node_modules/es-toolkit/dist/compat/compat.mjs
var compat_exports = {};
__export(compat_exports, {
  add: () => add,
  after: () => after2,
  ary: () => ary2,
  assign: () => assign,
  assignIn: () => assignIn,
  assignInWith: () => assignInWith,
  assignWith: () => assignWith,
  at: () => at,
  attempt: () => attempt,
  before: () => before,
  bind: () => bind,
  bindAll: () => bindAll,
  bindKey: () => bindKey,
  camelCase: () => camelCase2,
  capitalize: () => capitalize,
  castArray: () => castArray,
  ceil: () => ceil,
  chunk: () => chunk2,
  clamp: () => clamp2,
  clone: () => clone2,
  cloneDeep: () => cloneDeep2,
  cloneDeepWith: () => cloneDeepWith2,
  cloneWith: () => cloneWith,
  compact: () => compact2,
  concat: () => concat,
  cond: () => cond,
  conforms: () => conforms,
  conformsTo: () => conformsTo,
  constant: () => constant,
  countBy: () => countBy,
  create: () => create,
  curry: () => curry,
  curryRight: () => curryRight,
  debounce: () => debounce2,
  deburr: () => deburr2,
  defaultTo: () => defaultTo,
  defaults: () => defaults,
  defaultsDeep: () => defaultsDeep,
  defer: () => defer,
  delay: () => delay,
  difference: () => difference2,
  differenceBy: () => differenceBy2,
  differenceWith: () => differenceWith2,
  divide: () => divide,
  drop: () => drop2,
  dropRight: () => dropRight2,
  dropRightWhile: () => dropRightWhile2,
  dropWhile: () => dropWhile2,
  each: () => forEach,
  eachRight: () => forEachRight,
  endsWith: () => endsWith,
  eq: () => eq,
  escape: () => escape2,
  escapeRegExp: () => escapeRegExp2,
  every: () => every,
  extend: () => assignIn,
  extendWith: () => assignInWith,
  fill: () => fill2,
  filter: () => filter,
  find: () => find,
  findIndex: () => findIndex,
  findKey: () => findKey2,
  findLast: () => findLast,
  findLastIndex: () => findLastIndex,
  findLastKey: () => findLastKey,
  first: () => head2,
  flatMap: () => flatMap,
  flatMapDeep: () => flatMapDeep,
  flatMapDepth: () => flatMapDepth,
  flatten: () => flatten2,
  flattenDeep: () => flattenDeep,
  flattenDepth: () => flattenDepth,
  flip: () => flip,
  floor: () => floor,
  flow: () => flow2,
  flowRight: () => flowRight2,
  forEach: () => forEach,
  forEachRight: () => forEachRight,
  forIn: () => forIn,
  forInRight: () => forInRight,
  forOwn: () => forOwn,
  forOwnRight: () => forOwnRight,
  fromPairs: () => fromPairs,
  functions: () => functions,
  functionsIn: () => functionsIn,
  get: () => get,
  groupBy: () => groupBy2,
  gt: () => gt,
  gte: () => gte,
  has: () => has,
  hasIn: () => hasIn,
  head: () => head2,
  identity: () => identity,
  inRange: () => inRange2,
  includes: () => includes,
  indexOf: () => indexOf,
  initial: () => initial2,
  intersection: () => intersection2,
  intersectionBy: () => intersectionBy2,
  intersectionWith: () => intersectionWith2,
  invert: () => invert,
  invertBy: () => invertBy,
  invoke: () => invoke,
  invokeMap: () => invokeMap,
  isArguments: () => isArguments,
  isArray: () => isArray,
  isArrayBuffer: () => isArrayBuffer2,
  isArrayLike: () => isArrayLike,
  isArrayLikeObject: () => isArrayLikeObject,
  isBoolean: () => isBoolean,
  isBuffer: () => isBuffer2,
  isDate: () => isDate2,
  isElement: () => isElement,
  isEmpty: () => isEmpty,
  isEqual: () => isEqual,
  isEqualWith: () => isEqualWith2,
  isError: () => isError,
  isFinite: () => isFinite,
  isFunction: () => isFunction,
  isInteger: () => isInteger,
  isLength: () => isLength,
  isMap: () => isMap2,
  isMatch: () => isMatch,
  isMatchWith: () => isMatchWith,
  isNaN: () => isNaN,
  isNative: () => isNative,
  isNil: () => isNil2,
  isNull: () => isNull,
  isNumber: () => isNumber,
  isObject: () => isObject,
  isObjectLike: () => isObjectLike,
  isPlainObject: () => isPlainObject,
  isRegExp: () => isRegExp2,
  isSafeInteger: () => isSafeInteger,
  isSet: () => isSet2,
  isString: () => isString,
  isSymbol: () => isSymbol,
  isTypedArray: () => isTypedArray2,
  isUndefined: () => isUndefined,
  isWeakMap: () => isWeakMap2,
  isWeakSet: () => isWeakSet2,
  iteratee: () => iteratee,
  join: () => join,
  kebabCase: () => kebabCase2,
  keyBy: () => keyBy,
  keys: () => keys,
  keysIn: () => keysIn,
  last: () => last2,
  lastIndexOf: () => lastIndexOf,
  lowerCase: () => lowerCase2,
  lowerFirst: () => lowerFirst2,
  lt: () => lt,
  lte: () => lte,
  map: () => map,
  mapKeys: () => mapKeys2,
  mapValues: () => mapValues2,
  matches: () => matches,
  matchesProperty: () => matchesProperty,
  max: () => max,
  maxBy: () => maxBy2,
  mean: () => mean,
  meanBy: () => meanBy2,
  memoize: () => memoize,
  merge: () => merge,
  mergeWith: () => mergeWith,
  method: () => method,
  methodOf: () => methodOf,
  min: () => min,
  minBy: () => minBy2,
  multiply: () => multiply,
  negate: () => negate2,
  noop: () => noop,
  now: () => now,
  nth: () => nth,
  nthArg: () => nthArg,
  omit: () => omit,
  omitBy: () => omitBy,
  once: () => once,
  orderBy: () => orderBy,
  over: () => over,
  overArgs: () => overArgs,
  overEvery: () => overEvery,
  overSome: () => overSome,
  pad: () => pad2,
  padEnd: () => padEnd,
  padStart: () => padStart,
  parseInt: () => parseInt,
  partial: () => partial,
  partialRight: () => partialRight,
  partition: () => partition,
  pick: () => pick,
  pickBy: () => pickBy,
  property: () => property,
  propertyOf: () => propertyOf,
  pull: () => pull2,
  pullAll: () => pullAll,
  pullAllBy: () => pullAllBy,
  pullAllWith: () => pullAllWith,
  pullAt: () => pullAt,
  random: () => random2,
  range: () => range2,
  rangeRight: () => rangeRight,
  rearg: () => rearg,
  reduce: () => reduce,
  reduceRight: () => reduceRight,
  reject: () => reject,
  remove: () => remove2,
  repeat: () => repeat,
  replace: () => replace,
  rest: () => rest2,
  result: () => result,
  reverse: () => reverse,
  round: () => round,
  sample: () => sample2,
  sampleSize: () => sampleSize2,
  set: () => set,
  setWith: () => setWith,
  shuffle: () => shuffle2,
  size: () => size,
  slice: () => slice,
  snakeCase: () => snakeCase2,
  some: () => some,
  sortBy: () => sortBy,
  sortedIndex: () => sortedIndex,
  sortedIndexBy: () => sortedIndexBy,
  sortedIndexOf: () => sortedIndexOf,
  sortedLastIndex: () => sortedLastIndex,
  sortedLastIndexBy: () => sortedLastIndexBy,
  sortedLastIndexOf: () => sortedLastIndexOf,
  split: () => split,
  spread: () => spread,
  startCase: () => startCase,
  startsWith: () => startsWith,
  stubArray: () => stubArray,
  stubFalse: () => stubFalse,
  stubObject: () => stubObject,
  stubString: () => stubString,
  stubTrue: () => stubTrue,
  subtract: () => subtract,
  sum: () => sum,
  sumBy: () => sumBy,
  tail: () => tail2,
  take: () => take2,
  takeRight: () => takeRight2,
  takeRightWhile: () => takeRightWhile,
  takeWhile: () => takeWhile,
  template: () => template,
  templateSettings: () => templateSettings,
  throttle: () => throttle,
  times: () => times,
  toArray: () => toArray2,
  toDefaulted: () => toDefaulted,
  toFinite: () => toFinite,
  toInteger: () => toInteger,
  toLength: () => toLength,
  toLower: () => toLower,
  toNumber: () => toNumber,
  toPairs: () => toPairs,
  toPairsIn: () => toPairsIn,
  toPath: () => toPath,
  toPlainObject: () => toPlainObject,
  toSafeInteger: () => toSafeInteger,
  toString: () => toString,
  toUpper: () => toUpper,
  transform: () => transform,
  trim: () => trim2,
  trimEnd: () => trimEnd2,
  trimStart: () => trimStart2,
  truncate: () => truncate,
  unary: () => unary,
  unescape: () => unescape2,
  union: () => union,
  unionBy: () => unionBy,
  unionWith: () => unionWith,
  uniq: () => uniq2,
  uniqBy: () => uniqBy2,
  uniqWith: () => uniqWith2,
  uniqueId: () => uniqueId,
  unset: () => unset,
  unzip: () => unzip2,
  unzipWith: () => unzipWith,
  update: () => update,
  updateWith: () => updateWith,
  upperCase: () => upperCase2,
  upperFirst: () => upperFirst2,
  values: () => values,
  valuesIn: () => valuesIn,
  without: () => without2,
  words: () => words2,
  wrap: () => wrap,
  xor: () => xor,
  xorBy: () => xorBy,
  xorWith: () => xorWith,
  zip: () => zip2,
  zipObject: () => zipObject,
  zipObjectDeep: () => zipObjectDeep,
  zipWith: () => zipWith
});

// node_modules/es-toolkit/dist/compat/toolkit.mjs
var toolkit = (value) => {
  return value;
};
Object.assign(toolkit, compat_exports);
toolkit.partial.placeholder = toolkit;
toolkit.partialRight.placeholder = toolkit;

// node_modules/@inertiajs/vue3/dist/index.esm.js
var G = { created() {
  if (!this.$options.remember) return;
  Array.isArray(this.$options.remember) && (this.$options.remember = { data: this.$options.remember }), typeof this.$options.remember == "string" && (this.$options.remember = { data: [this.$options.remember] }), typeof this.$options.remember.data == "string" && (this.$options.remember = { data: [this.$options.remember.data] });
  let e = this.$options.remember.key instanceof Function ? this.$options.remember.key.call(this) : this.$options.remember.key, r = Wr.restore(e), n = this.$options.remember.data.filter((o) => !(this[o] !== null && typeof this[o] == "object" && this[o].__rememberable === false)), s = (o) => this[o] !== null && typeof this[o] == "object" && typeof this[o].__remember == "function" && typeof this[o].__restore == "function";
  n.forEach((o) => {
    this[o] !== void 0 && r !== void 0 && r[o] !== void 0 && (s(o) ? this[o].__restore(r[o]) : this[o] = r[o]), this.$watch(o, () => {
      Wr.remember(n.reduce((a, l) => ({ ...a, [l]: cloneDeep(s(l) ? this[l].__remember() : this[l]) }), {}), e);
    }, { immediate: true, deep: true });
  });
} };
var V = G;
function x(e, r) {
  let n = typeof e == "string" ? e : null, s = (typeof e == "string" ? r : e) ?? {}, o = n ? Wr.restore(n) : null, a = typeof s == "function" ? cloneDeep(s()) : cloneDeep(s), l = null, f = null, m = (t) => t, v = reactive({ ...o ? o.data : cloneDeep(a), isDirty: false, errors: o ? o.errors : {}, hasErrors: false, processing: false, progress: null, wasSuccessful: false, recentlySuccessful: false, data() {
    return Object.keys(a).reduce((t, i) => set(t, i, get(this, i)), {});
  }, transform(t) {
    return m = t, this;
  }, defaults(t, i) {
    if (typeof s == "function") throw new Error("You cannot call `defaults()` when using a function to define your form data.");
    return typeof t > "u" ? (a = cloneDeep(this.data()), this.isDirty = false) : a = typeof t == "string" ? set(cloneDeep(a), t, i) : Object.assign({}, cloneDeep(a), t), this;
  }, reset(...t) {
    let i = typeof s == "function" ? cloneDeep(s()) : cloneDeep(a), d = cloneDeep(i);
    return t.length === 0 ? (a = d, Object.assign(this, i)) : t.filter((p) => has(d, p)).forEach((p) => {
      set(a, p, get(d, p)), set(this, p, get(i, p));
    }), this;
  }, setError(t, i) {
    return Object.assign(this.errors, typeof t == "string" ? { [t]: i } : t), this.hasErrors = Object.keys(this.errors).length > 0, this;
  }, clearErrors(...t) {
    return this.errors = Object.keys(this.errors).reduce((i, d) => ({ ...i, ...t.length > 0 && !t.includes(d) ? { [d]: this.errors[d] } : {} }), {}), this.hasErrors = Object.keys(this.errors).length > 0, this;
  }, submit(...t) {
    let i = typeof t[0] == "object", d = i ? t[0].method : t[0], p = i ? t[0].url : t[1], u = (i ? t[1] : t[2]) ?? {}, F = m(this.data()), T = { ...u, onCancelToken: (c) => {
      if (l = c, u.onCancelToken) return u.onCancelToken(c);
    }, onBefore: (c) => {
      if (this.wasSuccessful = false, this.recentlySuccessful = false, clearTimeout(f), u.onBefore) return u.onBefore(c);
    }, onStart: (c) => {
      if (this.processing = true, u.onStart) return u.onStart(c);
    }, onProgress: (c) => {
      if (this.progress = c, u.onProgress) return u.onProgress(c);
    }, onSuccess: async (c) => {
      this.processing = false, this.progress = null, this.clearErrors(), this.wasSuccessful = true, this.recentlySuccessful = true, f = setTimeout(() => this.recentlySuccessful = false, 2e3);
      let w = u.onSuccess ? await u.onSuccess(c) : null;
      return a = cloneDeep(this.data()), this.isDirty = false, w;
    }, onError: (c) => {
      if (this.processing = false, this.progress = null, this.clearErrors().setError(c), u.onError) return u.onError(c);
    }, onCancel: () => {
      if (this.processing = false, this.progress = null, u.onCancel) return u.onCancel();
    }, onFinish: (c) => {
      if (this.processing = false, this.progress = null, l = null, u.onFinish) return u.onFinish(c);
    } };
    d === "delete" ? Wr.delete(p, { ...T, data: F }) : Wr[d](p, F, T);
  }, get(t, i) {
    this.submit("get", t, i);
  }, post(t, i) {
    this.submit("post", t, i);
  }, put(t, i) {
    this.submit("put", t, i);
  }, patch(t, i) {
    this.submit("patch", t, i);
  }, delete(t, i) {
    this.submit("delete", t, i);
  }, cancel() {
    l && l.cancel();
  }, __rememberable: n === null, __remember() {
    return { data: this.data(), errors: this.errors };
  }, __restore(t) {
    Object.assign(this, t.data), this.setError(t.errors);
  } });
  return watch(v, (t) => {
    v.isDirty = !isEqual(v.data(), a), n && Wr.remember(cloneDeep(t.__remember()), n);
  }, { immediate: true, deep: true }), v;
}
var y = ref(null);
var h2 = ref(null);
var I = shallowRef(null);
var A = ref(null);
var E = null;
var ie = defineComponent({ name: "Inertia", props: { initialPage: { type: Object, required: true }, initialComponent: { type: Object, required: false }, resolveComponent: { type: Function, required: false }, titleCallback: { type: Function, required: false, default: (e) => e }, onHeadUpdate: { type: Function, required: false, default: () => () => {
} } }, setup({ initialPage: e, initialComponent: r, resolveComponent: n, titleCallback: s, onHeadUpdate: o }) {
  y.value = r ? markRaw(r) : null, h2.value = e, A.value = null;
  let a = typeof window > "u";
  return E = Ie(a, s, o), a || (Wr.init({ initialPage: e, resolveComponent: n, swapComponent: async (l) => {
    y.value = markRaw(l.component), h2.value = l.page, A.value = l.preserveState ? A.value : Date.now();
  } }), Wr.on("navigate", () => E.forceUpdate())), () => {
    if (y.value) {
      y.value.inheritAttrs = !!y.value.inheritAttrs;
      let l = h(y.value, { ...h2.value.props, key: A.value });
      return I.value && (y.value.layout = I.value, I.value = null), y.value.layout ? typeof y.value.layout == "function" ? y.value.layout(h, l) : (Array.isArray(y.value.layout) ? y.value.layout : [y.value.layout]).concat(l).reverse().reduce((f, m) => (m.inheritAttrs = !!m.inheritAttrs, h(m, { ...h2.value.props }, () => f))) : l;
    }
  };
} });
var B = ie;
var q = { install(e) {
  Wr.form = x, Object.defineProperty(e.config.globalProperties, "$inertia", { get: () => Wr }), Object.defineProperty(e.config.globalProperties, "$page", { get: () => h2.value }), Object.defineProperty(e.config.globalProperties, "$headManager", { get: () => E }), e.mixin(V);
} };
function ae() {
  return reactive({ props: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.props;
  }), url: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.url;
  }), component: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.component;
  }), version: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.version;
  }), clearHistory: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.clearHistory;
  }), deferredProps: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.deferredProps;
  }), mergeProps: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.mergeProps;
  }), deepMergeProps: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.deepMergeProps;
  }), rememberedState: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.rememberedState;
  }), encryptHistory: computed(() => {
    var _a;
    return (_a = h2.value) == null ? void 0 : _a.encryptHistory;
  }) });
}
async function K({ id: e = "app", resolve: r, setup: n, title: s, progress: o = {}, page: a, render: l }) {
  let f = typeof window > "u", m = f ? null : document.getElementById(e), v = a || JSON.parse(m.dataset.page), t = (p) => Promise.resolve(r(p)).then((u) => u.default || u), i = [], d = await Promise.all([t(v.component), Wr.decryptHistory().catch(() => {
  })]).then(([p]) => n({ el: m, App: B, props: { initialPage: v, initialComponent: p, resolveComponent: t, titleCallback: s, onHeadUpdate: f ? (u) => i = u : null }, plugin: q }));
  if (!f && o && Me(o), f) {
    let p = await l(createSSRApp({ render: () => h("div", { id: e, "data-page": JSON.stringify(v), innerHTML: d ? l(d) : "" }) }));
    return { head: i, body: p };
  }
}
var me = defineComponent({ name: "Deferred", props: { data: { type: [String, Array], required: true } }, render() {
  let e = Array.isArray(this.$props.data) ? this.$props.data : [this.$props.data];
  if (!this.$slots.fallback) throw new Error("`<Deferred>` requires a `<template #fallback>` slot");
  return e.every((r) => this.$page.props[r] !== void 0) ? this.$slots.default() : this.$slots.fallback();
} });
var he = defineComponent({ props: { title: { type: String, required: false } }, data() {
  return { provider: this.$headManager.createProvider() };
}, beforeUnmount() {
  this.provider.disconnect();
}, methods: { isUnaryTag(e) {
  return ["area", "base", "br", "col", "embed", "hr", "img", "input", "keygen", "link", "meta", "param", "source", "track", "wbr"].indexOf(e.type) > -1;
}, renderTagStart(e) {
  e.props = e.props || {}, e.props.inertia = e.props["head-key"] !== void 0 ? e.props["head-key"] : "";
  let r = Object.keys(e.props).reduce((n, s) => {
    let o = e.props[s];
    return ["key", "head-key"].includes(s) ? n : o === "" ? n + ` ${s}` : n + ` ${s}="${o}"`;
  }, "");
  return `<${e.type}${r}>`;
}, renderTagChildren(e) {
  return typeof e.children == "string" ? e.children : e.children.reduce((r, n) => r + this.renderTag(n), "");
}, isFunctionNode(e) {
  return typeof e.type == "function";
}, isComponentNode(e) {
  return typeof e.type == "object";
}, isCommentNode(e) {
  return /(comment|cmt)/i.test(e.type.toString());
}, isFragmentNode(e) {
  return /(fragment|fgt|symbol\(\))/i.test(e.type.toString());
}, isTextNode(e) {
  return /(text|txt)/i.test(e.type.toString());
}, renderTag(e) {
  if (this.isTextNode(e)) return e.children;
  if (this.isFragmentNode(e)) return "";
  if (this.isCommentNode(e)) return "";
  let r = this.renderTagStart(e);
  return e.children && (r += this.renderTagChildren(e)), this.isUnaryTag(e) || (r += `</${e.type}>`), r;
}, addTitleElement(e) {
  return this.title && !e.find((r) => r.startsWith("<title")) && e.push(`<title inertia>${this.title}</title>`), e;
}, renderNodes(e) {
  return this.addTitleElement(e.flatMap((r) => this.resolveNode(r)).map((r) => this.renderTag(r)).filter((r) => r));
}, resolveNode(e) {
  return this.isFunctionNode(e) ? this.resolveNode(e.type()) : this.isComponentNode(e) ? (console.warn("Using components in the <Head> component is not supported."), []) : this.isTextNode(e) && e.children ? e : this.isFragmentNode(e) && e.children ? e.children.flatMap((r) => this.resolveNode(r)) : this.isCommentNode(e) ? [] : e;
} }, render() {
  this.provider.update(this.renderNodes(this.$slots.default ? this.$slots.default() : []));
} });
var ge = he;
var Te = defineComponent({ name: "Link", props: { as: { type: String, default: "a" }, data: { type: Object, default: () => ({}) }, href: { type: [String, Object], required: true }, method: { type: String, default: "get" }, replace: { type: Boolean, default: false }, preserveScroll: { type: Boolean, default: false }, preserveState: { type: Boolean, default: null }, only: { type: Array, default: () => [] }, except: { type: Array, default: () => [] }, headers: { type: Object, default: () => ({}) }, queryStringArrayFormat: { type: String, default: "brackets" }, async: { type: Boolean, default: false }, prefetch: { type: [Boolean, String, Array], default: false }, cacheFor: { type: [Number, String, Array], default: 0 }, onStart: { type: Function, default: (e) => {
} }, onProgress: { type: Function, default: () => {
} }, onFinish: { type: Function, default: () => {
} }, onBefore: { type: Function, default: () => {
} }, onCancel: { type: Function, default: () => {
} }, onSuccess: { type: Function, default: () => {
} }, onError: { type: Function, default: () => {
} }, onCancelToken: { type: Function, default: () => {
} } }, setup(e, { slots: r, attrs: n }) {
  let s = ref(0), o = ref(null), a = e.prefetch === true ? ["hover"] : e.prefetch === false ? [] : Array.isArray(e.prefetch) ? e.prefetch : [e.prefetch], l = e.cacheFor !== 0 ? e.cacheFor : a.length === 1 && a[0] === "click" ? 0 : 3e4;
  onMounted(() => {
    a.includes("mount") && F();
  }), onUnmounted(() => {
    clearTimeout(o.value);
  });
  let f = typeof e.href == "object" ? e.href.method : e.method.toLowerCase(), m = f !== "get" ? "button" : e.as.toLowerCase(), v = computed(() => qe(f, typeof e.href == "object" ? e.href.url : e.href || "", e.data, e.queryStringArrayFormat)), t = computed(() => v.value[0]), i = computed(() => v.value[1]), d = computed(() => ({ a: { href: t.value }, button: { type: "button" } })), p = { data: i.value, method: f, replace: e.replace, preserveScroll: e.preserveScroll, preserveState: e.preserveState ?? f !== "get", only: e.only, except: e.except, headers: e.headers, async: e.async }, u = { ...p, onCancelToken: e.onCancelToken, onBefore: e.onBefore, onStart: (g) => {
    s.value++, e.onStart(g);
  }, onProgress: e.onProgress, onFinish: (g) => {
    s.value--, e.onFinish(g);
  }, onCancel: e.onCancel, onSuccess: e.onSuccess, onError: e.onError }, F = () => {
    Wr.prefetch(t.value, p, { cacheFor: l });
  }, T = { onClick: (g) => {
    Ne(g) && (g.preventDefault(), Wr.visit(t.value, u));
  } }, c = { onMouseenter: () => {
    o.value = setTimeout(() => {
      F();
    }, 75);
  }, onMouseleave: () => {
    clearTimeout(o.value);
  }, onClick: T.onClick }, w = { onMousedown: (g) => {
    Ne(g) && (g.preventDefault(), F());
  }, onMouseup: (g) => {
    g.preventDefault(), Wr.visit(t.value, u);
  }, onClick: (g) => {
    Ne(g) && g.preventDefault();
  } };
  return () => h(m, { ...n, ...d.value[m] || {}, "data-loading": s.value > 0 ? "" : void 0, ...a.includes("hover") ? c : a.includes("click") ? w : T }, r);
} });
var Se = Te;
function W(e, r = {}, n = { keepAlive: false, autoStart: true }) {
  let { stop: s, start: o } = Wr.poll(e, r, { ...n, autoStart: false });
  return onMounted(() => {
    (n.autoStart ?? true) && o();
  }), onUnmounted(() => {
    s();
  }), { stop: s, start: o };
}
function J(e = {}) {
  let r = ref(false), n = ref(null), s = ref(false), o = typeof window > "u" ? null : Wr.getCached(window.location.pathname, e), a = typeof window > "u" ? null : Wr.getPrefetching(window.location.pathname, e);
  n.value = (o == null ? void 0 : o.staleTimestamp) || null, r.value = a !== null, s.value = o !== null;
  let l, f;
  return onMounted(() => {
    f = Wr.on("prefetching", (m) => {
      m.detail.visit.url.pathname === window.location.pathname && (r.value = true);
    }), l = Wr.on("prefetched", (m) => {
      m.detail.visit.url.pathname === window.location.pathname && (r.value = false, s.value = true);
    });
  }), onUnmounted(() => {
    l(), f();
  }), { lastUpdatedAt: n, isPrefetching: r, isPrefetched: s, flush: () => Wr.flush(window.location.pathname, e) };
}
function Q(e, r) {
  if (typeof e == "object" && e !== null && e.__rememberable === false) return e;
  let n = Wr.restore(r), s = isReactive(e) ? reactive : ref, o = typeof e.__remember == "function" && typeof e.__restore == "function", a = s(n === void 0 ? e : o ? e.__restore(n) : n);
  return watch(a, (l) => {
    Wr.remember(cloneDeep(o ? e.__remember() : l), r);
  }, { immediate: true, deep: true }), a;
}
var Re = defineComponent({ name: "WhenVisible", props: { data: { type: [String, Array] }, params: { type: Object }, buffer: { type: Number, default: 0 }, as: { type: String, default: "div" }, always: { type: Boolean, default: false } }, data() {
  return { loaded: false, fetching: false, observer: null };
}, unmounted() {
  var _a;
  (_a = this.observer) == null ? void 0 : _a.disconnect();
}, mounted() {
  this.observer = new IntersectionObserver((e) => {
    if (!e[0].isIntersecting || (this.$props.always || this.observer.disconnect(), this.fetching)) return;
    this.fetching = true;
    let r = this.getReloadParams();
    Wr.reload({ ...r, onStart: (n) => {
      var _a;
      this.fetching = true, (_a = r.onStart) == null ? void 0 : _a.call(r, n);
    }, onFinish: (n) => {
      var _a;
      this.loaded = true, this.fetching = false, (_a = r.onFinish) == null ? void 0 : _a.call(r, n);
    } });
  }, { rootMargin: `${this.$props.buffer}px` }), this.observer.observe(this.$el.nextSibling);
}, methods: { getReloadParams() {
  if (this.$props.data) return { only: Array.isArray(this.$props.data) ? this.$props.data : [this.$props.data] };
  if (!this.$props.params) throw new Error("You must provide either a `data` or `params` prop.");
  return this.$props.params;
} }, render() {
  let e = [];
  return (this.$props.always || !this.loaded) && e.push(h(this.$props.as)), this.loaded ? this.$slots.default && e.push(this.$slots.default()) : e.push(this.$slots.fallback ? this.$slots.fallback() : null), e;
} });
export {
  me as Deferred,
  ge as Head,
  Se as Link,
  Re as WhenVisible,
  K as createInertiaApp,
  Wr as router,
  x as useForm,
  ae as usePage,
  W as usePoll,
  J as usePrefetch,
  Q as useRemember
};
//# sourceMappingURL=@inertiajs_vue3.js.map

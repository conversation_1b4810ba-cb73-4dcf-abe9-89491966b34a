{"version": 3, "file": "ColumnFiltering.js", "sources": ["../../../src/features/ColumnFiltering.ts"], "sourcesContent": ["import { RowModel } from '..'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  FilterFns,\n  FilterMeta,\n  OnChangeFn,\n  Row,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface ColumnFiltersTableState {\n  columnFilters: ColumnFiltersState\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  filterFn: FilterFn<TData>\n  id: string\n  resolvedValue: unknown\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n  resolveFilterValue?: TransformFilterValueFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface ColumnFiltersColumnDef<TData extends RowData> {\n  /**\n   * Enables/disables the **column** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilter?: boolean\n  /**\n   * The filter function to use with this column. Can be the name of a built-in filter function or a custom filter function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFn?: FilterFnOption<TData>\n}\n\nexport interface ColumnFiltersColumn<TData extends RowData> {\n  /**\n   * Returns an automatically calculated filter function for the column based off of the columns first known value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be **column** filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getcanfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getCanFilter: () => boolean\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the columnId specified.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the index (including `-1`) of the column filter in the table's `state.columnFilters` array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilterindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterIndex: () => number\n  /**\n   * Returns the current filter value for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilterValue: () => unknown\n  /**\n   * Returns whether or not the column is currently filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getisfiltered)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getIsFiltered: () => boolean\n  /**\n   * A function that sets the current filter value for the column. You can pass it a value or an updater function for immutability-safe operations on existing values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setFilterValue: (updater: Updater<any>) => void\n}\n\nexport interface ColumnFiltersRow<TData extends RowData> {\n  /**\n   * The column filters map for the row. This object tracks whether a row is passing/failing specific filters by their column ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFilters: Record<string, boolean>\n  /**\n   * The column filters meta map for the row. This object tracks any filter meta for a row as optionally provided during the filtering process.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#columnfiltersmeta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface ColumnFiltersOptionsBase<TData extends RowData> {\n  /**\n   * Enables/disables **column** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablecolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableColumnFilters?: boolean\n  /**\n   * Enables/disables all filtering for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#enablefilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  enableFilters?: boolean\n  /**\n   * By default, filtering is done from parent rows down (so if a parent row is filtered out, all of its children will be filtered out as well). Setting this option to `true` will cause filtering to be done from leaf rows up (which means parent rows will be included so long as one of their child or grand-child rows is also included).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#filterfromleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  filterFromLeafRows?: boolean\n  /**\n   * If provided, this function is called **once** per table and should return a **new function** which will calculate and return the row model for the table when it's filtered.\n   * - For server-side filtering, this function is unnecessary and can be ignored since the server should already return the filtered row model.\n   * - For client-side filtering, this function is required. A default implementation is provided via any table adapter's `{ getFilteredRowModel }` export.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Disables the `getFilteredRowModel` from being used to filter data. This may be useful if your table needs to dynamically support both client-side and server-side filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#manualfiltering)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  manualFiltering?: boolean\n  /**\n   * By default, filtering is done for all rows (max depth of 100), no matter if they are root level parent rows or the child leaf rows of a parent row. Setting this option to `0` will cause filtering to only be applied to the root level parent rows, with all sub-rows remaining unfiltered. Similarly, setting this option to `1` will cause filtering to only be applied to child leaf rows 1 level deep, and so on.\n\n   * This is useful for situations where you want a row's entire child hierarchy to be visible regardless of the applied filter.\n    * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#maxleafrowfilterdepth)\n    * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  maxLeafRowFilterDepth?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnFilters` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#oncolumnfilterschange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface ColumnFiltersOptions<TData extends RowData>\n  extends ColumnFiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface ColumnFiltersInstance<TData extends RowData> {\n  _getFilteredRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getFilteredRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#getprefilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  getPreFilteredRowModel: () => RowModel<TData>\n  /**\n   * Resets the **columnFilters** state to `initialState.columnFilters`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetColumnFilters: (defaultState?: boolean) => void\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnFilters` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-filtering#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-filtering)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const ColumnFiltering: TableFeature = {\n  getDefaultColumnDef: <\n    TData extends RowData,\n  >(): ColumnFiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): ColumnFiltersTableState => {\n    return {\n      columnFilters: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnFiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n    } as ColumnFiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return filterFns.includesString\n      }\n\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange\n      }\n\n      if (typeof value === 'boolean') {\n        return filterFns.equals\n      }\n\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals\n      }\n\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes\n      }\n\n      return filterFns.weakEquals\n    }\n    column.getFilterFn = () => {\n      return isFunction(column.columnDef.filterFn)\n        ? column.columnDef.filterFn\n        : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n    }\n    column.getCanFilter = () => {\n      return (\n        (column.columnDef.enableColumnFilter ?? true) &&\n        (table.options.enableColumnFilters ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsFiltered = () => column.getFilterIndex() > -1\n\n    column.getFilterValue = () =>\n      table.getState().columnFilters?.find(d => d.id === column.id)?.value\n\n    column.getFilterIndex = () =>\n      table.getState().columnFilters?.findIndex(d => d.id === column.id) ?? -1\n\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn()\n        const previousFilter = old?.find(d => d.id === column.id)\n\n        const newFilter = functionalUpdate(\n          value,\n          previousFilter ? previousFilter.value : undefined\n        )\n\n        //\n        if (\n          shouldAutoRemoveFilter(filterFn as FilterFn<TData>, newFilter, column)\n        ) {\n          return old?.filter(d => d.id !== column.id) ?? []\n        }\n\n        const newFilterObj = { id: column.id, value: newFilter }\n\n        if (previousFilter) {\n          return (\n            old?.map(d => {\n              if (d.id === column.id) {\n                return newFilterObj\n              }\n              return d\n            }) ?? []\n          )\n        }\n\n        if (old?.length) {\n          return [...old, newFilterObj]\n        }\n\n        return [newFilterObj]\n      })\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    _table: Table<TData>\n  ): void => {\n    row.columnFilters = {}\n    row.columnFiltersMeta = {}\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnFilters = (updater: Updater<ColumnFiltersState>) => {\n      const leafColumns = table.getAllLeafColumns()\n\n      const updateFn = (old: ColumnFiltersState) => {\n        return functionalUpdate(updater, old)?.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id)\n\n          if (column) {\n            const filterFn = column.getFilterFn()\n\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false\n            }\n          }\n\n          return true\n        })\n      }\n\n      table.options.onColumnFiltersChange?.(updateFn)\n    }\n\n    table.resetColumnFilters = defaultState => {\n      table.setColumnFilters(\n        defaultState ? [] : table.initialState?.columnFilters ?? []\n      )\n    }\n\n    table.getPreFilteredRowModel = () => table.getCoreRowModel()\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n      }\n\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getFilteredRowModel()\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n"], "names": ["ColumnFiltering", "getDefaultColumnDef", "filterFn", "getInitialState", "state", "columnFilters", "getDefaultOptions", "table", "onColumnFiltersChange", "makeStateUpdater", "filterFromLeafRows", "maxLeafRowFilterDepth", "createColumn", "column", "getAutoFilterFn", "firstRow", "getCoreRowModel", "flatRows", "value", "getValue", "id", "filterFns", "includesString", "inNumberRange", "equals", "Array", "isArray", "arrIncludes", "weakEquals", "getFilterFn", "_table$options$filter", "_table$options$filter2", "isFunction", "columnDef", "options", "getCanFilter", "_column$columnDef$ena", "_table$options$enable", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "accessorFn", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "getState", "find", "d", "_table$getState$colum2", "_table$getState$colum3", "findIndex", "setFilterValue", "setColumnFilters", "old", "previousFilter", "newFilter", "functionalUpdate", "undefined", "shouldAutoRemoveFilter", "_old$filter", "filter", "newFilterObj", "_old$map", "map", "length", "createRow", "row", "_table", "columnFiltersMeta", "createTable", "updater", "leafColumns", "getAllLeafColumns", "updateFn", "_functionalUpdate", "resetColumnFilters", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getPreFilteredRowModel", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "autoRemove"], "mappings": ";;;;;;;;;;;;;;;AAgPA;;AAEO,MAAMA,eAA6B,GAAG;EAC3CC,mBAAmB,EAAEA,MAEiB;IACpC,OAAO;AACLC,MAAAA,QAAQ,EAAE,MAAA;KACX,CAAA;GACF;EAEDC,eAAe,EAAGC,KAAK,IAA8B;IACnD,OAAO;AACLC,MAAAA,aAAa,EAAE,EAAE;MACjB,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACa;IAChC,OAAO;AACLC,MAAAA,qBAAqB,EAAEC,sBAAgB,CAAC,eAAe,EAAEF,KAAK,CAAC;AAC/DG,MAAAA,kBAAkB,EAAE,KAAK;AACzBC,MAAAA,qBAAqB,EAAE,GAAA;KACxB,CAAA;GACF;AAEDC,EAAAA,YAAY,EAAEA,CACZC,MAA8B,EAC9BN,KAAmB,KACV;IACTM,MAAM,CAACC,eAAe,GAAG,MAAM;MAC7B,MAAMC,QAAQ,GAAGR,KAAK,CAACS,eAAe,EAAE,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAA;MAEpD,MAAMC,KAAK,GAAGH,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAEI,QAAQ,CAACN,MAAM,CAACO,EAAE,CAAC,CAAA;AAE3C,MAAA,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOG,mBAAS,CAACC,cAAc,CAAA;AACjC,OAAA;AAEA,MAAA,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOG,mBAAS,CAACE,aAAa,CAAA;AAChC,OAAA;AAEA,MAAA,IAAI,OAAOL,KAAK,KAAK,SAAS,EAAE;QAC9B,OAAOG,mBAAS,CAACG,MAAM,CAAA;AACzB,OAAA;MAEA,IAAIN,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC/C,OAAOG,mBAAS,CAACG,MAAM,CAAA;AACzB,OAAA;AAEA,MAAA,IAAIC,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,EAAE;QACxB,OAAOG,mBAAS,CAACM,WAAW,CAAA;AAC9B,OAAA;MAEA,OAAON,mBAAS,CAACO,UAAU,CAAA;KAC5B,CAAA;IACDf,MAAM,CAACgB,WAAW,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;AACzB,MAAA,OAAOC,gBAAU,CAACnB,MAAM,CAACoB,SAAS,CAAC/B,QAAQ,CAAC,GACxCW,MAAM,CAACoB,SAAS,CAAC/B,QAAQ,GACzBW,MAAM,CAACoB,SAAS,CAAC/B,QAAQ,KAAK,MAAM,GAClCW,MAAM,CAACC,eAAe,EAAE;AACxB,MAAA,CAAAgB,qBAAA,GAAA,CAAAC,sBAAA,GACAxB,KAAK,CAAC2B,OAAO,CAACb,SAAS,KAAA,IAAA,GAAA,KAAA,CAAA,GAAvBU,sBAAA,CAA0BlB,MAAM,CAACoB,SAAS,CAAC/B,QAAQ,CAAW,KAAA4B,IAAAA,GAAAA,qBAAA,GAC9DT,mBAAS,CAACR,MAAM,CAACoB,SAAS,CAAC/B,QAAQ,CAAoB,CAAA;KAC9D,CAAA;IACDW,MAAM,CAACsB,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,CAAA;AAC1B,MAAA,OACE,EAAAF,qBAAA,GAACvB,MAAM,CAACoB,SAAS,CAACM,kBAAkB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,OAAAC,qBAAA,GAC3C9B,KAAK,CAAC2B,OAAO,CAACM,mBAAmB,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,CAAC,KAAAC,CAAAA,sBAAA,GAC1C/B,KAAK,CAAC2B,OAAO,CAACO,aAAa,YAAAH,sBAAA,GAAI,IAAI,CAAC,IACrC,CAAC,CAACzB,MAAM,CAAC6B,UAAU,CAAA;KAEtB,CAAA;IAED7B,MAAM,CAAC8B,aAAa,GAAG,MAAM9B,MAAM,CAAC+B,cAAc,EAAE,GAAG,CAAC,CAAC,CAAA;IAEzD/B,MAAM,CAACgC,cAAc,GAAG,MAAA;AAAA,MAAA,IAAAC,qBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,qBAAA,GACtBvC,KAAK,CAACwC,QAAQ,EAAE,CAAC1C,aAAa,KAAA,IAAA,IAAA,CAAAyC,qBAAA,GAA9BA,qBAAA,CAAgCE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAKP,MAAM,CAACO,EAAE,CAAC,KAA7D0B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAA+D5B,KAAK,CAAA;AAAA,KAAA,CAAA;IAEtEL,MAAM,CAAC+B,cAAc,GAAG,MAAA;MAAA,IAAAM,sBAAA,EAAAC,sBAAA,CAAA;AAAA,MAAA,OAAA,CAAAD,sBAAA,GAAA,CAAAC,sBAAA,GACtB5C,KAAK,CAACwC,QAAQ,EAAE,CAAC1C,aAAa,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9B8C,sBAAA,CAAgCC,SAAS,CAACH,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAKP,MAAM,CAACO,EAAE,CAAC,KAAA,IAAA,GAAA8B,sBAAA,GAAI,CAAC,CAAC,CAAA;AAAA,KAAA,CAAA;AAE1ErC,IAAAA,MAAM,CAACwC,cAAc,GAAGnC,KAAK,IAAI;AAC/BX,MAAAA,KAAK,CAAC+C,gBAAgB,CAACC,GAAG,IAAI;AAC5B,QAAA,MAAMrD,QAAQ,GAAGW,MAAM,CAACgB,WAAW,EAAE,CAAA;AACrC,QAAA,MAAM2B,cAAc,GAAGD,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAKP,MAAM,CAACO,EAAE,CAAC,CAAA;AAEzD,QAAA,MAAMqC,SAAS,GAAGC,sBAAgB,CAChCxC,KAAK,EACLsC,cAAc,GAAGA,cAAc,CAACtC,KAAK,GAAGyC,SAC1C,CAAC,CAAA;;AAED;QACA,IACEC,sBAAsB,CAAC1D,QAAQ,EAAqBuD,SAAS,EAAE5C,MAAM,CAAC,EACtE;AAAA,UAAA,IAAAgD,WAAA,CAAA;UACA,OAAAA,CAAAA,WAAA,GAAON,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEO,MAAM,CAACb,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAKP,MAAM,CAACO,EAAE,CAAC,KAAA,IAAA,GAAAyC,WAAA,GAAI,EAAE,CAAA;AACnD,SAAA;AAEA,QAAA,MAAME,YAAY,GAAG;UAAE3C,EAAE,EAAEP,MAAM,CAACO,EAAE;AAAEF,UAAAA,KAAK,EAAEuC,SAAAA;SAAW,CAAA;AAExD,QAAA,IAAID,cAAc,EAAE;AAAA,UAAA,IAAAQ,QAAA,CAAA;UAClB,OAAAA,CAAAA,QAAA,GACET,GAAG,IAAA,IAAA,GAAA,KAAA,CAAA,GAAHA,GAAG,CAAEU,GAAG,CAAChB,CAAC,IAAI;AACZ,YAAA,IAAIA,CAAC,CAAC7B,EAAE,KAAKP,MAAM,CAACO,EAAE,EAAE;AACtB,cAAA,OAAO2C,YAAY,CAAA;AACrB,aAAA;AACA,YAAA,OAAOd,CAAC,CAAA;AACV,WAAC,CAAC,KAAA,IAAA,GAAAe,QAAA,GAAI,EAAE,CAAA;AAEZ,SAAA;AAEA,QAAA,IAAIT,GAAG,IAAA,IAAA,IAAHA,GAAG,CAAEW,MAAM,EAAE;AACf,UAAA,OAAO,CAAC,GAAGX,GAAG,EAAEQ,YAAY,CAAC,CAAA;AAC/B,SAAA;QAEA,OAAO,CAACA,YAAY,CAAC,CAAA;AACvB,OAAC,CAAC,CAAA;KACH,CAAA;GACF;AAEDI,EAAAA,SAAS,EAAEA,CACTC,GAAe,EACfC,MAAoB,KACX;AACTD,IAAAA,GAAG,CAAC/D,aAAa,GAAG,EAAE,CAAA;AACtB+D,IAAAA,GAAG,CAACE,iBAAiB,GAAG,EAAE,CAAA;GAC3B;EAEDC,WAAW,EAA0BhE,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAAC+C,gBAAgB,GAAIkB,OAAoC,IAAK;AACjE,MAAA,MAAMC,WAAW,GAAGlE,KAAK,CAACmE,iBAAiB,EAAE,CAAA;MAE7C,MAAMC,QAAQ,GAAIpB,GAAuB,IAAK;AAAA,QAAA,IAAAqB,iBAAA,CAAA;AAC5C,QAAA,OAAA,CAAAA,iBAAA,GAAOlB,sBAAgB,CAACc,OAAO,EAAEjB,GAAG,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAA9BqB,iBAAA,CAAgCd,MAAM,CAACA,MAAM,IAAI;AACtD,UAAA,MAAMjD,MAAM,GAAG4D,WAAW,CAACzB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAK0C,MAAM,CAAC1C,EAAE,CAAC,CAAA;AAExD,UAAA,IAAIP,MAAM,EAAE;AACV,YAAA,MAAMX,QAAQ,GAAGW,MAAM,CAACgB,WAAW,EAAE,CAAA;YAErC,IAAI+B,sBAAsB,CAAC1D,QAAQ,EAAE4D,MAAM,CAAC5C,KAAK,EAAEL,MAAM,CAAC,EAAE;AAC1D,cAAA,OAAO,KAAK,CAAA;AACd,aAAA;AACF,WAAA;AAEA,UAAA,OAAO,IAAI,CAAA;AACb,SAAC,CAAC,CAAA;OACH,CAAA;AAEDN,MAAAA,KAAK,CAAC2B,OAAO,CAAC1B,qBAAqB,IAAnCD,IAAAA,IAAAA,KAAK,CAAC2B,OAAO,CAAC1B,qBAAqB,CAAGmE,QAAQ,CAAC,CAAA;KAChD,CAAA;AAEDpE,IAAAA,KAAK,CAACsE,kBAAkB,GAAGC,YAAY,IAAI;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MACzCzE,KAAK,CAAC+C,gBAAgB,CACpBwB,YAAY,GAAG,EAAE,GAAA,CAAAC,qBAAA,GAAA,CAAAC,mBAAA,GAAGzE,KAAK,CAAC0E,YAAY,qBAAlBD,mBAAA,CAAoB3E,aAAa,KAAA0E,IAAAA,GAAAA,qBAAA,GAAI,EAC3D,CAAC,CAAA;KACF,CAAA;IAEDxE,KAAK,CAAC2E,sBAAsB,GAAG,MAAM3E,KAAK,CAACS,eAAe,EAAE,CAAA;IAC5DT,KAAK,CAAC4E,mBAAmB,GAAG,MAAM;MAChC,IAAI,CAAC5E,KAAK,CAAC6E,oBAAoB,IAAI7E,KAAK,CAAC2B,OAAO,CAACiD,mBAAmB,EAAE;QACpE5E,KAAK,CAAC6E,oBAAoB,GAAG7E,KAAK,CAAC2B,OAAO,CAACiD,mBAAmB,CAAC5E,KAAK,CAAC,CAAA;AACvE,OAAA;MAEA,IAAIA,KAAK,CAAC2B,OAAO,CAACmD,eAAe,IAAI,CAAC9E,KAAK,CAAC6E,oBAAoB,EAAE;AAChE,QAAA,OAAO7E,KAAK,CAAC2E,sBAAsB,EAAE,CAAA;AACvC,OAAA;AAEA,MAAA,OAAO3E,KAAK,CAAC6E,oBAAoB,EAAE,CAAA;KACpC,CAAA;AACH,GAAA;AACF,EAAC;AAEM,SAASxB,sBAAsBA,CACpC1D,QAA0B,EAC1BgB,KAAW,EACXL,MAA+B,EAC/B;AACA,EAAA,OACE,CAACX,QAAQ,IAAIA,QAAQ,CAACoF,UAAU,GAC5BpF,QAAQ,CAACoF,UAAU,CAACpE,KAAK,EAAEL,MAAM,CAAC,GAClC,KAAK,KACT,OAAOK,KAAK,KAAK,WAAW,IAC3B,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAM,CAAA;AAEzC;;;;;"}
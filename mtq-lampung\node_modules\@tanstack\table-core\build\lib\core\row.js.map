{"version": 3, "file": "row.js", "sources": ["../../../src/core/row.ts"], "sourcesContent": ["import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, getMemoOptions, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  _uniqueValuesCache: Record<string, unknown>\n  _valuesCache: Record<string, unknown>\n  /**\n   * The depth of the row (if nested or grouped) relative to the root row array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  depth: number\n  /**\n   * Returns all of the cells for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getallcells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getAllCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns the leaf rows for the row, not including any parent rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getLeafRows: () => Row<TData>[]\n  /**\n   * Returns the parent row for the row, if it exists.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRow: () => Row<TData> | undefined\n  /**\n   * Returns the parent rows for the row, all the way up to a root row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRows: () => Row<TData>[]\n  /**\n   * Returns a unique array of values from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getuniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  /**\n   * Returns the value from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getValue: <TValue>(columnId: string) => TValue\n  /**\n   * The resolved unique identifier for the row resolved via the `options.getRowId` option. Defaults to the row's index (or relative index if it is a subRow).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  id: string\n  /**\n   * The index of the row within its parent array (or the root data array).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  index: number\n  /**\n   * The original row object provided to the table. If the row is a grouped row, the original row object will be the first original in the group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#original)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  original: TData\n  /**\n   * An array of the original subRows as returned by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#originalsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  originalSubRows?: TData[]\n  /**\n   * If nested, this row's parent row id.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#parentid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  parentId?: string\n  /**\n   * Renders the value for the row in a given columnId the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  renderValue: <TValue>(columnId: string) => TValue\n  /**\n   * An array of subRows for the row as returned and created by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#subrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  subRows: Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () =>\n      row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCells')\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce(\n          (acc, cell) => {\n            acc[cell.column.id] = cell\n            return acc\n          },\n          {} as Record<string, Cell<TData, unknown>>\n        )\n      },\n      getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId')\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    feature?.createRow?.(row as Row<TData>, table)\n  }\n\n  return row as Row<TData>\n}\n"], "names": ["createRow", "table", "id", "original", "rowIndex", "depth", "subRows", "parentId", "row", "index", "_valuesCache", "_uniqueValuesCache", "getValue", "columnId", "hasOwnProperty", "column", "getColumn", "accessorFn", "undefined", "getUniqueValues", "columnDef", "renderValue", "_row$getValue", "options", "renderFallbackValue", "getLeafRows", "flattenBy", "d", "getParentRow", "getRow", "getParentRows", "parentRows", "currentRow", "parentRow", "push", "reverse", "getAllCells", "memo", "getAllLeafColumns", "leafColumns", "map", "createCell", "getMemoOptions", "_getAllCellsByColumnId", "allCells", "reduce", "acc", "cell", "i", "_features", "length", "feature"], "mappings": ";;;;;;;;;;;;;;;MA8FaA,SAAS,GAAGA,CACvBC,KAAmB,EACnBC,EAAU,EACVC,QAAe,EACfC,QAAgB,EAChBC,KAAa,EACbC,OAAsB,EACtBC,QAAiB,KACF;AACf,EAAA,IAAIC,GAAmB,GAAG;IACxBN,EAAE;AACFO,IAAAA,KAAK,EAAEL,QAAQ;IACfD,QAAQ;IACRE,KAAK;IACLE,QAAQ;IACRG,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAEC,QAAQ,IAAI;MACpB,IAAIL,GAAG,CAACE,YAAY,CAACI,cAAc,CAACD,QAAQ,CAAC,EAAE;AAC7C,QAAA,OAAOL,GAAG,CAACE,YAAY,CAACG,QAAQ,CAAC,CAAA;AACnC,OAAA;AAEA,MAAA,MAAME,MAAM,GAAGd,KAAK,CAACe,SAAS,CAACH,QAAQ,CAAC,CAAA;AAExC,MAAA,IAAI,EAACE,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEE,UAAU,CAAE,EAAA;AACvB,QAAA,OAAOC,SAAS,CAAA;AAClB,OAAA;AAEAV,MAAAA,GAAG,CAACE,YAAY,CAACG,QAAQ,CAAC,GAAGE,MAAM,CAACE,UAAU,CAC5CT,GAAG,CAACL,QAAQ,EACZC,QACF,CAAC,CAAA;AAED,MAAA,OAAOI,GAAG,CAACE,YAAY,CAACG,QAAQ,CAAC,CAAA;KAClC;IACDM,eAAe,EAAEN,QAAQ,IAAI;MAC3B,IAAIL,GAAG,CAACG,kBAAkB,CAACG,cAAc,CAACD,QAAQ,CAAC,EAAE;AACnD,QAAA,OAAOL,GAAG,CAACG,kBAAkB,CAACE,QAAQ,CAAC,CAAA;AACzC,OAAA;AAEA,MAAA,MAAME,MAAM,GAAGd,KAAK,CAACe,SAAS,CAACH,QAAQ,CAAC,CAAA;AAExC,MAAA,IAAI,EAACE,MAAM,IAAA,IAAA,IAANA,MAAM,CAAEE,UAAU,CAAE,EAAA;AACvB,QAAA,OAAOC,SAAS,CAAA;AAClB,OAAA;AAEA,MAAA,IAAI,CAACH,MAAM,CAACK,SAAS,CAACD,eAAe,EAAE;AACrCX,QAAAA,GAAG,CAACG,kBAAkB,CAACE,QAAQ,CAAC,GAAG,CAACL,GAAG,CAACI,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAAA;AAC3D,QAAA,OAAOL,GAAG,CAACG,kBAAkB,CAACE,QAAQ,CAAC,CAAA;AACzC,OAAA;AAEAL,MAAAA,GAAG,CAACG,kBAAkB,CAACE,QAAQ,CAAC,GAAGE,MAAM,CAACK,SAAS,CAACD,eAAe,CACjEX,GAAG,CAACL,QAAQ,EACZC,QACF,CAAC,CAAA;AAED,MAAA,OAAOI,GAAG,CAACG,kBAAkB,CAACE,QAAQ,CAAC,CAAA;KACxC;AACDQ,IAAAA,WAAW,EAAER,QAAQ,IAAA;AAAA,MAAA,IAAAS,aAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,aAAA,GACnBd,GAAG,CAACI,QAAQ,CAACC,QAAQ,CAAC,KAAA,IAAA,GAAAS,aAAA,GAAIrB,KAAK,CAACsB,OAAO,CAACC,mBAAmB,CAAA;AAAA,KAAA;AAC7DlB,IAAAA,OAAO,EAAEA,OAAO,IAAPA,IAAAA,GAAAA,OAAO,GAAI,EAAE;AACtBmB,IAAAA,WAAW,EAAEA,MAAMC,eAAS,CAAClB,GAAG,CAACF,OAAO,EAAEqB,CAAC,IAAIA,CAAC,CAACrB,OAAO,CAAC;AACzDsB,IAAAA,YAAY,EAAEA,MACZpB,GAAG,CAACD,QAAQ,GAAGN,KAAK,CAAC4B,MAAM,CAACrB,GAAG,CAACD,QAAQ,EAAE,IAAI,CAAC,GAAGW,SAAS;IAC7DY,aAAa,EAAEA,MAAM;MACnB,IAAIC,UAAwB,GAAG,EAAE,CAAA;MACjC,IAAIC,UAAU,GAAGxB,GAAG,CAAA;AACpB,MAAA,OAAO,IAAI,EAAE;AACX,QAAA,MAAMyB,SAAS,GAAGD,UAAU,CAACJ,YAAY,EAAE,CAAA;QAC3C,IAAI,CAACK,SAAS,EAAE,MAAA;AAChBF,QAAAA,UAAU,CAACG,IAAI,CAACD,SAAS,CAAC,CAAA;AAC1BD,QAAAA,UAAU,GAAGC,SAAS,CAAA;AACxB,OAAA;AACA,MAAA,OAAOF,UAAU,CAACI,OAAO,EAAE,CAAA;KAC5B;AACDC,IAAAA,WAAW,EAAEC,UAAI,CACf,MAAM,CAACpC,KAAK,CAACqC,iBAAiB,EAAE,CAAC,EACjCC,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAACC,GAAG,CAACzB,MAAM,IAAI;QAC/B,OAAO0B,eAAU,CAACxC,KAAK,EAAEO,GAAG,EAAgBO,MAAM,EAAEA,MAAM,CAACb,EAAE,CAAC,CAAA;AAChE,OAAC,CAAC,CAAA;KACH,EACDwC,oBAAc,CAACzC,KAAK,CAACsB,OAAO,EAAE,WAAW,EAAE,aAAa,CAC1D,CAAC;AAEDoB,IAAAA,sBAAsB,EAAEN,UAAI,CAC1B,MAAM,CAAC7B,GAAG,CAAC4B,WAAW,EAAE,CAAC,EACzBQ,QAAQ,IAAI;MACV,OAAOA,QAAQ,CAACC,MAAM,CACpB,CAACC,GAAG,EAAEC,IAAI,KAAK;QACbD,GAAG,CAACC,IAAI,CAAChC,MAAM,CAACb,EAAE,CAAC,GAAG6C,IAAI,CAAA;AAC1B,QAAA,OAAOD,GAAG,CAAA;OACX,EACD,EACF,CAAC,CAAA;KACF,EACDJ,oBAAc,CAACzC,KAAK,CAACsB,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAA;GACD,CAAA;AAED,EAAA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,KAAK,CAACgD,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;AAC/C,IAAA,MAAMG,OAAO,GAAGlD,KAAK,CAACgD,SAAS,CAACD,CAAC,CAAC,CAAA;AAClCG,IAAAA,OAAO,IAAPA,IAAAA,IAAAA,OAAO,CAAEnD,SAAS,IAAlBmD,IAAAA,IAAAA,OAAO,CAAEnD,SAAS,CAAGQ,GAAG,EAAgBP,KAAK,CAAC,CAAA;AAChD,GAAA;AAEA,EAAA,OAAOO,GAAG,CAAA;AACZ;;;;"}
<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Icon from '@/components/Icon.vue'

interface User {
  username: string
  email: string
  created_at: string
}

interface Wilayah {
  nama_wilayah: string
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface Pembayaran {
  nomor_transaksi: string
  status_pembayaran: string
  jumlah_bayar: number
  tanggal_bayar: string | null
}

interface DokumenPeserta {
  jenis_dokumen: string
  nama_file: string
  status_verifikasi: string
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  nomor_peserta: string
  status_pendaftaran: string
  tahun_pendaftaran: number
  created_at: string
  golongan: Golongan
  pembayaran: Pembayaran | null
  dokumen_peserta: DokumenPeserta[]
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  nik: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: string
  alamat: string
  no_telepon: string | null
  email: string
  status_peserta: string
  registration_type: string
  keterangan: string | null
  created_at: string
  user: User
  wilayah: Wilayah
  pendaftaran: Pendaftaran[]
}

const props = defineProps<{
  peserta: Peserta
}>()

function getStatusColor(status: string): string {
  const colors = {
    draft: 'bg-gray-100 text-gray-800',
    submitted: 'bg-blue-100 text-blue-800',
    verified: 'bg-indigo-100 text-indigo-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    draft: 'Draft',
    submitted: 'Disubmit',
    verified: 'Terverifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}

function getPaymentStatusColor(status: string): string {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    expired: 'bg-gray-100 text-gray-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getDocumentStatusColor(status: string): string {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount)
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function calculateAge(birthDate: string): number {
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  
  return age
}

function getDocumentTypeText(type: string): string {
  const types = {
    foto: 'Foto',
    ktp: 'KTP',
    kartu_keluarga: 'Kartu Keluarga',
    surat_rekomendasi: 'Surat Rekomendasi',
    ijazah: 'Ijazah',
    sertifikat: 'Sertifikat',
    lainnya: 'Lainnya'
  }
  return types[type as keyof typeof types] || type
}

function getRegistrationTypeText(type: string): string {
  const types = {
    mandiri: 'Mandiri',
    admin_daerah: 'Admin Daerah'
  }
  return types[type as keyof typeof types] || type
}
</script>

<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center space-x-4">
        <Button
          as="link"
          :href="route('admin-daerah.peserta.index')"
          variant="ghost"
          size="sm"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <Heading>Detail Peserta</Heading>
      </div>
    </template>

    <Head title="Detail Peserta" />

    <div class="space-y-6">
      <!-- Peserta Information -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>{{ peserta.nama_lengkap }}</CardTitle>
            <div class="flex space-x-2">
              <Badge :class="getStatusColor(peserta.status_peserta)">
                {{ getStatusText(peserta.status_peserta) }}
              </Badge>
              <Badge variant="outline">
                {{ getRegistrationTypeText(peserta.registration_type) }}
              </Badge>
            </div>
          </div>
          <CardDescription>{{ peserta.wilayah.nama_wilayah }}</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="space-y-4">
              <div>
                <p class="text-sm font-medium text-gray-500">NIK</p>
                <p class="font-mono">{{ peserta.nik }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</p>
                <p>{{ peserta.tempat_lahir }}, {{ formatDate(peserta.tanggal_lahir) }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500">Usia</p>
                <p>{{ calculateAge(peserta.tanggal_lahir) }} tahun</p>
              </div>
            </div>
            
            <div class="space-y-4">
              <div>
                <p class="text-sm font-medium text-gray-500">Jenis Kelamin</p>
                <p>{{ peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500">Email</p>
                <p>{{ peserta.email }}</p>
              </div>
              <div v-if="peserta.no_telepon">
                <p class="text-sm font-medium text-gray-500">No. Telepon</p>
                <p>{{ peserta.no_telepon }}</p>
              </div>
            </div>
            
            <div class="space-y-4">
              <div>
                <p class="text-sm font-medium text-gray-500">Username</p>
                <p class="font-mono">{{ peserta.user.username }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500">Tanggal Daftar</p>
                <p>{{ formatDateTime(peserta.created_at) }}</p>
              </div>
            </div>
          </div>
          
          <div v-if="peserta.alamat" class="mt-6 pt-6 border-t">
            <p class="text-sm font-medium text-gray-500 mb-2">Alamat</p>
            <p>{{ peserta.alamat }}</p>
          </div>
          
          <div v-if="peserta.keterangan" class="mt-4">
            <p class="text-sm font-medium text-gray-500 mb-2">Keterangan</p>
            <p class="text-sm">{{ peserta.keterangan }}</p>
          </div>
        </CardContent>
      </Card>

      <!-- Registrations -->
      <Card>
        <CardHeader>
          <CardTitle>Pendaftaran Lomba</CardTitle>
          <CardDescription>Daftar lomba yang diikuti peserta</CardDescription>
        </CardHeader>
        <CardContent>
          <div v-if="peserta.pendaftaran.length === 0" class="text-center py-8">
            <Icon name="clipboard-list" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Pendaftaran</h3>
            <p class="text-gray-600">Peserta belum mendaftar untuk lomba apapun.</p>
          </div>
          
          <div v-else class="space-y-4">
            <div 
              v-for="reg in peserta.pendaftaran" 
              :key="reg.id_pendaftaran"
              class="p-4 border rounded-lg"
            >
              <div class="flex items-center justify-between mb-3">
                <div>
                  <h4 class="font-medium">{{ reg.golongan.nama_golongan }}</h4>
                  <p class="text-sm text-gray-600">{{ reg.golongan.cabang_lomba.nama_cabang }}</p>
                </div>
                <Badge :class="getStatusColor(reg.status_pendaftaran)">
                  {{ getStatusText(reg.status_pendaftaran) }}
                </Badge>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p class="font-medium text-gray-500">No. Pendaftaran</p>
                  <p class="font-mono">{{ reg.nomor_pendaftaran }}</p>
                </div>
                <div>
                  <p class="font-medium text-gray-500">No. Peserta</p>
                  <p class="font-mono">{{ reg.nomor_peserta }}</p>
                </div>
                <div>
                  <p class="font-medium text-gray-500">Tahun</p>
                  <p>{{ reg.tahun_pendaftaran }}</p>
                </div>
              </div>
              
              <!-- Payment Info -->
              <div v-if="reg.pembayaran" class="mt-4 p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">Pembayaran</span>
                  <Badge :class="getPaymentStatusColor(reg.pembayaran.status_pembayaran)">
                    {{ reg.pembayaran.status_pembayaran }}
                  </Badge>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p class="text-gray-500">No. Transaksi</p>
                    <p class="font-mono">{{ reg.pembayaran.nomor_transaksi }}</p>
                  </div>
                  <div>
                    <p class="text-gray-500">Jumlah</p>
                    <p class="font-medium">{{ formatCurrency(reg.pembayaran.jumlah_bayar) }}</p>
                  </div>
                </div>
                <div v-if="reg.pembayaran.tanggal_bayar" class="mt-2 text-xs text-gray-600">
                  Dibayar: {{ formatDateTime(reg.pembayaran.tanggal_bayar) }}
                </div>
              </div>
              
              <!-- Documents -->
              <div v-if="reg.dokumen_peserta.length > 0" class="mt-4">
                <p class="text-sm font-medium mb-2">Dokumen ({{ reg.dokumen_peserta.length }})</p>
                <div class="flex flex-wrap gap-2">
                  <Badge 
                    v-for="doc in reg.dokumen_peserta" 
                    :key="doc.jenis_dokumen"
                    :class="getDocumentStatusColor(doc.status_verifikasi)"
                    variant="outline"
                    class="text-xs"
                  >
                    {{ getDocumentTypeText(doc.jenis_dokumen) }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <Card>
        <CardContent class="p-6">
          <div class="flex flex-wrap gap-4">
            <Button 
              as="link" 
              :href="route('admin-daerah.peserta.edit', peserta.id_peserta)"
            >
              <Icon name="edit" class="w-4 h-4 mr-2" />
              Edit Peserta
            </Button>
            
            <Button 
              as="link" 
              :href="route('admin-daerah.pendaftaran.create', { peserta: peserta.id_peserta })"
              variant="outline"
            >
              <Icon name="plus" class="w-4 h-4 mr-2" />
              Daftarkan ke Lomba
            </Button>
            
            <Button 
              variant="outline"
              @click="window.print()"
            >
              <Icon name="printer" class="w-4 h-4 mr-2" />
              Cetak Data
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

{"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourcesContent": ["import {\n  TableOptions,\n  createTable,\n  RowData,\n  TableOptionsResolved,\n} from '@tanstack/table-core'\nimport {\n  h,\n  watchEffect,\n  ref,\n  defineComponent,\n  isRef,\n  unref,\n  MaybeRef,\n  watch,\n  shallowRef,\n} from 'vue'\nimport { mergeProxy } from './merge-proxy'\n\nexport * from '@tanstack/table-core'\n\nexport type TableOptionsWithReactiveData<TData extends RowData> = Omit<\n  TableOptions<TData>,\n  'data'\n> & {\n  data: MaybeRef<TData[]>\n}\n\nexport const FlexRender = defineComponent({\n  props: ['render', 'props'],\n  setup: (props: { render: any; props: any }) => {\n    return () => {\n      if (\n        typeof props.render === 'function' ||\n        typeof props.render === 'object'\n      ) {\n        return h(props.render, props.props)\n      }\n\n      return props.render\n    }\n  },\n})\n\nfunction getOptionsWithReactiveData<TData extends RowData>(\n  options: TableOptionsWithReactiveData<TData>\n) {\n  return mergeProxy(options, {\n    data: unref(options.data),\n  })\n}\n\nexport function useVueTable<TData extends RowData>(\n  initialOptions: TableOptionsWithReactiveData<TData>\n) {\n  const IS_REACTIVE = isRef(initialOptions.data)\n\n  const resolvedOptions = mergeProxy(\n    {\n      state: {}, // Dummy state\n      onStateChange: () => {}, // noop\n      renderFallbackValue: null,\n      mergeOptions(\n        defaultOptions: TableOptions<TData>,\n        options: TableOptions<TData>\n      ) {\n        return IS_REACTIVE\n          ? {\n              ...defaultOptions,\n              ...options,\n            }\n          : mergeProxy(defaultOptions, options)\n      },\n    },\n    IS_REACTIVE ? getOptionsWithReactiveData(initialOptions) : initialOptions\n  )\n\n  const table = createTable<TData>(\n    resolvedOptions as TableOptionsResolved<TData>\n  )\n\n  // Add reactivity support\n  if (IS_REACTIVE) {\n    const dataRef = shallowRef(initialOptions.data)\n    watch(\n      dataRef,\n      () => {\n        table.setState(prev => ({\n          ...prev,\n          data: dataRef.value,\n        }))\n      },\n      { immediate: true }\n    )\n  }\n\n  // can't use `reactive` because update needs to be immutable\n  const state = ref(table.initialState)\n\n  watchEffect(() => {\n    table.setOptions(prev => {\n      const stateProxy = new Proxy({} as typeof state.value, {\n        get: (_, prop) => state.value[prop as keyof typeof state.value],\n      })\n\n      return mergeProxy(\n        prev,\n        IS_REACTIVE\n          ? getOptionsWithReactiveData(initialOptions)\n          : initialOptions,\n        {\n          // merge the initialState and `options.state`\n          // create a new proxy on each `setOptions` call\n          // and get the value from state on each property access\n          state: mergeProxy(stateProxy, initialOptions.state ?? {}),\n          // Similarly, we'll maintain both our internal state and any user-provided\n          // state.\n          onStateChange: (updater: any) => {\n            if (updater instanceof Function) {\n              state.value = updater(state.value)\n            } else {\n              state.value = updater\n            }\n\n            initialOptions.onStateChange?.(updater)\n          },\n        }\n      )\n    })\n  })\n\n  return table\n}\n"], "names": ["FlexRender", "defineComponent", "props", "setup", "render", "h", "getOptionsWithReactiveData", "options", "mergeProxy", "data", "unref", "useVueTable", "initialOptions", "IS_REACTIVE", "isRef", "resolvedOptions", "state", "onStateChange", "renderFallbackValue", "mergeOptions", "defaultOptions", "table", "createTable", "dataRef", "shallowRef", "watch", "setState", "prev", "value", "immediate", "ref", "initialState", "watchEffect", "setOptions", "_initialOptions$state", "stateProxy", "Proxy", "get", "_", "prop", "updater", "Function"], "mappings": ";;;;;;;;;;;;;;;;AA4BaA,MAAAA,UAAU,GAAGC,mBAAe,CAAC;AACxCC,EAAAA,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;EAC1BC,KAAK,EAAGD,KAAkC,IAAK;AAC7C,IAAA,OAAO,MAAM;AACX,MAAA,IACE,OAAOA,KAAK,CAACE,MAAM,KAAK,UAAU,IAClC,OAAOF,KAAK,CAACE,MAAM,KAAK,QAAQ,EAChC;QACA,OAAOC,KAAC,CAACH,KAAK,CAACE,MAAM,EAAEF,KAAK,CAACA,KAAK,CAAC,CAAA;AACrC,OAAA;MAEA,OAAOA,KAAK,CAACE,MAAM,CAAA;KACpB,CAAA;AACH,GAAA;AACF,CAAC,EAAC;AAEF,SAASE,0BAA0BA,CACjCC,OAA4C,EAC5C;EACA,OAAOC,qBAAU,CAACD,OAAO,EAAE;AACzBE,IAAAA,IAAI,EAAEC,SAAK,CAACH,OAAO,CAACE,IAAI,CAAA;AAC1B,GAAC,CAAC,CAAA;AACJ,CAAA;AAEO,SAASE,WAAWA,CACzBC,cAAmD,EACnD;AACA,EAAA,MAAMC,WAAW,GAAGC,SAAK,CAACF,cAAc,CAACH,IAAI,CAAC,CAAA;EAE9C,MAAMM,eAAe,GAAGP,qBAAU,CAChC;IACEQ,KAAK,EAAE,EAAE;AAAE;AACXC,IAAAA,aAAa,EAAEA,MAAM,EAAE;AAAE;AACzBC,IAAAA,mBAAmB,EAAE,IAAI;AACzBC,IAAAA,YAAYA,CACVC,cAAmC,EACnCb,OAA4B,EAC5B;AACA,MAAA,OAAOM,WAAW,GACd;AACE,QAAA,GAAGO,cAAc;QACjB,GAAGb,OAAAA;AACL,OAAC,GACDC,qBAAU,CAACY,cAAc,EAAEb,OAAO,CAAC,CAAA;AACzC,KAAA;GACD,EACDM,WAAW,GAAGP,0BAA0B,CAACM,cAAc,CAAC,GAAGA,cAC7D,CAAC,CAAA;AAED,EAAA,MAAMS,KAAK,GAAGC,qBAAW,CACvBP,eACF,CAAC,CAAA;;AAED;AACA,EAAA,IAAIF,WAAW,EAAE;AACf,IAAA,MAAMU,OAAO,GAAGC,cAAU,CAACZ,cAAc,CAACH,IAAI,CAAC,CAAA;IAC/CgB,SAAK,CACHF,OAAO,EACP,MAAM;AACJF,MAAAA,KAAK,CAACK,QAAQ,CAACC,IAAI,KAAK;AACtB,QAAA,GAAGA,IAAI;QACPlB,IAAI,EAAEc,OAAO,CAACK,KAAAA;AAChB,OAAC,CAAC,CAAC,CAAA;AACL,KAAC,EACD;AAAEC,MAAAA,SAAS,EAAE,IAAA;AAAK,KACpB,CAAC,CAAA;AACH,GAAA;;AAEA;AACA,EAAA,MAAMb,KAAK,GAAGc,OAAG,CAACT,KAAK,CAACU,YAAY,CAAC,CAAA;AAErCC,EAAAA,eAAW,CAAC,MAAM;AAChBX,IAAAA,KAAK,CAACY,UAAU,CAACN,IAAI,IAAI;AAAA,MAAA,IAAAO,qBAAA,CAAA;AACvB,MAAA,MAAMC,UAAU,GAAG,IAAIC,KAAK,CAAC,EAAE,EAAwB;QACrDC,GAAG,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAKvB,KAAK,CAACY,KAAK,CAACW,IAAI,CAAA;AACpC,OAAC,CAAC,CAAA;AAEF,MAAA,OAAO/B,qBAAU,CACfmB,IAAI,EACJd,WAAW,GACPP,0BAA0B,CAACM,cAAc,CAAC,GAC1CA,cAAc,EAClB;AACE;AACA;AACA;AACAI,QAAAA,KAAK,EAAER,qBAAU,CAAC2B,UAAU,GAAAD,qBAAA,GAAEtB,cAAc,CAACI,KAAK,KAAAkB,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAC;AACzD;AACA;QACAjB,aAAa,EAAGuB,OAAY,IAAK;UAC/B,IAAIA,OAAO,YAAYC,QAAQ,EAAE;YAC/BzB,KAAK,CAACY,KAAK,GAAGY,OAAO,CAACxB,KAAK,CAACY,KAAK,CAAC,CAAA;AACpC,WAAC,MAAM;YACLZ,KAAK,CAACY,KAAK,GAAGY,OAAO,CAAA;AACvB,WAAA;UAEA5B,cAAc,CAACK,aAAa,IAA5BL,IAAAA,IAAAA,cAAc,CAACK,aAAa,CAAGuB,OAAO,CAAC,CAAA;AACzC,SAAA;AACF,OACF,CAAC,CAAA;AACH,KAAC,CAAC,CAAA;AACJ,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOnB,KAAK,CAAA;AACd;;;;;;;;;;;"}
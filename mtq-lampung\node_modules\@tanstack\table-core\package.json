{"name": "@tanstack/table-core", "version": "8.21.3", "description": "Headless UI for building powerful tables & datagrids for TS/JS.", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/table.git", "directory": "packages/table-core"}, "homepage": "https://tanstack.com/table", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "vue", "solid", "table", "table-core", "datagrid"], "type": "commonjs", "module": "build/lib/index.esm.js", "main": "build/lib/index.js", "types": "build/lib/index.d.ts", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./package.json": "./package.json"}, "sideEffects": false, "engines": {"node": ">=12"}, "files": ["build/lib/*", "build/umd/*", "src"], "scripts": {}}